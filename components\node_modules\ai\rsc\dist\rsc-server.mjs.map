{"version": 3, "sources": ["../ai-state.tsx", "../utils.tsx", "../streamable.tsx", "../../core/util/retry-with-exponential-backoff.ts", "../../core/util/delay.ts", "../../core/prompt/convert-to-language-model-prompt.ts", "../../core/util/detect-image-mimetype.ts", "../../core/util/download.ts", "../../core/prompt/data-content.ts", "../../core/prompt/invalid-message-role-error.ts", "../../core/prompt/get-validated-prompt.ts", "../../core/prompt/prepare-call-settings.ts", "../../core/types/token-usage.ts", "../../core/util/schema.ts", "../../core/util/is-non-empty-object.ts", "../../core/prompt/prepare-tools-and-tool-choice.ts", "../../streams/ai-stream.ts", "../../streams/stream-data.ts", "../../streams/openai-stream.ts", "../constants.ts", "../stream-ui/stream-ui.tsx", "../provider.tsx"], "sourcesContent": ["import { AsyncLocalStorage } from 'async_hooks';\nimport * as jsondiffpatch from 'jsondiffpatch';\nimport { createResolvablePromise, isFunction } from './utils';\nimport type {\n  AIProvider,\n  InternalAIStateStorageOptions,\n  InferAIState,\n  MutableAIState,\n  ValueOrUpdater,\n} from './types';\n\n// It is possible that multiple AI requests get in concurrently, for different\n// AI instances. So ALS is necessary here for a simpler API.\nconst asyncAIStateStorage = new AsyncLocalStorage<{\n  currentState: any;\n  originalState: any;\n  sealed: boolean;\n  options: InternalAIStateStorageOptions;\n  mutationDeltaPromise?: Promise<any>;\n  mutationDeltaResolve?: (v: any) => void;\n}>();\n\nfunction getAIStateStoreOrThrow(message: string) {\n  const store = asyncAIStateStorage.getStore();\n  if (!store) {\n    throw new Error(message);\n  }\n  return store;\n}\n\nexport function withAIState<S, T>(\n  { state, options }: { state: S; options: InternalAIStateStorageOptions },\n  fn: () => T,\n): T {\n  return asyncAIStateStorage.run(\n    {\n      currentState: state,\n      originalState: state,\n      sealed: false,\n      options,\n    },\n    fn,\n  );\n}\n\nexport function getAIStateDeltaPromise() {\n  const store = getAIStateStoreOrThrow('Internal error occurred.');\n  return store.mutationDeltaPromise;\n}\n\n// Internal method. This will be called after the AI Action has been returned\n// and you can no longer call `getMutableAIState()` inside any async callbacks\n// created by that Action.\nexport function sealMutableAIState() {\n  const store = getAIStateStoreOrThrow('Internal error occurred.');\n  store.sealed = true;\n}\n\n/**\n * Get the current AI state.\n * If `key` is provided, it will return the value of the specified key in the\n * AI state, if it's an object. If it's not an object, it will throw an error.\n *\n * @example const state = getAIState() // Get the entire AI state\n * @example const field = getAIState('key') // Get the value of the key\n */\nfunction getAIState<AI extends AIProvider = any>(): Readonly<\n  InferAIState<AI, any>\n>;\nfunction getAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): Readonly<InferAIState<AI, any>[typeof key]>;\nfunction getAIState<AI extends AIProvider = any>(\n  ...args: [] | [key: keyof InferAIState<AI, any>]\n) {\n  const store = getAIStateStoreOrThrow(\n    '`getAIState` must be called within an AI Action.',\n  );\n\n  if (args.length > 0) {\n    const key = args[0];\n    if (typeof store.currentState !== 'object') {\n      throw new Error(\n        `You can't get the \"${String(\n          key,\n        )}\" field from the AI state because it's not an object.`,\n      );\n    }\n    return store.currentState[key as keyof typeof store.currentState];\n  }\n\n  return store.currentState;\n}\n\n/**\n * Get the mutable AI state. Note that you must call `.done()` when finishing\n * updating the AI state.\n *\n * @example\n * ```tsx\n * const state = getMutableAIState()\n * state.update({ ...state.get(), key: 'value' })\n * state.update((currentState) => ({ ...currentState, key: 'value' }))\n * state.done()\n * ```\n *\n * @example\n * ```tsx\n * const state = getMutableAIState()\n * state.done({ ...state.get(), key: 'value' }) // Done with a new state\n * ```\n */\nfunction getMutableAIState<AI extends AIProvider = any>(): MutableAIState<\n  InferAIState<AI, any>\n>;\nfunction getMutableAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): MutableAIState<InferAIState<AI, any>[typeof key]>;\nfunction getMutableAIState<AI extends AIProvider = any>(\n  ...args: [] | [key: keyof InferAIState<AI, any>]\n) {\n  type AIState = InferAIState<AI, any>;\n  type AIStateWithKey = typeof args extends [key: keyof AIState]\n    ? AIState[(typeof args)[0]]\n    : AIState;\n  type NewStateOrUpdater = ValueOrUpdater<AIStateWithKey>;\n\n  const store = getAIStateStoreOrThrow(\n    '`getMutableAIState` must be called within an AI Action.',\n  );\n\n  if (store.sealed) {\n    throw new Error(\n      \"`getMutableAIState` must be called before returning from an AI Action. Please move it to the top level of the Action's function body.\",\n    );\n  }\n\n  if (!store.mutationDeltaPromise) {\n    const { promise, resolve } = createResolvablePromise();\n    store.mutationDeltaPromise = promise;\n    store.mutationDeltaResolve = resolve;\n  }\n\n  function doUpdate(newState: NewStateOrUpdater, done: boolean) {\n    if (args.length > 0) {\n      if (typeof store.currentState !== 'object') {\n        const key = args[0];\n        throw new Error(\n          `You can't modify the \"${String(\n            key,\n          )}\" field of the AI state because it's not an object.`,\n        );\n      }\n    }\n\n    if (isFunction(newState)) {\n      if (args.length > 0) {\n        store.currentState[args[0]] = newState(store.currentState[args[0]]);\n      } else {\n        store.currentState = newState(store.currentState);\n      }\n    } else {\n      if (args.length > 0) {\n        store.currentState[args[0]] = newState;\n      } else {\n        store.currentState = newState;\n      }\n    }\n\n    store.options.onSetAIState?.({\n      key: args.length > 0 ? args[0] : undefined,\n      state: store.currentState,\n      done,\n    });\n  }\n\n  const mutableState = {\n    get: () => {\n      if (args.length > 0) {\n        const key = args[0];\n        if (typeof store.currentState !== 'object') {\n          throw new Error(\n            `You can't get the \"${String(\n              key,\n            )}\" field from the AI state because it's not an object.`,\n          );\n        }\n        return store.currentState[key] as Readonly<AIStateWithKey>;\n      }\n\n      return store.currentState as Readonly<AIState>;\n    },\n    update: function update(newAIState: NewStateOrUpdater) {\n      doUpdate(newAIState, false);\n    },\n    done: function done(...doneArgs: [] | [NewStateOrUpdater]) {\n      if (doneArgs.length > 0) {\n        doUpdate(doneArgs[0] as NewStateOrUpdater, true);\n      }\n\n      const delta = jsondiffpatch.diff(store.originalState, store.currentState);\n      store.mutationDeltaResolve!(delta);\n    },\n  };\n\n  return mutableState;\n}\n\nexport { getAIState, getMutableAIState };\n", "import React, { Suspense } from 'react';\n\nexport function createResolvablePromise<T = any>() {\n  let resolve: (value: T) => void, reject: (error: unknown) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  };\n}\n\n// Use the name `R` for `Row` as it will be shorter in the RSC payload.\nconst R = [\n  (async ({\n    c, // current\n    n, // next\n  }: {\n    c: React.ReactNode;\n    n: Promise<any>;\n  }) => {\n    const chunk = await n;\n    if (chunk.done) {\n      return chunk.value;\n    }\n\n    if (chunk.append) {\n      return (\n        <>\n          {c}\n          <Suspense fallback={chunk.value}>\n            <R c={chunk.value} n={chunk.next} />\n          </Suspense>\n        </>\n      );\n    }\n\n    return (\n      <Suspense fallback={chunk.value}>\n        <R c={chunk.value} n={chunk.next} />\n      </Suspense>\n    );\n  }) as unknown as React.FC<{\n    c: React.ReactNode;\n    n: Promise<any>;\n  }>,\n][0];\n\nexport function createSuspensedChunk(initialValue: React.ReactNode) {\n  const { promise, resolve, reject } = createResolvablePromise();\n\n  return {\n    row: (\n      <Suspense fallback={initialValue}>\n        <R c={initialValue} n={promise} />\n      </Suspense>\n    ) as React.ReactNode,\n    resolve,\n    reject,\n  };\n}\n\nexport const isFunction = (x: unknown): x is Function =>\n  typeof x === 'function';\n\nexport const consumeStream = async (stream: ReadableStream) => {\n  const reader = stream.getReader();\n  while (true) {\n    const { done } = await reader.read();\n    if (done) break;\n  }\n};\n", "import type { ReactNode } from 'react';\nimport type <PERSON><PERSON><PERSON> from 'openai';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\n\n// TODO: This needs to be externalized.\nimport { OpenAIStream } from '../streams';\n\nimport {\n  STREAMABLE_VALUE_TYPE,\n  DEV_DEFAULT_STREAMABLE_WARNING_TIME,\n} from './constants';\nimport {\n  createResolvablePromise,\n  createSuspensedChunk,\n  consumeStream,\n} from './utils';\nimport type { StreamablePatch, StreamableValue } from './types';\n\n// It's necessary to define the type manually here, otherwise TypeScript compiler\n// will not be able to infer the correct return type as it's circular.\ntype StreamableUIWrapper = {\n  /**\n   * The value of the streamable UI. This can be returned from a Server Action and received by the client.\n   */\n  readonly value: React.ReactNode;\n\n  /**\n   * This method updates the current UI node. It takes a new UI node and replaces the old one.\n   */\n  update(value: React.ReactNode): StreamableUIWrapper;\n\n  /**\n   * This method is used to append a new UI node to the end of the old one.\n   * Once appended a new UI node, the previous UI node cannot be updated anymore.\n   *\n   * @example\n   * ```jsx\n   * const ui = createStreamableUI(<div>hello</div>)\n   * ui.append(<div>world</div>)\n   *\n   * // The UI node will be:\n   * // <>\n   * //   <div>hello</div>\n   * //   <div>world</div>\n   * // </>\n   * ```\n   */\n  append(value: React.ReactNode): StreamableUIWrapper;\n\n  /**\n   * This method is used to signal that there is an error in the UI stream.\n   * It will be thrown on the client side and caught by the nearest error boundary component.\n   */\n  error(error: any): StreamableUIWrapper;\n\n  /**\n   * This method marks the UI node as finalized. You can either call it without any parameters or with a new UI node as the final state.\n   * Once called, the UI node cannot be updated or appended anymore.\n   *\n   * This method is always **required** to be called, otherwise the response will be stuck in a loading state.\n   */\n  done(...args: [React.ReactNode] | []): StreamableUIWrapper;\n};\n\n/**\n * Create a piece of changable UI that can be streamed to the client.\n * On the client side, it can be rendered as a normal React node.\n */\nfunction createStreamableUI(initialValue?: React.ReactNode) {\n  let currentValue = initialValue;\n  let closed = false;\n  let { row, resolve, reject } = createSuspensedChunk(initialValue);\n\n  function assertStream(method: string) {\n    if (closed) {\n      throw new Error(method + ': UI stream is already closed.');\n    }\n  }\n\n  let warningTimeout: NodeJS.Timeout | undefined;\n  function warnUnclosedStream() {\n    if (process.env.NODE_ENV === 'development') {\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      warningTimeout = setTimeout(() => {\n        console.warn(\n          'The streamable UI has been slow to update. This may be a bug or a performance issue or you forgot to call `.done()`.',\n        );\n      }, DEV_DEFAULT_STREAMABLE_WARNING_TIME);\n    }\n  }\n  warnUnclosedStream();\n\n  const streamable: StreamableUIWrapper = {\n    value: row,\n    update(value: React.ReactNode) {\n      assertStream('.update()');\n\n      // There is no need to update the value if it's referentially equal.\n      if (value === currentValue) {\n        warnUnclosedStream();\n        return streamable;\n      }\n\n      const resolvable = createResolvablePromise();\n      currentValue = value;\n\n      resolve({ value: currentValue, done: false, next: resolvable.promise });\n      resolve = resolvable.resolve;\n      reject = resolvable.reject;\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    append(value: React.ReactNode) {\n      assertStream('.append()');\n\n      const resolvable = createResolvablePromise();\n      currentValue = value;\n\n      resolve({ value, done: false, append: true, next: resolvable.promise });\n      resolve = resolvable.resolve;\n      reject = resolvable.reject;\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    error(error: any) {\n      assertStream('.error()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      reject(error);\n\n      return streamable;\n    },\n    done(...args: [] | [React.ReactNode]) {\n      assertStream('.done()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      if (args.length) {\n        resolve({ value: args[0], done: true });\n        return streamable;\n      }\n      resolve({ value: currentValue, done: true });\n\n      return streamable;\n    },\n  };\n\n  return streamable;\n}\n\nconst STREAMABLE_VALUE_INTERNAL_LOCK = Symbol('streamable.value.lock');\n\n/**\n * Create a wrapped, changable value that can be streamed to the client.\n * On the client side, the value can be accessed via the readStreamableValue() API.\n */\nfunction createStreamableValue<T = any, E = any>(\n  initialValue?: T | ReadableStream<T>,\n) {\n  const isReadableStream =\n    initialValue instanceof ReadableStream ||\n    (typeof initialValue === 'object' &&\n      initialValue !== null &&\n      'getReader' in initialValue &&\n      typeof initialValue.getReader === 'function' &&\n      'locked' in initialValue &&\n      typeof initialValue.locked === 'boolean');\n\n  if (!isReadableStream) {\n    return createStreamableValueImpl<T, E>(initialValue);\n  }\n\n  const streamableValue = createStreamableValueImpl<T, E>();\n\n  // Since the streamable value will be from a readable stream, it's not allowed\n  // to update the value manually as that introduces race conditions and\n  // unexpected behavior.\n  // We lock the value to prevent any updates from the user.\n  streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = true;\n\n  (async () => {\n    try {\n      // Consume the readable stream and update the value.\n      const reader = initialValue.getReader();\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) {\n          break;\n        }\n\n        // Unlock the value to allow updates.\n        streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n        if (typeof value === 'string') {\n          streamableValue.append(value);\n        } else {\n          streamableValue.update(value);\n        }\n        // Lock the value again.\n        streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = true;\n      }\n\n      streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n      streamableValue.done();\n    } catch (e) {\n      streamableValue[STREAMABLE_VALUE_INTERNAL_LOCK] = false;\n      streamableValue.error(e);\n    }\n  })();\n\n  return streamableValue;\n}\n\n// It's necessary to define the type manually here, otherwise TypeScript compiler\n// will not be able to infer the correct return type as it's circular.\ntype StreamableValueWrapper<T, E> = {\n  /**\n   * The value of the streamable. This can be returned from a Server Action and\n   * received by the client. To read the streamed values, use the\n   * `readStreamableValue` or `useStreamableValue` APIs.\n   */\n  readonly value: StreamableValue<T, E>;\n\n  /**\n   * This method updates the current value with a new one.\n   */\n  update(value: T): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method is used to append a delta string to the current value. It\n   * requires the current value of the streamable to be a string.\n   *\n   * @example\n   * ```jsx\n   * const streamable = createStreamableValue('hello');\n   * streamable.append(' world');\n   *\n   * // The value will be 'hello world'\n   * ```\n   */\n  append(value: T): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method is used to signal that there is an error in the value stream.\n   * It will be thrown on the client side when consumed via\n   * `readStreamableValue` or `useStreamableValue`.\n   */\n  error(error: any): StreamableValueWrapper<T, E>;\n\n  /**\n   * This method marks the value as finalized. You can either call it without\n   * any parameters or with a new value as the final state.\n   * Once called, the value cannot be updated or appended anymore.\n   *\n   * This method is always **required** to be called, otherwise the response\n   * will be stuck in a loading state.\n   */\n  done(...args: [T] | []): StreamableValueWrapper<T, E>;\n\n  /**\n   * @internal This is an internal lock to prevent the value from being\n   * updated by the user.\n   */\n  [STREAMABLE_VALUE_INTERNAL_LOCK]: boolean;\n};\n\nfunction createStreamableValueImpl<T = any, E = any>(initialValue?: T) {\n  let closed = false;\n  let locked = false;\n  let resolvable = createResolvablePromise<StreamableValue<T, E>>();\n\n  let currentValue = initialValue;\n  let currentError: E | undefined;\n  let currentPromise: typeof resolvable.promise | undefined =\n    resolvable.promise;\n  let currentPatchValue: StreamablePatch;\n\n  function assertStream(method: string) {\n    if (closed) {\n      throw new Error(method + ': Value stream is already closed.');\n    }\n    if (locked) {\n      throw new Error(\n        method + ': Value stream is locked and cannot be updated.',\n      );\n    }\n  }\n\n  let warningTimeout: NodeJS.Timeout | undefined;\n  function warnUnclosedStream() {\n    if (process.env.NODE_ENV === 'development') {\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      warningTimeout = setTimeout(() => {\n        console.warn(\n          'The streamable value has been slow to update. This may be a bug or a performance issue or you forgot to call `.done()`.',\n        );\n      }, DEV_DEFAULT_STREAMABLE_WARNING_TIME);\n    }\n  }\n  warnUnclosedStream();\n\n  function createWrapped(initialChunk?: boolean): StreamableValue<T, E> {\n    // This makes the payload much smaller if there're mutative updates before the first read.\n    let init: Partial<StreamableValue<T, E>>;\n\n    if (currentError !== undefined) {\n      init = { error: currentError };\n    } else {\n      if (currentPatchValue && !initialChunk) {\n        init = { diff: currentPatchValue };\n      } else {\n        init = { curr: currentValue };\n      }\n    }\n\n    if (currentPromise) {\n      init.next = currentPromise;\n    }\n\n    if (initialChunk) {\n      init.type = STREAMABLE_VALUE_TYPE;\n    }\n\n    return init;\n  }\n\n  // Update the internal `currentValue` and `currentPatchValue` if needed.\n  function updateValueStates(value: T) {\n    // If we can only send a patch over the wire, it's better to do so.\n    currentPatchValue = undefined;\n    if (typeof value === 'string') {\n      if (typeof currentValue === 'string') {\n        if (value.startsWith(currentValue)) {\n          currentPatchValue = [0, value.slice(currentValue.length)];\n        }\n      }\n    }\n\n    currentValue = value;\n  }\n\n  const streamable: StreamableValueWrapper<T, E> = {\n    set [STREAMABLE_VALUE_INTERNAL_LOCK](state: boolean) {\n      locked = state;\n    },\n    get value() {\n      return createWrapped(true);\n    },\n    update(value: T) {\n      assertStream('.update()');\n\n      const resolvePrevious = resolvable.resolve;\n      resolvable = createResolvablePromise();\n\n      updateValueStates(value);\n      currentPromise = resolvable.promise;\n      resolvePrevious(createWrapped());\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    append(value: T) {\n      assertStream('.append()');\n\n      if (\n        typeof currentValue !== 'string' &&\n        typeof currentValue !== 'undefined'\n      ) {\n        throw new Error(\n          `.append(): The current value is not a string. Received: ${typeof currentValue}`,\n        );\n      }\n      if (typeof value !== 'string') {\n        throw new Error(\n          `.append(): The value is not a string. Received: ${typeof value}`,\n        );\n      }\n\n      const resolvePrevious = resolvable.resolve;\n      resolvable = createResolvablePromise();\n\n      if (typeof currentValue === 'string') {\n        currentPatchValue = [0, value];\n        (currentValue as string) = currentValue + value;\n      } else {\n        currentPatchValue = undefined;\n        currentValue = value;\n      }\n\n      currentPromise = resolvable.promise;\n      resolvePrevious(createWrapped());\n\n      warnUnclosedStream();\n\n      return streamable;\n    },\n    error(error: any) {\n      assertStream('.error()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      currentError = error;\n      currentPromise = undefined;\n\n      resolvable.resolve({ error });\n\n      return streamable;\n    },\n    done(...args: [] | [T]) {\n      assertStream('.done()');\n\n      if (warningTimeout) {\n        clearTimeout(warningTimeout);\n      }\n      closed = true;\n      currentPromise = undefined;\n\n      if (args.length) {\n        updateValueStates(args[0]);\n        resolvable.resolve(createWrapped());\n        return streamable;\n      }\n\n      resolvable.resolve({});\n\n      return streamable;\n    },\n  };\n\n  return streamable;\n}\n\nexport { createStreamableUI, createStreamableValue };\n\ntype Streamable = ReactNode | Promise<ReactNode>;\ntype Renderer<T> = (\n  props: T,\n) =>\n  | Streamable\n  | Generator<Streamable, Streamable, void>\n  | AsyncGenerator<Streamable, Streamable, void>;\n\n/**\n * `render` is a helper function to create a streamable UI from some LLMs.\n * This API only supports OpenAI's GPT models with Function Calling and Assistants Tools,\n * please use `streamUI` for compatibility with other providers.\n *\n * @deprecated It's recommended to use the `streamUI` API for compatibility with AI SDK Core APIs\n * and future features. This API will be removed in a future release.\n */\nexport function render<\n  TS extends {\n    [name: string]: z.Schema;\n  } = {},\n  FS extends {\n    [name: string]: z.Schema;\n  } = {},\n>(options: {\n  /**\n   * The model name to use. Must be OpenAI SDK compatible. Tools and Functions are only supported\n   * GPT models (3.5/4), OpenAI Assistants, Mistral small and large, and Fireworks firefunction-v1.\n   *\n   * @example \"gpt-3.5-turbo\"\n   */\n  model: string;\n  /**\n   * The provider instance to use. Currently the only provider available is OpenAI.\n   * This needs to match the model name.\n   */\n  provider: OpenAI;\n  messages: Parameters<\n    typeof OpenAI.prototype.chat.completions.create\n  >[0]['messages'];\n  text?: Renderer<{\n    /**\n     * The full text content from the model so far.\n     */\n    content: string;\n    /**\n     * The new appended text content from the model since the last `text` call.\n     */\n    delta: string;\n    /**\n     * Whether the model is done generating text.\n     * If `true`, the `content` will be the final output and this call will be the last.\n     */\n    done: boolean;\n  }>;\n  tools?: {\n    [name in keyof TS]: {\n      description?: string;\n      parameters: TS[name];\n      render: Renderer<z.infer<TS[name]>>;\n    };\n  };\n  functions?: {\n    [name in keyof FS]: {\n      description?: string;\n      parameters: FS[name];\n      render: Renderer<z.infer<FS[name]>>;\n    };\n  };\n  initial?: ReactNode;\n  temperature?: number;\n}): ReactNode {\n  const ui = createStreamableUI(options.initial);\n\n  // The default text renderer just returns the content as string.\n  const text = options.text\n    ? options.text\n    : ({ content }: { content: string }) => content;\n\n  const functions = options.functions\n    ? Object.entries(options.functions).map(\n        ([name, { description, parameters }]) => {\n          return {\n            name,\n            description,\n            parameters: zodToJsonSchema(parameters) as Record<string, unknown>,\n          };\n        },\n      )\n    : undefined;\n\n  const tools = options.tools\n    ? Object.entries(options.tools).map(\n        ([name, { description, parameters }]) => {\n          return {\n            type: 'function' as const,\n            function: {\n              name,\n              description,\n              parameters: zodToJsonSchema(parameters) as Record<\n                string,\n                unknown\n              >,\n            },\n          };\n        },\n      )\n    : undefined;\n\n  if (functions && tools) {\n    throw new Error(\n      \"You can't have both functions and tools defined. Please choose one or the other.\",\n    );\n  }\n\n  let finished: Promise<void> | undefined;\n\n  async function handleRender(\n    args: any,\n    renderer: undefined | Renderer<any>,\n    res: ReturnType<typeof createStreamableUI>,\n  ) {\n    if (!renderer) return;\n\n    const resolvable = createResolvablePromise<void>();\n\n    if (finished) {\n      finished = finished.then(() => resolvable.promise);\n    } else {\n      finished = resolvable.promise;\n    }\n\n    const value = renderer(args);\n    if (\n      value instanceof Promise ||\n      (value &&\n        typeof value === 'object' &&\n        'then' in value &&\n        typeof value.then === 'function')\n    ) {\n      const node = await (value as Promise<React.ReactNode>);\n      res.update(node);\n      resolvable.resolve(void 0);\n    } else if (\n      value &&\n      typeof value === 'object' &&\n      Symbol.asyncIterator in value\n    ) {\n      const it = value as AsyncGenerator<\n        React.ReactNode,\n        React.ReactNode,\n        void\n      >;\n      while (true) {\n        const { done, value } = await it.next();\n        res.update(value);\n        if (done) break;\n      }\n      resolvable.resolve(void 0);\n    } else if (value && typeof value === 'object' && Symbol.iterator in value) {\n      const it = value as Generator<React.ReactNode, React.ReactNode, void>;\n      while (true) {\n        const { done, value } = it.next();\n        res.update(value);\n        if (done) break;\n      }\n      resolvable.resolve(void 0);\n    } else {\n      res.update(value);\n      resolvable.resolve(void 0);\n    }\n  }\n\n  (async () => {\n    let hasFunction = false;\n    let content = '';\n\n    consumeStream(\n      OpenAIStream(\n        (await options.provider.chat.completions.create({\n          model: options.model,\n          messages: options.messages,\n          temperature: options.temperature,\n          stream: true,\n          ...(functions\n            ? {\n                functions,\n              }\n            : {}),\n          ...(tools\n            ? {\n                tools,\n              }\n            : {}),\n        })) as any,\n        {\n          ...(functions\n            ? {\n                async experimental_onFunctionCall(functionCallPayload) {\n                  hasFunction = true;\n                  handleRender(\n                    functionCallPayload.arguments,\n                    options.functions?.[functionCallPayload.name as any]\n                      ?.render,\n                    ui,\n                  );\n                },\n              }\n            : {}),\n          ...(tools\n            ? {\n                async experimental_onToolCall(toolCallPayload: any) {\n                  hasFunction = true;\n\n                  // TODO: We might need Promise.all here?\n                  for (const tool of toolCallPayload.tools) {\n                    handleRender(\n                      tool.func.arguments,\n                      options.tools?.[tool.func.name as any]?.render,\n                      ui,\n                    );\n                  }\n                },\n              }\n            : {}),\n          onText(chunk) {\n            content += chunk;\n            handleRender({ content, done: false, delta: chunk }, text, ui);\n          },\n          async onFinal() {\n            if (hasFunction) {\n              await finished;\n              ui.done();\n              return;\n            }\n\n            handleRender({ content, done: true }, text, ui);\n            await finished;\n            ui.done();\n          },\n        },\n      ),\n    );\n  })();\n\n  return ui.value;\n}\n", "import { APICallError, RetryError } from '@ai-sdk/provider';\nimport { getErrorMessage, isAbortError } from '@ai-sdk/provider-utils';\nimport { delay } from './delay';\n\nexport type RetryFunction = <OUTPUT>(\n  fn: () => PromiseLike<OUTPUT>,\n) => PromiseLike<OUTPUT>;\n\n/**\nThe `retryWithExponentialBackoff` strategy retries a failed API call with an exponential backoff.\nYou can configure the maximum number of retries, the initial delay, and the backoff factor.\n */\nexport const retryWithExponentialBackoff =\n  ({\n    maxRetries = 2,\n    initialDelayInMs = 2000,\n    backoffFactor = 2,\n  } = {}): RetryFunction =>\n  async <OUTPUT>(f: () => PromiseLike<OUTPUT>) =>\n    _retryWithExponentialBackoff(f, {\n      maxRetries,\n      delayInMs: initialDelayInMs,\n      backoffFactor,\n    });\n\nasync function _retryWithExponentialBackoff<OUTPUT>(\n  f: () => PromiseLike<OUTPUT>,\n  {\n    maxRetries,\n    delayInMs,\n    backoffFactor,\n  }: { maxRetries: number; delayInMs: number; backoffFactor: number },\n  errors: unknown[] = [],\n): Promise<OUTPUT> {\n  try {\n    return await f();\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error; // don't retry when the request was aborted\n    }\n\n    if (maxRetries === 0) {\n      throw error; // don't wrap the error when retries are disabled\n    }\n\n    const errorMessage = getErrorMessage(error);\n    const newErrors = [...errors, error];\n    const tryNumber = newErrors.length;\n\n    if (tryNumber > maxRetries) {\n      throw new RetryError({\n        message: `Failed after ${tryNumber} attempts. Last error: ${errorMessage}`,\n        reason: 'maxRetriesExceeded',\n        errors: newErrors,\n      });\n    }\n\n    if (\n      error instanceof Error &&\n      APICallError.isAPICallError(error) &&\n      error.isRetryable === true &&\n      tryNumber <= maxRetries\n    ) {\n      await delay(delayInMs);\n      return _retryWithExponentialBackoff(\n        f,\n        { maxRetries, delayInMs: backoffFactor * delayInMs, backoffFactor },\n        newErrors,\n      );\n    }\n\n    if (tryNumber === 1) {\n      throw error; // don't wrap the error when a non-retryable error occurs on the first try\n    }\n\n    throw new RetryError({\n      message: `Failed after ${tryNumber} attempts with non-retryable error: '${errorMessage}'`,\n      reason: 'errorNotRetryable',\n      errors: newErrors,\n    });\n  }\n}\n", "export async function delay(delayInMs: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "import {\n  LanguageModelV1ImagePart,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1TextPart,\n} from '@ai-sdk/provider';\nimport { getErrorMessage } from '@ai-sdk/provider-utils';\nimport { CoreMessage } from '../prompt/message';\nimport { detectImageMimeType } from '../util/detect-image-mimetype';\nimport { download } from '../util/download';\nimport { ImagePart, TextPart } from './content-part';\nimport { convertDataContentToUint8Array } from './data-content';\nimport { ValidatedPrompt } from './get-validated-prompt';\nimport { InvalidMessageRoleError } from './invalid-message-role-error';\n\nexport async function convertToLanguageModelPrompt({\n  prompt,\n  modelSupportsImageUrls = true,\n  downloadImplementation = download,\n}: {\n  prompt: ValidatedPrompt;\n  modelSupportsImageUrls: boolean | undefined;\n  downloadImplementation?: typeof download;\n}): Promise<LanguageModelV1Prompt> {\n  const languageModelMessages: LanguageModelV1Prompt = [];\n\n  if (prompt.system != null) {\n    languageModelMessages.push({ role: 'system', content: prompt.system });\n  }\n\n  const downloadedImages =\n    modelSupportsImageUrls || prompt.messages == null\n      ? null\n      : await downloadImages(prompt.messages, downloadImplementation);\n\n  const promptType = prompt.type;\n  switch (promptType) {\n    case 'prompt': {\n      languageModelMessages.push({\n        role: 'user',\n        content: [{ type: 'text', text: prompt.prompt }],\n      });\n      break;\n    }\n\n    case 'messages': {\n      languageModelMessages.push(\n        ...prompt.messages.map(\n          (message): LanguageModelV1Message =>\n            convertToLanguageModelMessage(message, downloadedImages),\n        ),\n      );\n      break;\n    }\n\n    default: {\n      const _exhaustiveCheck: never = promptType;\n      throw new Error(`Unsupported prompt type: ${_exhaustiveCheck}`);\n    }\n  }\n\n  return languageModelMessages;\n}\n\n/**\n * Convert a CoreMessage to a LanguageModelV1Message.\n *\n * @param message The CoreMessage to convert.\n * @param downloadedImages A map of image URLs to their downloaded data. Only\n *   available if the model does not support image URLs, null otherwise.\n */\nexport function convertToLanguageModelMessage(\n  message: CoreMessage,\n  downloadedImages: Record<\n    string,\n    { mimeType: string | undefined; data: Uint8Array }\n  > | null,\n): LanguageModelV1Message {\n  const role = message.role;\n  switch (role) {\n    case 'system': {\n      return { role: 'system', content: message.content };\n    }\n\n    case 'user': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'user',\n          content: [{ type: 'text', text: message.content }],\n        };\n      }\n\n      return {\n        role: 'user',\n        content: message.content.map(\n          (part): LanguageModelV1TextPart | LanguageModelV1ImagePart => {\n            switch (part.type) {\n              case 'text': {\n                return part;\n              }\n\n              case 'image': {\n                if (part.image instanceof URL) {\n                  if (downloadedImages == null) {\n                    return {\n                      type: 'image',\n                      image: part.image,\n                      mimeType: part.mimeType,\n                    };\n                  } else {\n                    const downloadedImage =\n                      downloadedImages[part.image.toString()];\n                    return {\n                      type: 'image',\n                      image: downloadedImage.data,\n                      mimeType: part.mimeType ?? downloadedImage.mimeType,\n                    };\n                  }\n                }\n\n                // try to convert string image parts to urls\n                if (typeof part.image === 'string') {\n                  try {\n                    const url = new URL(part.image);\n\n                    switch (url.protocol) {\n                      case 'http:':\n                      case 'https:': {\n                        if (downloadedImages == null) {\n                          return {\n                            type: 'image',\n                            image: url,\n                            mimeType: part.mimeType,\n                          };\n                        } else {\n                          const downloadedImage = downloadedImages[part.image];\n                          return {\n                            type: 'image',\n                            image: downloadedImage.data,\n                            mimeType: part.mimeType ?? downloadedImage.mimeType,\n                          };\n                        }\n                      }\n                      case 'data:': {\n                        try {\n                          const [header, base64Content] = part.image.split(',');\n                          const mimeType = header.split(';')[0].split(':')[1];\n\n                          if (mimeType == null || base64Content == null) {\n                            throw new Error('Invalid data URL format');\n                          }\n\n                          return {\n                            type: 'image',\n                            image:\n                              convertDataContentToUint8Array(base64Content),\n                            mimeType,\n                          };\n                        } catch (error) {\n                          throw new Error(\n                            `Error processing data URL: ${getErrorMessage(\n                              message,\n                            )}`,\n                          );\n                        }\n                      }\n                      default: {\n                        throw new Error(\n                          `Unsupported URL protocol: ${url.protocol}`,\n                        );\n                      }\n                    }\n                  } catch (_ignored) {\n                    // not a URL\n                  }\n                }\n\n                const imageUint8 = convertDataContentToUint8Array(part.image);\n\n                return {\n                  type: 'image',\n                  image: imageUint8,\n                  mimeType: part.mimeType ?? detectImageMimeType(imageUint8),\n                };\n              }\n            }\n          },\n        ),\n      };\n    }\n\n    case 'assistant': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'assistant',\n          content: [{ type: 'text', text: message.content }],\n        };\n      }\n\n      return {\n        role: 'assistant',\n        content: message.content.filter(\n          // remove empty text parts:\n          part => part.type !== 'text' || part.text !== '',\n        ),\n      };\n    }\n\n    case 'tool': {\n      return message;\n    }\n\n    default: {\n      const _exhaustiveCheck: never = role;\n      throw new InvalidMessageRoleError({ role: _exhaustiveCheck });\n    }\n  }\n}\n\nasync function downloadImages(\n  messages: CoreMessage[],\n  downloadImplementation: typeof download,\n): Promise<Record<string, { mimeType: string | undefined; data: Uint8Array }>> {\n  const urls = messages\n    .filter(message => message.role === 'user')\n    .map(message => message.content)\n    .filter((content): content is Array<TextPart | ImagePart> =>\n      Array.isArray(content),\n    )\n    .flat()\n    .filter((part): part is ImagePart => part.type === 'image')\n    .map(part => part.image)\n    .map(part =>\n      // support string urls in image parts:\n      typeof part === 'string' &&\n      (part.startsWith('http:') || part.startsWith('https:'))\n        ? new URL(part)\n        : part,\n    )\n    .filter((image): image is URL => image instanceof URL);\n\n  // download images in parallel:\n  const downloadedImages = await Promise.all(\n    urls.map(async url => ({\n      url,\n      data: await downloadImplementation({ url }),\n    })),\n  );\n\n  return Object.fromEntries(\n    downloadedImages.map(({ url, data }) => [url.toString(), data]),\n  );\n}\n", "const mimeTypeSignatures = [\n  { mimeType: 'image/gif' as const, bytes: [0x47, 0x49, 0x46] },\n  { mimeType: 'image/png' as const, bytes: [0x89, 0x50, 0x4e, 0x47] },\n  { mimeType: 'image/jpeg' as const, bytes: [0xff, 0xd8] },\n  { mimeType: 'image/webp' as const, bytes: [0x52, 0x49, 0x46, 0x46] },\n];\n\nexport function detectImageMimeType(\n  image: Uint8Array,\n): 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp' | undefined {\n  for (const { bytes, mimeType } of mimeTypeSignatures) {\n    if (\n      image.length >= bytes.length &&\n      bytes.every((byte, index) => image[index] === byte)\n    ) {\n      return mimeType;\n    }\n  }\n\n  return undefined;\n}\n", "import { DownloadError } from '@ai-sdk/provider';\n\nexport async function download({\n  url,\n  fetchImplementation = fetch,\n}: {\n  url: URL;\n  fetchImplementation?: typeof fetch;\n}): Promise<{\n  data: Uint8Array;\n  mimeType: string | undefined;\n}> {\n  const urlText = url.toString();\n  try {\n    const response = await fetchImplementation(urlText);\n\n    if (!response.ok) {\n      throw new DownloadError({\n        url: urlText,\n        statusCode: response.status,\n        statusText: response.statusText,\n      });\n    }\n\n    return {\n      data: new Uint8Array(await response.arrayBuffer()),\n      mimeType: response.headers.get('content-type') ?? undefined,\n    };\n  } catch (error) {\n    if (DownloadError.isDownloadError(error)) {\n      throw error;\n    }\n\n    throw new DownloadError({ url: urlText, cause: error });\n  }\n}\n", "import { InvalidDataContentError } from '@ai-sdk/provider';\nimport {\n  convertBase64ToUint8Array,\n  convertUint8ArrayToBase64,\n} from '@ai-sdk/provider-utils';\n\n/**\nData content. Can either be a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer.\n */\nexport type DataContent = string | Uint8Array | ArrayBuffer | Buffer;\n\n/**\nConverts data content to a base64-encoded string.\n\n@param content - Data content to convert.\n@returns Base64-encoded string.\n*/\nexport function convertDataContentToBase64String(content: DataContent): string {\n  if (typeof content === 'string') {\n    return content;\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return convertUint8ArrayToBase64(new Uint8Array(content));\n  }\n\n  return convertUint8ArrayToBase64(content);\n}\n\n/**\nConverts data content to a Uint8Array.\n\n@param content - Data content to convert.\n@returns Uint8Array.\n */\nexport function convertDataContentToUint8Array(\n  content: DataContent,\n): Uint8Array {\n  if (content instanceof Uint8Array) {\n    return content;\n  }\n\n  if (typeof content === 'string') {\n    try {\n      return convertBase64ToUint8Array(content);\n    } catch (error) {\n      throw new InvalidDataContentError({\n        message:\n          'Invalid data content. Content string is not a base64-encoded media.',\n        content,\n        cause: error,\n      });\n    }\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return new Uint8Array(content);\n  }\n\n  throw new InvalidDataContentError({ content });\n}\n\n/**\n * Converts a Uint8Array to a string of text.\n *\n * @param uint8Array - The Uint8Array to convert.\n * @returns The converted string.\n */\nexport function convertUint8ArrayToText(uint8Array: Uint8Array): string {\n  try {\n    return new TextDecoder().decode(uint8Array);\n  } catch (error) {\n    throw new Error('Error decoding Uint8Array to text');\n  }\n}\n", "export class InvalidMessageRoleError extends Error {\n  readonly role: string;\n\n  constructor({\n    role,\n    message = `Invalid message role: '${role}'. Must be one of: \"system\", \"user\", \"assistant\", \"tool\".`,\n  }: {\n    role: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidMessageRoleError';\n\n    this.role = role;\n  }\n\n  static isInvalidMessageRoleError(\n    error: unknown,\n  ): error is InvalidMessageRoleError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidMessageRoleError' &&\n      typeof (error as InvalidMessageRoleError).role === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      role: this.role,\n    };\n  }\n}\n", "import { InvalidPromptError } from '@ai-sdk/provider';\nimport { CoreMessage } from './message';\nimport { Prompt } from './prompt';\n\nexport type ValidatedPrompt =\n  | {\n      type: 'prompt';\n      prompt: string;\n      messages: undefined;\n      system?: string;\n    }\n  | {\n      type: 'messages';\n      prompt: undefined;\n      messages: CoreMessage[];\n      system?: string;\n    };\n\nexport function getValidatedPrompt(prompt: Prompt): ValidatedPrompt {\n  if (prompt.prompt == null && prompt.messages == null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt or messages must be defined',\n    });\n  }\n\n  if (prompt.prompt != null && prompt.messages != null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt and messages cannot be defined at the same time',\n    });\n  }\n\n  if (prompt.messages != null) {\n    for (const message of prompt.messages) {\n      if (message.role === 'system' && typeof message.content !== 'string') {\n        throw new InvalidPromptError({\n          prompt,\n          message: 'system message content must be a string',\n        });\n      }\n    }\n  }\n\n  return prompt.prompt != null\n    ? {\n        type: 'prompt',\n        prompt: prompt.prompt,\n        messages: undefined,\n        system: prompt.system,\n      }\n    : {\n        type: 'messages',\n        prompt: undefined,\n        messages: prompt.messages!, // only possible case bc of checks above\n        system: prompt.system,\n      };\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { CallSettings } from './call-settings';\n\n/**\n * Validates call settings and sets default values.\n */\nexport function prepareCallSettings({\n  maxTokens,\n  temperature,\n  topP,\n  presencePenalty,\n  frequencyPenalty,\n  stopSequences,\n  seed,\n  maxRetries,\n}: CallSettings): CallSettings {\n  if (maxTokens != null) {\n    if (!Number.isInteger(maxTokens)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be an integer',\n      });\n    }\n\n    if (maxTokens < 1) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be >= 1',\n      });\n    }\n  }\n\n  if (temperature != null) {\n    if (typeof temperature !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'temperature',\n        value: temperature,\n        message: 'temperature must be a number',\n      });\n    }\n  }\n\n  if (topP != null) {\n    if (typeof topP !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topP',\n        value: topP,\n        message: 'topP must be a number',\n      });\n    }\n  }\n\n  if (presencePenalty != null) {\n    if (typeof presencePenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'presencePenalty',\n        value: presencePenalty,\n        message: 'presencePenalty must be a number',\n      });\n    }\n  }\n\n  if (frequencyPenalty != null) {\n    if (typeof frequencyPenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'frequencyPenalty',\n        value: frequencyPenalty,\n        message: 'frequencyPenalty must be a number',\n      });\n    }\n  }\n\n  if (seed != null) {\n    if (!Number.isInteger(seed)) {\n      throw new InvalidArgumentError({\n        parameter: 'seed',\n        value: seed,\n        message: 'seed must be an integer',\n      });\n    }\n  }\n\n  if (maxRetries != null) {\n    if (!Number.isInteger(maxRetries)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be an integer',\n      });\n    }\n\n    if (maxRetries < 0) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be >= 0',\n      });\n    }\n  }\n\n  return {\n    maxTokens,\n    temperature: temperature ?? 0,\n    topP,\n    presencePenalty,\n    frequencyPenalty,\n    stopSequences:\n      stopSequences != null && stopSequences.length > 0\n        ? stopSequences\n        : undefined,\n    seed,\n    maxRetries: maxRetries ?? 2,\n  };\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type CompletionTokenUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingTokenUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateCompletionTokenUsage(usage: {\n  promptTokens: number;\n  completionTokens: number;\n}): CompletionTokenUsage {\n  return {\n    promptTokens: usage.promptTokens,\n    completionTokens: usage.completionTokens,\n    totalTokens: usage.promptTokens + usage.completionTokens,\n  };\n}\n", "import { Validator, validatorSymbol } from '@ai-sdk/provider-utils';\nimport { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema: z.Schema<OBJECT> | Schema<OBJECT>,\n): Schema<OBJECT> {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\nexport function zodSchema<OBJECT>(zodSchema: z.Schema<OBJECT>): Schema<OBJECT> {\n  return jsonSchema(\n    // we assume that zodToJsonSchema will return a valid JSONSchema7:\n    zodToJsonSchema(zodSchema) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n", "export function isNonEmptyObject(\n  object: Record<string, unknown> | undefined | null,\n): object is Record<string, unknown> {\n  return object != null && Object.keys(object).length > 0;\n}\n", "import {\n  LanguageModelV1FunctionTool,\n  LanguageModelV1ToolChoice,\n} from '@ai-sdk/provider';\nimport { CoreTool } from '../tool/tool';\nimport { CoreToolChoice } from '../types/language-model';\nimport { isNonEmptyObject } from '../util/is-non-empty-object';\nimport { asSchema } from '../util/schema';\n\nexport function prepareToolsAndToolChoice<\n  TOOLS extends Record<string, CoreTool>,\n>({\n  tools,\n  toolChoice,\n}: {\n  tools: TOOLS | undefined;\n  toolChoice: CoreToolChoice<TOOLS> | undefined;\n}): {\n  tools: LanguageModelV1FunctionTool[] | undefined;\n  toolChoice: LanguageModelV1ToolChoice | undefined;\n} {\n  if (!isNonEmptyObject(tools)) {\n    return {\n      tools: undefined,\n      toolChoice: undefined,\n    };\n  }\n\n  return {\n    tools: Object.entries(tools).map(([name, tool]) => ({\n      type: 'function' as const,\n      name,\n      description: tool.description,\n      parameters: asSchema(tool.parameters).jsonSchema,\n    })),\n    toolChoice:\n      toolChoice == null\n        ? { type: 'auto' }\n        : typeof toolChoice === 'string'\n        ? { type: toolChoice }\n        : { type: 'tool' as const, toolName: toolChoice.toolName as string },\n  };\n}\n", "import {\n  createParser,\n  type EventSourceParser,\n  type ParsedEvent,\n  type ReconnectInterval,\n} from 'eventsource-parser';\nimport { OpenAIStreamCallbacks } from './openai-stream';\n\nexport interface FunctionCallPayload {\n  name: string;\n  arguments: Record<string, unknown>;\n}\nexport interface ToolCallPayload {\n  tools: {\n    id: string;\n    type: 'function';\n    func: {\n      name: string;\n      arguments: Record<string, unknown>;\n    };\n  }[];\n}\n\n/**\n * Configuration options and helper callback methods for AIStream stream lifecycle events.\n * @interface\n */\nexport interface AIStreamCallbacksAndOptions {\n  /** `onStart`: Called once when the stream is initialized. */\n  onStart?: () => Promise<void> | void;\n  /** `onCompletion`: Called for each tokenized message. */\n  onCompletion?: (completion: string) => Promise<void> | void;\n  /** `onFinal`: Called once when the stream is closed with the final completion message. */\n  onFinal?: (completion: string) => Promise<void> | void;\n  /** `onToken`: Called for each tokenized message. */\n  onToken?: (token: string) => Promise<void> | void;\n  /** `onText`: Called for each text chunk. */\n  onText?: (text: string) => Promise<void> | void;\n  /**\n   * @deprecated This flag is no longer used and only retained for backwards compatibility.\n   * You can remove it from your code.\n   */\n  experimental_streamData?: boolean;\n}\n\n/**\n * Options for the AIStreamParser.\n * @interface\n * @property {string} event - The event (type) from the server side event stream.\n */\nexport interface AIStreamParserOptions {\n  event?: string;\n}\n\n/**\n * Custom parser for AIStream data.\n * @interface\n * @param {string} data - The data to be parsed.\n * @param {AIStreamParserOptions} options - The options for the parser.\n * @returns {string | void} The parsed data or void.\n */\nexport interface AIStreamParser {\n  (data: string, options: AIStreamParserOptions):\n    | string\n    | void\n    | { isText: false; content: string };\n}\n\n/**\n * Creates a TransformStream that parses events from an EventSource stream using a custom parser.\n * @param {AIStreamParser} customParser - Function to handle event data.\n * @returns {TransformStream<Uint8Array, string>} TransformStream parsing events.\n */\nexport function createEventStreamTransformer(\n  customParser?: AIStreamParser,\n): TransformStream<Uint8Array, string | { isText: false; content: string }> {\n  const textDecoder = new TextDecoder();\n  let eventSourceParser: EventSourceParser;\n\n  return new TransformStream({\n    async start(controller): Promise<void> {\n      eventSourceParser = createParser(\n        (event: ParsedEvent | ReconnectInterval) => {\n          if (\n            ('data' in event &&\n              event.type === 'event' &&\n              event.data === '[DONE]') ||\n            // Replicate doesn't send [DONE] but does send a 'done' event\n            // @see https://replicate.com/docs/streaming\n            (event as any).event === 'done'\n          ) {\n            controller.terminate();\n            return;\n          }\n\n          if ('data' in event) {\n            const parsedMessage = customParser\n              ? customParser(event.data, {\n                  event: event.event,\n                })\n              : event.data;\n            if (parsedMessage) controller.enqueue(parsedMessage);\n          }\n        },\n      );\n    },\n\n    transform(chunk) {\n      eventSourceParser.feed(textDecoder.decode(chunk));\n    },\n  });\n}\n\n/**\n * Creates a transform stream that encodes input messages and invokes optional callback functions.\n * The transform stream uses the provided callbacks to execute custom logic at different stages of the stream's lifecycle.\n * - `onStart`: Called once when the stream is initialized.\n * - `onToken`: Called for each tokenized message.\n * - `onCompletion`: Called every time an AIStream completion message is received. This can occur multiple times when using e.g. OpenAI functions\n * - `onFinal`: Called once when the stream is closed with the final completion message.\n *\n * This function is useful when you want to process a stream of messages and perform specific actions during the stream's lifecycle.\n *\n * @param {AIStreamCallbacksAndOptions} [callbacks] - An object containing the callback functions.\n * @return {TransformStream<string, Uint8Array>} A transform stream that encodes input messages as Uint8Array and allows the execution of custom logic through callbacks.\n *\n * @example\n * const callbacks = {\n *   onStart: async () => console.log('Stream started'),\n *   onToken: async (token) => console.log(`Token: ${token}`),\n *   onCompletion: async (completion) => console.log(`Completion: ${completion}`)\n *   onFinal: async () => data.close()\n * };\n * const transformer = createCallbacksTransformer(callbacks);\n */\nexport function createCallbacksTransformer(\n  cb: AIStreamCallbacksAndOptions | OpenAIStreamCallbacks | undefined,\n): TransformStream<string | { isText: false; content: string }, Uint8Array> {\n  const textEncoder = new TextEncoder();\n  let aggregatedResponse = '';\n  const callbacks = cb || {};\n\n  return new TransformStream({\n    async start(): Promise<void> {\n      if (callbacks.onStart) await callbacks.onStart();\n    },\n\n    async transform(message, controller): Promise<void> {\n      const content = typeof message === 'string' ? message : message.content;\n\n      controller.enqueue(textEncoder.encode(content));\n\n      aggregatedResponse += content;\n\n      if (callbacks.onToken) await callbacks.onToken(content);\n      if (callbacks.onText && typeof message === 'string') {\n        await callbacks.onText(message);\n      }\n    },\n\n    async flush(): Promise<void> {\n      const isOpenAICallbacks = isOfTypeOpenAIStreamCallbacks(callbacks);\n      // If it's OpenAICallbacks, it has an experimental_onFunctionCall which means that the createFunctionCallTransformer\n      // will handle calling onComplete.\n      if (callbacks.onCompletion) {\n        await callbacks.onCompletion(aggregatedResponse);\n      }\n\n      if (callbacks.onFinal && !isOpenAICallbacks) {\n        await callbacks.onFinal(aggregatedResponse);\n      }\n    },\n  });\n}\n\nfunction isOfTypeOpenAIStreamCallbacks(\n  callbacks: AIStreamCallbacksAndOptions | OpenAIStreamCallbacks,\n): callbacks is OpenAIStreamCallbacks {\n  return 'experimental_onFunctionCall' in callbacks;\n}\n/**\n * Returns a stateful function that, when invoked, trims leading whitespace\n * from the input text. The trimming only occurs on the first invocation, ensuring that\n * subsequent calls do not alter the input text. This is particularly useful in scenarios\n * where a text stream is being processed and only the initial whitespace should be removed.\n *\n * @return {function(string): string} A function that takes a string as input and returns a string\n * with leading whitespace removed if it is the first invocation; otherwise, it returns the input unchanged.\n *\n * @example\n * const trimStart = trimStartOfStreamHelper();\n * const output1 = trimStart(\"   text\"); // \"text\"\n * const output2 = trimStart(\"   text\"); // \"   text\"\n *\n */\nexport function trimStartOfStreamHelper(): (text: string) => string {\n  let isStreamStart = true;\n\n  return (text: string): string => {\n    if (isStreamStart) {\n      text = text.trimStart();\n      if (text) isStreamStart = false;\n    }\n    return text;\n  };\n}\n\n/**\n * Returns a ReadableStream created from the response, parsed and handled with custom logic.\n * The stream goes through two transformation stages, first parsing the events and then\n * invoking the provided callbacks.\n *\n * For 2xx HTTP responses:\n * - The function continues with standard stream processing.\n *\n * For non-2xx HTTP responses:\n * - If the response body is defined, it asynchronously extracts and decodes the response body.\n * - It then creates a custom ReadableStream to propagate a detailed error message.\n *\n * @param {Response} response - The response.\n * @param {AIStreamParser} customParser - The custom parser function.\n * @param {AIStreamCallbacksAndOptions} callbacks - The callbacks.\n * @return {ReadableStream} The AIStream.\n * @throws Will throw an error if the response is not OK.\n */\nexport function AIStream(\n  response: Response,\n  customParser?: AIStreamParser,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream<Uint8Array> {\n  if (!response.ok) {\n    if (response.body) {\n      const reader = response.body.getReader();\n      return new ReadableStream({\n        async start(controller) {\n          const { done, value } = await reader.read();\n          if (!done) {\n            const errorText = new TextDecoder().decode(value);\n            controller.error(new Error(`Response error: ${errorText}`));\n          }\n        },\n      });\n    } else {\n      return new ReadableStream({\n        start(controller) {\n          controller.error(new Error('Response error: No response body'));\n        },\n      });\n    }\n  }\n\n  const responseBodyStream = response.body || createEmptyReadableStream();\n\n  return responseBodyStream\n    .pipeThrough(createEventStreamTransformer(customParser))\n    .pipeThrough(createCallbacksTransformer(callbacks));\n}\n\n// outputs lines like\n// 0: chunk\n// 0: more chunk\n// 1: a fct call\n// z: added data from Data\n\n/**\n * Creates an empty ReadableStream that immediately closes upon creation.\n * This function is used as a fallback for creating a ReadableStream when the response body is null or undefined,\n * ensuring that the subsequent pipeline processing doesn't fail due to a lack of a stream.\n *\n * @returns {ReadableStream} An empty and closed ReadableStream instance.\n */\nfunction createEmptyReadableStream(): ReadableStream {\n  return new ReadableStream({\n    start(controller) {\n      controller.close();\n    },\n  });\n}\n\n/**\n * Implements ReadableStream.from(asyncIterable), which isn't documented in MDN and isn't implemented in node.\n * https://github.com/whatwg/streams/commit/8d7a0bf26eb2cc23e884ddbaac7c1da4b91cf2bc\n */\nexport function readableFromAsyncIterable<T>(iterable: AsyncIterable<T>) {\n  let it = iterable[Symbol.asyncIterator]();\n  return new ReadableStream<T>({\n    async pull(controller) {\n      const { done, value } = await it.next();\n      if (done) controller.close();\n      else controller.enqueue(value);\n    },\n\n    async cancel(reason) {\n      await it.return?.(reason);\n    },\n  });\n}\n", "import { JSONValue, formatStreamPart } from '@ai-sdk/ui-utils';\n\n/**\n * A stream wrapper to send custom JSON-encoded data back to the client.\n */\nexport class StreamData {\n  private encoder = new TextEncoder();\n\n  private controller: ReadableStreamController<Uint8Array> | null = null;\n  public stream: ReadableStream<Uint8Array>;\n\n  private isClosed: boolean = false;\n  private warningTimeout: NodeJS.Timeout | null = null;\n\n  constructor() {\n    const self = this;\n\n    this.stream = new ReadableStream({\n      start: async controller => {\n        self.controller = controller;\n\n        // Set a timeout to show a warning if the stream is not closed within 3 seconds\n        if (process.env.NODE_ENV === 'development') {\n          self.warningTimeout = setTimeout(() => {\n            console.warn(\n              'The data stream is hanging. Did you forget to close it with `data.close()`?',\n            );\n          }, 3000);\n        }\n      },\n      pull: controller => {\n        // No-op: we don't need to do anything special on pull\n      },\n      cancel: reason => {\n        this.isClosed = true;\n      },\n    });\n  }\n\n  async close(): Promise<void> {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.close();\n    this.isClosed = true;\n\n    // Clear the warning timeout if the stream is closed\n    if (this.warningTimeout) {\n      clearTimeout(this.warningTimeout);\n    }\n  }\n\n  append(value: JSONValue): void {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.enqueue(\n      this.encoder.encode(formatStreamPart('data', [value])),\n    );\n  }\n\n  appendMessageAnnotation(value: JSONValue): void {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.enqueue(\n      this.encoder.encode(formatStreamPart('message_annotations', [value])),\n    );\n  }\n}\n\n/**\n * A TransformStream for LLMs that do not have their own transform stream handlers managing encoding (e.g. OpenAIStream has one for function call handling).\n * This assumes every chunk is a 'text' chunk.\n */\nexport function createStreamDataTransformer() {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder();\n  return new TransformStream({\n    transform: async (chunk, controller) => {\n      const message = decoder.decode(chunk);\n      controller.enqueue(encoder.encode(formatStreamPart('text', message)));\n    },\n  });\n}\n\n/**\n@deprecated Use `StreamData` instead.\n */\nexport class experimental_StreamData extends StreamData {}\n", "import {\n  Create<PERSON>essage,\n  Function<PERSON>all,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ue,\n  Tool<PERSON>all,\n  createChunkDecoder,\n  formatStreamPart,\n} from '@ai-sdk/ui-utils';\nimport {\n  AIStream,\n  FunctionCallPayload,\n  ToolCallPayload,\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n  trimStartOfStreamHelper,\n  type AIStreamCallbacksAndOptions,\n} from './ai-stream';\nimport { AzureChatCompletions } from './azure-openai-types';\nimport { createStreamDataTransformer } from './stream-data';\n\nexport type OpenAIStreamCallbacks = AIStreamCallbacksAndOptions & {\n  /**\n   * @example\n   * ```js\n   * const response = await openai.chat.completions.create({\n   *   model: 'gpt-3.5-turbo-0613',\n   *   stream: true,\n   *   messages,\n   *   functions,\n   * })\n   *\n   * const stream = OpenAIStream(response, {\n   *   experimental_onFunctionCall: async (functionCallPayload, createFunctionCallMessages) => {\n   *     // ... run your custom logic here\n   *     const result = await myFunction(functionCallPayload)\n   *\n   *     // Ask for another completion, or return a string to send to the client as an assistant message.\n   *     return await openai.chat.completions.create({\n   *       model: 'gpt-3.5-turbo-0613',\n   *       stream: true,\n   *       // Append the relevant \"assistant\" and \"function\" call messages\n   *       messages: [...messages, ...createFunctionCallMessages(result)],\n   *       functions,\n   *     })\n   *   }\n   * })\n   * ```\n   */\n  experimental_onFunctionCall?: (\n    functionCallPayload: FunctionCallPayload,\n    createFunctionCallMessages: (\n      functionCallResult: JSONValue,\n    ) => CreateMessage[],\n  ) => Promise<\n    Response | undefined | void | string | AsyncIterableOpenAIStreamReturnTypes\n  >;\n  /**\n   * @example\n   * ```js\n   * const response = await openai.chat.completions.create({\n   *   model: 'gpt-3.5-turbo-1106', // or gpt-4-1106-preview\n   *   stream: true,\n   *   messages,\n   *   tools,\n   *   tool_choice: \"auto\", // auto is default, but we'll be explicit\n   * })\n   *\n   * const stream = OpenAIStream(response, {\n   *   experimental_onToolCall: async (toolCallPayload, appendToolCallMessages) => {\n   *    let messages: CreateMessage[] = []\n   *    //   There might be multiple tool calls, so we need to iterate through them\n   *    for (const tool of toolCallPayload.tools) {\n   *     // ... run your custom logic here\n   *     const result = await myFunction(tool.function)\n   *    // Append the relevant \"assistant\" and \"tool\" call messages\n   *     appendToolCallMessage({tool_call_id:tool.id, function_name:tool.function.name, tool_call_result:result})\n   *    }\n   *     // Ask for another completion, or return a string to send to the client as an assistant message.\n   *     return await openai.chat.completions.create({\n   *       model: 'gpt-3.5-turbo-1106', // or gpt-4-1106-preview\n   *       stream: true,\n   *       // Append the results messages, calling appendToolCallMessage without\n   *       // any arguments will jsut return the accumulated messages\n   *       messages: [...messages, ...appendToolCallMessage()],\n   *       tools,\n   *        tool_choice: \"auto\", // auto is default, but we'll be explicit\n   *     })\n   *   }\n   * })\n   * ```\n   */\n  experimental_onToolCall?: (\n    toolCallPayload: ToolCallPayload,\n    appendToolCallMessage: (result?: {\n      tool_call_id: string;\n      function_name: string;\n      tool_call_result: JSONValue;\n    }) => CreateMessage[],\n  ) => Promise<\n    Response | undefined | void | string | AsyncIterableOpenAIStreamReturnTypes\n  >;\n};\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L28-L40\ninterface ChatCompletionChunk {\n  id: string;\n  choices: Array<ChatCompletionChunkChoice>;\n  created: number;\n  model: string;\n  object: string;\n}\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L43-L49\n// Updated for https://github.com/openai/openai-node/commit/f10c757d831d90407ba47b4659d9cd34b1a35b1d\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ChatCompletionChunkChoice {\n  delta: ChoiceDelta;\n  finish_reason:\n    | 'stop'\n    | 'length'\n    | 'tool_calls'\n    | 'content_filter'\n    | 'function_call'\n    | null;\n  index: number;\n}\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L123-L139\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ChoiceDelta {\n  /**\n   * The contents of the chunk message.\n   */\n  content?: string | null;\n\n  /**\n   * The name and arguments of a function that should be called, as generated by the\n   * model.\n   */\n  function_call?: FunctionCall;\n\n  /**\n   * The role of the author of this message.\n   */\n  role?: 'system' | 'user' | 'assistant' | 'tool';\n\n  tool_calls?: Array<DeltaToolCall>;\n}\n\n// From https://github.com/openai/openai-node/blob/master/src/resources/chat/completions.ts\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface DeltaToolCall {\n  index: number;\n\n  /**\n   * The ID of the tool call.\n   */\n  id?: string;\n\n  /**\n   * The function that the model called.\n   */\n  function?: ToolCallFunction;\n\n  /**\n   * The type of the tool. Currently, only `function` is supported.\n   */\n  type?: 'function';\n}\n\n// From https://github.com/openai/openai-node/blob/master/src/resources/chat/completions.ts\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ToolCallFunction {\n  /**\n   * The arguments to call the function with, as generated by the model in JSON\n   * format. Note that the model does not always generate valid JSON, and may\n   * hallucinate parameters not defined by your function schema. Validate the\n   * arguments in your code before calling your function.\n   */\n  arguments?: string;\n\n  /**\n   * The name of the function to call.\n   */\n  name?: string;\n}\n\n/**\n * https://github.com/openai/openai-node/blob/3ec43ee790a2eb6a0ccdd5f25faa23251b0f9b8e/src/resources/completions.ts#L28C1-L64C1\n * Completions API. Streamed and non-streamed responses are the same.\n */\ninterface Completion {\n  /**\n   * A unique identifier for the completion.\n   */\n  id: string;\n\n  /**\n   * The list of completion choices the model generated for the input prompt.\n   */\n  choices: Array<CompletionChoice>;\n\n  /**\n   * The Unix timestamp of when the completion was created.\n   */\n  created: number;\n\n  /**\n   * The model used for completion.\n   */\n  model: string;\n\n  /**\n   * The object type, which is always \"text_completion\"\n   */\n  object: string;\n\n  /**\n   * Usage statistics for the completion request.\n   */\n  usage?: CompletionUsage;\n}\n\ninterface CompletionChoice {\n  /**\n   * The reason the model stopped generating tokens. This will be `stop` if the model\n   * hit a natural stop point or a provided stop sequence, or `length` if the maximum\n   * number of tokens specified in the request was reached.\n   */\n  finish_reason: 'stop' | 'length' | 'content_filter';\n\n  index: number;\n\n  // edited: Removed CompletionChoice.logProbs and replaced with any\n  logprobs: any | null;\n\n  text: string;\n}\n\nexport interface CompletionUsage {\n  /**\n   * Usage statistics for the completion request.\n   */\n\n  /**\n   * Number of tokens in the generated completion.\n   */\n  completion_tokens: number;\n\n  /**\n   * Number of tokens in the prompt.\n   */\n  prompt_tokens: number;\n\n  /**\n   * Total number of tokens used in the request (prompt + completion).\n   */\n  total_tokens: number;\n}\n\n/**\n * Creates a parser function for processing the OpenAI stream data.\n * The parser extracts and trims text content from the JSON data. This parser\n * can handle data for chat or completion models.\n *\n * @return {(data: string) => string | void| { isText: false; content: string }}\n * A parser function that takes a JSON string as input and returns the extracted text content,\n * a complex object with isText: false for function/tool calls, or nothing.\n */\nfunction parseOpenAIStream(): (\n  data: string,\n) => string | void | { isText: false; content: string } {\n  const extract = chunkToText();\n  return data => extract(JSON.parse(data) as OpenAIStreamReturnTypes);\n}\n\n/**\n * Reads chunks from OpenAI's new Streamable interface, which is essentially\n * the same as the old Response body interface with an included SSE parser\n * doing the parsing for us.\n */\nasync function* streamable(stream: AsyncIterableOpenAIStreamReturnTypes) {\n  const extract = chunkToText();\n\n  for await (let chunk of stream) {\n    // convert chunk if it is an Azure chat completion. Azure does not expose all\n    // properties in the interfaces, and also uses camelCase instead of snake_case\n    if ('promptFilterResults' in chunk) {\n      chunk = {\n        id: chunk.id,\n        created: chunk.created.getDate(),\n        object: (chunk as any).object, // not exposed by Azure API\n        model: (chunk as any).model, // not exposed by Azure API\n        choices: chunk.choices.map(choice => ({\n          delta: {\n            content: choice.delta?.content,\n            function_call: choice.delta?.functionCall,\n            role: choice.delta?.role as any,\n            tool_calls: choice.delta?.toolCalls?.length\n              ? choice.delta?.toolCalls?.map((toolCall, index) => ({\n                  index,\n                  id: toolCall.id,\n                  function: toolCall.function,\n                  type: toolCall.type,\n                }))\n              : undefined,\n          },\n          finish_reason: choice.finishReason as any,\n          index: choice.index,\n        })),\n      } satisfies ChatCompletionChunk;\n    }\n\n    const text = extract(chunk);\n\n    if (text) yield text;\n  }\n}\n\nfunction chunkToText(): (\n  chunk: OpenAIStreamReturnTypes,\n) => string | { isText: false; content: string } | void {\n  const trimStartOfStream = trimStartOfStreamHelper();\n  let isFunctionStreamingIn: boolean;\n  return json => {\n    if (isChatCompletionChunk(json)) {\n      const delta = json.choices[0]?.delta;\n      if (delta.function_call?.name) {\n        isFunctionStreamingIn = true;\n        return {\n          isText: false,\n          content: `{\"function_call\": {\"name\": \"${delta.function_call.name}\", \"arguments\": \"`,\n        };\n      } else if (delta.tool_calls?.[0]?.function?.name) {\n        isFunctionStreamingIn = true;\n        const toolCall = delta.tool_calls[0];\n        if (toolCall.index === 0) {\n          return {\n            isText: false,\n            content: `{\"tool_calls\":[ {\"id\": \"${toolCall.id}\", \"type\": \"function\", \"function\": {\"name\": \"${toolCall.function?.name}\", \"arguments\": \"`,\n          };\n        } else {\n          return {\n            isText: false,\n            content: `\"}}, {\"id\": \"${toolCall.id}\", \"type\": \"function\", \"function\": {\"name\": \"${toolCall.function?.name}\", \"arguments\": \"`,\n          };\n        }\n      } else if (delta.function_call?.arguments) {\n        return {\n          isText: false,\n          content: cleanupArguments(delta.function_call?.arguments),\n        };\n      } else if (delta.tool_calls?.[0]?.function?.arguments) {\n        return {\n          isText: false,\n          content: cleanupArguments(delta.tool_calls?.[0]?.function?.arguments),\n        };\n      } else if (\n        isFunctionStreamingIn &&\n        (json.choices[0]?.finish_reason === 'function_call' ||\n          json.choices[0]?.finish_reason === 'stop')\n      ) {\n        isFunctionStreamingIn = false; // Reset the flag\n        return {\n          isText: false,\n          content: '\"}}',\n        };\n      } else if (\n        isFunctionStreamingIn &&\n        json.choices[0]?.finish_reason === 'tool_calls'\n      ) {\n        isFunctionStreamingIn = false; // Reset the flag\n        return {\n          isText: false,\n          content: '\"}}]}',\n        };\n      }\n    }\n\n    const text = trimStartOfStream(\n      isChatCompletionChunk(json) && json.choices[0].delta.content\n        ? json.choices[0].delta.content\n        : isCompletion(json)\n        ? json.choices[0].text\n        : '',\n    );\n\n    return text;\n  };\n\n  function cleanupArguments(argumentChunk: string) {\n    let escapedPartialJson = argumentChunk\n      .replace(/\\\\/g, '\\\\\\\\') // Replace backslashes first to prevent double escaping\n      .replace(/\\//g, '\\\\/') // Escape slashes\n      .replace(/\"/g, '\\\\\"') // Escape double quotes\n      .replace(/\\n/g, '\\\\n') // Escape new lines\n      .replace(/\\r/g, '\\\\r') // Escape carriage returns\n      .replace(/\\t/g, '\\\\t') // Escape tabs\n      .replace(/\\f/g, '\\\\f'); // Escape form feeds\n\n    return `${escapedPartialJson}`;\n  }\n}\n\nconst __internal__OpenAIFnMessagesSymbol = Symbol(\n  'internal_openai_fn_messages',\n);\n\ntype AsyncIterableOpenAIStreamReturnTypes =\n  | AsyncIterable<ChatCompletionChunk>\n  | AsyncIterable<Completion>\n  | AsyncIterable<AzureChatCompletions>;\n\ntype ExtractType<T> = T extends AsyncIterable<infer U> ? U : never;\n\ntype OpenAIStreamReturnTypes =\n  ExtractType<AsyncIterableOpenAIStreamReturnTypes>;\n\nfunction isChatCompletionChunk(\n  data: OpenAIStreamReturnTypes,\n): data is ChatCompletionChunk {\n  return (\n    'choices' in data &&\n    data.choices &&\n    data.choices[0] &&\n    'delta' in data.choices[0]\n  );\n}\n\nfunction isCompletion(data: OpenAIStreamReturnTypes): data is Completion {\n  return (\n    'choices' in data &&\n    data.choices &&\n    data.choices[0] &&\n    'text' in data.choices[0]\n  );\n}\n\n/**\n * @deprecated Use the [OpenAI provider](https://sdk.vercel.ai/providers/ai-sdk-providers/openai) instead.\n */\nexport function OpenAIStream(\n  res: Response | AsyncIterableOpenAIStreamReturnTypes,\n  callbacks?: OpenAIStreamCallbacks,\n): ReadableStream {\n  // Annotate the internal `messages` property for recursive function calls\n  const cb:\n    | undefined\n    | (OpenAIStreamCallbacks & {\n        [__internal__OpenAIFnMessagesSymbol]?: CreateMessage[];\n      }) = callbacks;\n\n  let stream: ReadableStream<Uint8Array>;\n  if (Symbol.asyncIterator in res) {\n    stream = readableFromAsyncIterable(streamable(res)).pipeThrough(\n      createCallbacksTransformer(\n        cb?.experimental_onFunctionCall || cb?.experimental_onToolCall\n          ? {\n              ...cb,\n              onFinal: undefined,\n            }\n          : {\n              ...cb,\n            },\n      ),\n    );\n  } else {\n    stream = AIStream(\n      res,\n      parseOpenAIStream(),\n      cb?.experimental_onFunctionCall || cb?.experimental_onToolCall\n        ? {\n            ...cb,\n            onFinal: undefined,\n          }\n        : {\n            ...cb,\n          },\n    );\n  }\n\n  if (cb && (cb.experimental_onFunctionCall || cb.experimental_onToolCall)) {\n    const functionCallTransformer = createFunctionCallTransformer(cb);\n    return stream.pipeThrough(functionCallTransformer);\n  } else {\n    return stream.pipeThrough(createStreamDataTransformer());\n  }\n}\n\nfunction createFunctionCallTransformer(\n  callbacks: OpenAIStreamCallbacks & {\n    [__internal__OpenAIFnMessagesSymbol]?: CreateMessage[];\n  },\n): TransformStream<Uint8Array, Uint8Array> {\n  const textEncoder = new TextEncoder();\n  let isFirstChunk = true;\n  let aggregatedResponse = '';\n  let aggregatedFinalCompletionResponse = '';\n  let isFunctionStreamingIn = false;\n\n  let functionCallMessages: CreateMessage[] =\n    callbacks[__internal__OpenAIFnMessagesSymbol] || [];\n\n  const decode = createChunkDecoder();\n\n  return new TransformStream({\n    async transform(chunk, controller): Promise<void> {\n      const message = decode(chunk);\n      aggregatedFinalCompletionResponse += message;\n\n      const shouldHandleAsFunction =\n        isFirstChunk &&\n        (message.startsWith('{\"function_call\":') ||\n          message.startsWith('{\"tool_calls\":'));\n\n      if (shouldHandleAsFunction) {\n        isFunctionStreamingIn = true;\n        aggregatedResponse += message;\n        isFirstChunk = false;\n        return;\n      }\n\n      // Stream as normal\n      if (!isFunctionStreamingIn) {\n        controller.enqueue(\n          textEncoder.encode(formatStreamPart('text', message)),\n        );\n        return;\n      } else {\n        aggregatedResponse += message;\n      }\n    },\n    async flush(controller): Promise<void> {\n      try {\n        if (\n          !isFirstChunk &&\n          isFunctionStreamingIn &&\n          (callbacks.experimental_onFunctionCall ||\n            callbacks.experimental_onToolCall)\n        ) {\n          isFunctionStreamingIn = false;\n          const payload = JSON.parse(aggregatedResponse);\n          // Append the function call message to the list\n          let newFunctionCallMessages: CreateMessage[] = [\n            ...functionCallMessages,\n          ];\n\n          let functionResponse:\n            | Response\n            | undefined\n            | void\n            | string\n            | AsyncIterableOpenAIStreamReturnTypes\n            | undefined = undefined;\n          // This callbacks.experimental_onFunctionCall check should not be necessary but TS complains\n          if (callbacks.experimental_onFunctionCall) {\n            // If the user is using the experimental_onFunctionCall callback, they should not be using tools\n            // if payload.function_call is not defined by time we get here we must have gotten a tool response\n            // and the user had defined experimental_onToolCall\n            if (payload.function_call === undefined) {\n              console.warn(\n                'experimental_onFunctionCall should not be defined when using tools',\n              );\n            }\n\n            const argumentsPayload = JSON.parse(\n              payload.function_call.arguments,\n            );\n\n            functionResponse = await callbacks.experimental_onFunctionCall(\n              {\n                name: payload.function_call.name,\n                arguments: argumentsPayload,\n              },\n              result => {\n                // Append the function call request and result messages to the list\n                newFunctionCallMessages = [\n                  ...functionCallMessages,\n                  {\n                    role: 'assistant',\n                    content: '',\n                    function_call: payload.function_call,\n                  },\n                  {\n                    role: 'function',\n                    name: payload.function_call.name,\n                    content: JSON.stringify(result),\n                  },\n                ];\n                // Return it to the user\n                return newFunctionCallMessages;\n              },\n            );\n          }\n          if (callbacks.experimental_onToolCall) {\n            const toolCalls: ToolCallPayload = {\n              tools: [],\n            };\n            for (const tool of payload.tool_calls) {\n              toolCalls.tools.push({\n                id: tool.id,\n                type: 'function',\n                func: {\n                  name: tool.function.name,\n                  arguments: JSON.parse(tool.function.arguments),\n                },\n              });\n            }\n            let responseIndex = 0;\n            try {\n              functionResponse = await callbacks.experimental_onToolCall(\n                toolCalls,\n                result => {\n                  if (result) {\n                    const { tool_call_id, function_name, tool_call_result } =\n                      result;\n                    // Append the function call request and result messages to the list\n                    newFunctionCallMessages = [\n                      ...newFunctionCallMessages,\n                      // Only append the assistant message if it's the first response\n                      ...(responseIndex === 0\n                        ? [\n                            {\n                              role: 'assistant' as const,\n                              content: '',\n                              tool_calls: payload.tool_calls.map(\n                                (tc: ToolCall) => ({\n                                  id: tc.id,\n                                  type: 'function',\n                                  function: {\n                                    name: tc.function.name,\n                                    // we send the arguments an object to the user, but as the API expects a string, we need to stringify it\n                                    arguments: JSON.stringify(\n                                      tc.function.arguments,\n                                    ),\n                                  },\n                                }),\n                              ),\n                            },\n                          ]\n                        : []),\n                      // Append the function call result message\n                      {\n                        role: 'tool',\n                        tool_call_id,\n                        name: function_name,\n                        content: JSON.stringify(tool_call_result),\n                      },\n                    ];\n                    responseIndex++;\n                  }\n                  // Return it to the user\n                  return newFunctionCallMessages;\n                },\n              );\n            } catch (e) {\n              console.error('Error calling experimental_onToolCall:', e);\n            }\n          }\n\n          if (!functionResponse) {\n            // The user didn't do anything with the function call on the server and wants\n            // to either do nothing or run it on the client\n            // so we just return the function call as a message\n            controller.enqueue(\n              textEncoder.encode(\n                formatStreamPart(\n                  payload.function_call ? 'function_call' : 'tool_calls',\n                  // parse to prevent double-encoding:\n                  JSON.parse(aggregatedResponse),\n                ),\n              ),\n            );\n            return;\n          } else if (typeof functionResponse === 'string') {\n            // The user returned a string, so we just return it as a message\n            controller.enqueue(\n              textEncoder.encode(formatStreamPart('text', functionResponse)),\n            );\n            aggregatedFinalCompletionResponse = functionResponse;\n            return;\n          }\n\n          // Recursively:\n\n          // We don't want to trigger onStart or onComplete recursively\n          // so we remove them from the callbacks\n          // see https://github.com/vercel/ai/issues/351\n          const filteredCallbacks: OpenAIStreamCallbacks = {\n            ...callbacks,\n            onStart: undefined,\n          };\n          // We only want onFinal to be called the _last_ time\n          callbacks.onFinal = undefined;\n\n          const openAIStream = OpenAIStream(functionResponse, {\n            ...filteredCallbacks,\n            [__internal__OpenAIFnMessagesSymbol]: newFunctionCallMessages,\n          } as AIStreamCallbacksAndOptions);\n\n          const reader = openAIStream.getReader();\n\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n              break;\n            }\n            controller.enqueue(value);\n          }\n        }\n      } finally {\n        if (callbacks.onFinal && aggregatedFinalCompletionResponse) {\n          await callbacks.onFinal(aggregatedFinalCompletionResponse);\n        }\n      }\n    },\n  });\n}\n", "export const STREAMABLE_VALUE_TYPE = Symbol.for('ui.streamable.value');\nexport const DEV_DEFAULT_STREAMABLE_WARNING_TIME = 15 * 1000;\n", "import {\n  InvalidToolArgumentsError,\n  LanguageModelV1,\n  NoSuchToolError,\n} from '@ai-sdk/provider';\nimport { ReactNode } from 'react';\nimport { z } from 'zod';\n\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { CallSettings } from '../../core/prompt/call-settings';\nimport { convertToLanguageModelPrompt } from '../../core/prompt/convert-to-language-model-prompt';\nimport { getValidatedPrompt } from '../../core/prompt/get-validated-prompt';\nimport { prepareCallSettings } from '../../core/prompt/prepare-call-settings';\nimport { prepareToolsAndToolChoice } from '../../core/prompt/prepare-tools-and-tool-choice';\nimport { Prompt } from '../../core/prompt/prompt';\nimport { CallWarning, CoreToolChoice, FinishReason } from '../../core/types';\nimport {\n  CompletionTokenUsage,\n  calculateCompletionTokenUsage,\n} from '../../core/types/token-usage';\nimport { retryWithExponentialBackoff } from '../../core/util/retry-with-exponential-backoff';\nimport { createStreamableUI } from '../streamable';\nimport { createResolvablePromise } from '../utils';\n\ntype Streamable = ReactNode | Promise<ReactNode>;\n\ntype Renderer<T extends Array<any>> = (\n  ...args: T\n) =>\n  | Streamable\n  | Generator<Streamable, Streamable, void>\n  | AsyncGenerator<Streamable, Streamable, void>;\n\ntype RenderTool<PARAMETERS extends z.ZodTypeAny = any> = {\n  description?: string;\n  parameters: PARAMETERS;\n  generate?: Renderer<\n    [\n      z.infer<PARAMETERS>,\n      {\n        toolName: string;\n        toolCallId: string;\n      },\n    ]\n  >;\n};\n\ntype RenderText = Renderer<\n  [\n    {\n      /**\n       * The full text content from the model so far.\n       */\n      content: string;\n      /**\n       * The new appended text content from the model since the last `text` call.\n       */\n      delta: string;\n      /**\n       * Whether the model is done generating text.\n       * If `true`, the `content` will be the final output and this call will be the last.\n       */\n      done: boolean;\n    },\n  ]\n>;\n\ntype RenderResult = {\n  value: ReactNode;\n} & Awaited<ReturnType<LanguageModelV1['doStream']>>;\n\nconst defaultTextRenderer: RenderText = ({ content }: { content: string }) =>\n  content;\n\n/**\n * `streamUI` is a helper function to create a streamable UI from LLMs.\n */\nexport async function streamUI<\n  TOOLS extends { [name: string]: z.ZodTypeAny } = {},\n>({\n  model,\n  tools,\n  toolChoice,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  initial,\n  text,\n  onFinish,\n  ...settings\n}: CallSettings &\n  Prompt & {\n    /**\n     * The language model to use.\n     */\n    model: LanguageModelV1;\n\n    /**\n     * The tools that the model can call. The model needs to support calling tools.\n     */\n    tools?: {\n      [name in keyof TOOLS]: RenderTool<TOOLS[name]>;\n    };\n\n    /**\n     * The tool choice strategy. Default: 'auto'.\n     */\n    toolChoice?: CoreToolChoice<TOOLS>;\n\n    text?: RenderText;\n    initial?: ReactNode;\n    /**\n     * Callback that is called when the LLM response and the final object validation are finished.\n     */\n    onFinish?: (event: {\n      /**\n       * The reason why the generation finished.\n       */\n      finishReason: FinishReason;\n      /**\n       * The token usage of the generated response.\n       */\n      usage: CompletionTokenUsage;\n      /**\n       * The final ui node that was generated.\n       */\n      value: ReactNode;\n      /**\n       * Warnings from the model provider (e.g. unsupported settings)\n       */\n      warnings?: CallWarning[];\n      /**\n       * Optional raw response data.\n       */\n      rawResponse?: {\n        /**\n         * Response headers.\n         */\n        headers?: Record<string, string>;\n      };\n    }) => Promise<void> | void;\n  }): Promise<RenderResult> {\n  // TODO: Remove these errors after the experimental phase.\n  if (typeof model === 'string') {\n    throw new Error(\n      '`model` cannot be a string in `streamUI`. Use the actual model instance instead.',\n    );\n  }\n  if ('functions' in settings) {\n    throw new Error(\n      '`functions` is not supported in `streamUI`, use `tools` instead.',\n    );\n  }\n  if ('provider' in settings) {\n    throw new Error(\n      '`provider` is no longer needed in `streamUI`. Use `model` instead.',\n    );\n  }\n  if (tools) {\n    for (const [name, tool] of Object.entries(tools)) {\n      if ('render' in tool) {\n        throw new Error(\n          'Tool definition in `streamUI` should not have `render` property. Use `generate` instead. Found in tool: ' +\n            name,\n        );\n      }\n    }\n  }\n\n  const ui = createStreamableUI(initial);\n\n  // The default text renderer just returns the content as string.\n  const textRender = text || defaultTextRenderer;\n\n  let finished: Promise<void> | undefined;\n\n  async function handleRender(\n    args: [payload: any] | [payload: any, options: any],\n    renderer: undefined | Renderer<any>,\n    res: ReturnType<typeof createStreamableUI>,\n    lastCall = false,\n  ) {\n    if (!renderer) return;\n\n    const resolvable = createResolvablePromise<void>();\n\n    if (finished) {\n      finished = finished.then(() => resolvable.promise);\n    } else {\n      finished = resolvable.promise;\n    }\n\n    const value = renderer(...args);\n    if (\n      value instanceof Promise ||\n      (value &&\n        typeof value === 'object' &&\n        'then' in value &&\n        typeof value.then === 'function')\n    ) {\n      const node = await (value as Promise<React.ReactNode>);\n\n      if (lastCall) {\n        res.done(node);\n      } else {\n        res.update(node);\n      }\n\n      resolvable.resolve(void 0);\n    } else if (\n      value &&\n      typeof value === 'object' &&\n      Symbol.asyncIterator in value\n    ) {\n      const it = value as AsyncGenerator<\n        React.ReactNode,\n        React.ReactNode,\n        void\n      >;\n      while (true) {\n        const { done, value } = await it.next();\n        if (lastCall && done) {\n          res.done(value);\n        } else {\n          res.update(value);\n        }\n        if (done) break;\n      }\n      resolvable.resolve(void 0);\n    } else if (value && typeof value === 'object' && Symbol.iterator in value) {\n      const it = value as Generator<React.ReactNode, React.ReactNode, void>;\n      while (true) {\n        const { done, value } = it.next();\n        if (lastCall && done) {\n          res.done(value);\n        } else {\n          res.update(value);\n        }\n        if (done) break;\n      }\n      resolvable.resolve(void 0);\n    } else {\n      if (lastCall) {\n        res.done(value);\n      } else {\n        res.update(value);\n      }\n      resolvable.resolve(void 0);\n    }\n  }\n\n  const retry = retryWithExponentialBackoff({ maxRetries });\n  const validatedPrompt = getValidatedPrompt({ system, prompt, messages });\n  const result = await retry(async () =>\n    model.doStream({\n      mode: {\n        type: 'regular',\n        ...prepareToolsAndToolChoice({ tools, toolChoice }),\n      },\n      ...prepareCallSettings(settings),\n      inputFormat: validatedPrompt.type,\n      prompt: await convertToLanguageModelPrompt({\n        prompt: validatedPrompt,\n        modelSupportsImageUrls: model.supportsImageUrls,\n      }),\n      abortSignal,\n      headers,\n    }),\n  );\n\n  const [stream, forkedStream] = result.stream.tee();\n\n  (async () => {\n    try {\n      // Consume the forked stream asynchonously.\n\n      let content = '';\n      let hasToolCall = false;\n\n      const reader = forkedStream.getReader();\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        switch (value.type) {\n          case 'text-delta': {\n            content += value.textDelta;\n            handleRender(\n              [{ content, done: false, delta: value.textDelta }],\n              textRender,\n              ui,\n            );\n            break;\n          }\n\n          case 'tool-call-delta': {\n            hasToolCall = true;\n            break;\n          }\n\n          case 'tool-call': {\n            const toolName = value.toolName as keyof TOOLS & string;\n\n            if (!tools) {\n              throw new NoSuchToolError({ toolName: toolName });\n            }\n\n            const tool = tools[toolName];\n            if (!tool) {\n              throw new NoSuchToolError({\n                toolName,\n                availableTools: Object.keys(tools),\n              });\n            }\n\n            hasToolCall = true;\n            const parseResult = safeParseJSON({\n              text: value.args,\n              schema: tool.parameters,\n            });\n\n            if (parseResult.success === false) {\n              throw new InvalidToolArgumentsError({\n                toolName,\n                toolArgs: value.args,\n                cause: parseResult.error,\n              });\n            }\n\n            handleRender(\n              [\n                parseResult.value,\n                {\n                  toolName,\n                  toolCallId: value.toolCallId,\n                },\n              ],\n              tool.generate,\n              ui,\n              true,\n            );\n\n            break;\n          }\n\n          case 'error': {\n            throw value.error;\n          }\n\n          case 'finish': {\n            onFinish?.({\n              finishReason: value.finishReason,\n              usage: calculateCompletionTokenUsage(value.usage),\n              value: ui.value,\n              warnings: result.warnings,\n              rawResponse: result.rawResponse,\n            });\n          }\n        }\n      }\n\n      if (hasToolCall) {\n        await finished;\n      } else {\n        handleRender([{ content, done: true }], textRender, ui, true);\n        await finished;\n      }\n    } catch (error) {\n      // During the stream rendering, we don't want to throw the error to the\n      // parent scope but only let the React's error boundary to catch it.\n      ui.error(error);\n    }\n  })();\n\n  return {\n    ...result,\n    stream,\n    value: ui.value,\n  };\n}\n", "// This file provides the AI context to all AI Actions via AsyncLocalStorage.\n\nimport * as React from 'react';\nimport { InternalAIProvider } from './rsc-shared.mjs';\nimport {\n  withAIState,\n  getAIStateDeltaPromise,\n  sealMutableAIState,\n} from './ai-state';\nimport type {\n  ServerWrappedActions,\n  AIAction,\n  AIActions,\n  AIProvider,\n  InternalAIStateStorageOptions,\n  OnSetAIState,\n  OnGetUIState,\n} from './types';\n\nasync function innerAction<T>(\n  {\n    action,\n    options,\n  }: { action: AIAction; options: InternalAIStateStorageOptions },\n  state: T,\n  ...args: unknown[]\n) {\n  'use server';\n  return await withAIState(\n    {\n      state,\n      options,\n    },\n    async () => {\n      const result = await action(...args);\n      sealMutableAIState();\n      return [getAIStateDeltaPromise() as Promise<T>, result];\n    },\n  );\n}\n\nfunction wrapAction<T = unknown>(\n  action: AIAction,\n  options: InternalAIStateStorageOptions,\n) {\n  return innerAction.bind(null, { action, options }) as AIAction<T>;\n}\n\nexport function createAI<\n  AIState = any,\n  UIState = any,\n  Actions extends AIActions = {},\n>({\n  actions,\n  initialAIState,\n  initialUIState,\n\n  onSetAIState,\n  onGetUIState,\n}: {\n  actions: Actions;\n  initialAIState?: AIState;\n  initialUIState?: UIState;\n\n  /**\n   * This function is called whenever the AI state is updated by an Action.\n   * You can use this to persist the AI state to a database, or to send it to a\n   * logging service.\n   */\n  onSetAIState?: OnSetAIState<AIState>;\n\n  /**\n   * This function is used to retrieve the UI state based on the AI state.\n   * For example, to render the initial UI state based on a given AI state, or\n   * to sync the UI state when the application is already loaded.\n   *\n   * If returning `undefined`, the client side UI state will not be updated.\n   *\n   * This function must be annotated with the `\"use server\"` directive.\n   *\n   * @example\n   * ```tsx\n   * onGetUIState: async () => {\n   *   'use server';\n   *\n   *   const currentAIState = getAIState();\n   *   const externalAIState = await loadAIStateFromDatabase();\n   *\n   *   if (currentAIState === externalAIState) return undefined;\n   *\n   *   // Update current AI state and return the new UI state\n   *   const state = getMutableAIState()\n   *   state.done(externalAIState)\n   *\n   *   return <div>...</div>;\n   * }\n   * ```\n   */\n  onGetUIState?: OnGetUIState<UIState>;\n}) {\n  // Wrap all actions with our HoC.\n  const wrappedActions: ServerWrappedActions = {};\n  for (const name in actions) {\n    wrappedActions[name] = wrapAction(actions[name], {\n      onSetAIState,\n    });\n  }\n\n  const wrappedSyncUIState = onGetUIState\n    ? wrapAction(onGetUIState, {})\n    : undefined;\n\n  const AI: AIProvider<AIState, UIState, Actions> = async props => {\n    if ('useState' in React) {\n      // This file must be running on the React Server layer.\n      // Ideally we should be using `import \"server-only\"` here but we can have a\n      // more customized error message with this implementation.\n      throw new Error(\n        'This component can only be used inside Server Components.',\n      );\n    }\n\n    let uiState = props.initialUIState ?? initialUIState;\n    let aiState = props.initialAIState ?? initialAIState;\n    let aiStateDelta = undefined;\n\n    if (wrappedSyncUIState) {\n      const [newAIStateDelta, newUIState] = await wrappedSyncUIState(aiState);\n      if (newUIState !== undefined) {\n        aiStateDelta = newAIStateDelta;\n        uiState = newUIState;\n      }\n    }\n\n    return (\n      <InternalAIProvider\n        wrappedActions={wrappedActions}\n        wrappedSyncUIState={wrappedSyncUIState}\n        initialUIState={uiState}\n        initialAIState={aiState}\n        initialAIStatePatch={aiStateDelta}\n      >\n        {props.children}\n      </InternalAIProvider>\n    );\n  };\n\n  return AI;\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,YAAY,mBAAmB;;;ACD/B,SAAgB,gBAAgB;AA+BxB,mBAGI,KAHJ;AA7BD,SAAS,0BAAmC;AACjD,MAAI,SAA6B;AACjC,QAAM,UAAU,IAAI,QAAW,CAAC,KAAK,QAAQ;AAC3C,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,IAAM,IAAI;AAAA,EACP,OAAO;AAAA,IACN;AAAA;AAAA,IACA;AAAA;AAAA,EACF,MAGM;AACJ,UAAM,QAAQ,MAAM;AACpB,QAAI,MAAM,MAAM;AACd,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,MAAM,QAAQ;AAChB,aACE,iCACG;AAAA;AAAA,QACD,oBAAC,YAAS,UAAU,MAAM,OACxB,8BAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,SACF;AAAA,IAEJ;AAEA,WACE,oBAAC,YAAS,UAAU,MAAM,OACxB,8BAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,EAEJ;AAIF,EAAE,CAAC;AAEI,SAAS,qBAAqB,cAA+B;AAClE,QAAM,EAAE,SAAS,SAAS,OAAO,IAAI,wBAAwB;AAE7D,SAAO;AAAA,IACL,KACE,oBAAC,YAAS,UAAU,cAClB,8BAAC,KAAE,GAAG,cAAc,GAAG,SAAS,GAClC;AAAA,IAEF;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAM,aAAa,CAAC,MACzB,OAAO,MAAM;AAER,IAAM,gBAAgB,OAAO,WAA2B;AAC7D,QAAM,SAAS,OAAO,UAAU;AAChC,SAAO,MAAM;AACX,UAAM,EAAE,KAAK,IAAI,MAAM,OAAO,KAAK;AACnC,QAAI;AAAM;AAAA,EACZ;AACF;;;AD7DA,IAAM,sBAAsB,IAAI,kBAO7B;AAEH,SAAS,uBAAuB,SAAiB;AAC/C,QAAM,QAAQ,oBAAoB,SAAS;AAC3C,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACA,SAAO;AACT;AAEO,SAAS,YACd,EAAE,OAAO,QAAQ,GACjB,IACG;AACH,SAAO,oBAAoB;AAAA,IACzB;AAAA,MACE,cAAc;AAAA,MACd,eAAe;AAAA,MACf,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,yBAAyB;AACvC,QAAM,QAAQ,uBAAuB,0BAA0B;AAC/D,SAAO,MAAM;AACf;AAKO,SAAS,qBAAqB;AACnC,QAAM,QAAQ,uBAAuB,0BAA0B;AAC/D,QAAM,SAAS;AACjB;AAgBA,SAAS,cACJ,MACH;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,MAAM,KAAK,CAAC;AAClB,QAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,YAAM,IAAI;AAAA,QACR,sBAAsB;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,MAAM,aAAa,GAAsC;AAAA,EAClE;AAEA,SAAO,MAAM;AACf;AA0BA,SAAS,qBACJ,MACH;AAOA,QAAM,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ;AAChB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,MAAM,sBAAsB;AAC/B,UAAM,EAAE,SAAS,QAAQ,IAAI,wBAAwB;AACrD,UAAM,uBAAuB;AAC7B,UAAM,uBAAuB;AAAA,EAC/B;AAEA,WAAS,SAAS,UAA6B,MAAe;AA/IhE;AAgJI,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,IAAI;AAAA,UACR,yBAAyB;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAW,QAAQ,GAAG;AACxB,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,aAAa,KAAK,CAAC,CAAC,IAAI,SAAS,MAAM,aAAa,KAAK,CAAC,CAAC,CAAC;AAAA,MACpE,OAAO;AACL,cAAM,eAAe,SAAS,MAAM,YAAY;AAAA,MAClD;AAAA,IACF,OAAO;AACL,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,aAAa,KAAK,CAAC,CAAC,IAAI;AAAA,MAChC,OAAO;AACL,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAEA,sBAAM,SAAQ,iBAAd,4BAA6B;AAAA,MAC3B,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI;AAAA,MACjC,OAAO,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,QAAM,eAAe;AAAA,IACnB,KAAK,MAAM;AACT,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,OAAO,MAAM,iBAAiB,UAAU;AAC1C,gBAAM,IAAI;AAAA,YACR,sBAAsB;AAAA,cACpB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,MAAM,aAAa,GAAG;AAAA,MAC/B;AAEA,aAAO,MAAM;AAAA,IACf;AAAA,IACA,QAAQ,SAAS,OAAO,YAA+B;AACrD,eAAS,YAAY,KAAK;AAAA,IAC5B;AAAA,IACA,MAAM,SAAS,QAAQ,UAAoC;AACzD,UAAI,SAAS,SAAS,GAAG;AACvB,iBAAS,SAAS,CAAC,GAAwB,IAAI;AAAA,MACjD;AAEA,YAAM,QAAsB,mBAAK,MAAM,eAAe,MAAM,YAAY;AACxE,YAAM,qBAAsB,KAAK;AAAA,IACnC;AAAA,EACF;AAEA,SAAO;AACT;;;AE3MA,OAAOA,sBAAqB;;;ACH5B,SAAS,cAAc,kBAAkB;AACzC,SAAS,iBAAiB,oBAAoB;;;ACD9C,eAAsB,MAAM,WAAkC;AAC5D,SAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,SAAS,CAAC;AAC9D;;;ADUO,IAAM,8BACX,CAAC;AAAA,EACC,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,gBAAgB;AAClB,IAAI,CAAC,MACL,OAAe,MACb,6BAA6B,GAAG;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,EACX;AACF,CAAC;AAEL,eAAe,6BACb,GACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GACA,SAAoB,CAAC,GACJ;AACjB,MAAI;AACF,WAAO,MAAM,EAAE;AAAA,EACjB,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,eAAe,GAAG;AACpB,YAAM;AAAA,IACR;AAEA,UAAM,eAAe,gBAAgB,KAAK;AAC1C,UAAM,YAAY,CAAC,GAAG,QAAQ,KAAK;AACnC,UAAM,YAAY,UAAU;AAE5B,QAAI,YAAY,YAAY;AAC1B,YAAM,IAAI,WAAW;AAAA,QACnB,SAAS,gBAAgB,SAAS,0BAA0B,YAAY;AAAA,QACxE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,QACE,iBAAiB,SACjB,aAAa,eAAe,KAAK,KACjC,MAAM,gBAAgB,QACtB,aAAa,YACb;AACA,YAAM,MAAM,SAAS;AACrB,aAAO;AAAA,QACL;AAAA,QACA,EAAE,YAAY,WAAW,gBAAgB,WAAW,cAAc;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAEA,QAAI,cAAc,GAAG;AACnB,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,WAAW;AAAA,MACnB,SAAS,gBAAgB,SAAS,wCAAwC,YAAY;AAAA,MACtF,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AE3EA,SAAS,mBAAAC,wBAAuB;;;ACNhC,IAAM,qBAAqB;AAAA,EACzB,EAAE,UAAU,aAAsB,OAAO,CAAC,IAAM,IAAM,EAAI,EAAE;AAAA,EAC5D,EAAE,UAAU,aAAsB,OAAO,CAAC,KAAM,IAAM,IAAM,EAAI,EAAE;AAAA,EAClE,EAAE,UAAU,cAAuB,OAAO,CAAC,KAAM,GAAI,EAAE;AAAA,EACvD,EAAE,UAAU,cAAuB,OAAO,CAAC,IAAM,IAAM,IAAM,EAAI,EAAE;AACrE;AAEO,SAAS,oBACd,OACqE;AACrE,aAAW,EAAE,OAAO,SAAS,KAAK,oBAAoB;AACpD,QACE,MAAM,UAAU,MAAM,UACtB,MAAM,MAAM,CAAC,MAAM,UAAU,MAAM,KAAK,MAAM,IAAI,GAClD;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACpBA,SAAS,qBAAqB;AAE9B,eAAsB,SAAS;AAAA,EAC7B;AAAA,EACA,sBAAsB;AACxB,GAMG;AAXH;AAYE,QAAM,UAAU,IAAI,SAAS;AAC7B,MAAI;AACF,UAAM,WAAW,MAAM,oBAAoB,OAAO;AAElD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,cAAc;AAAA,QACtB,KAAK;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,SAAS;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM,IAAI,WAAW,MAAM,SAAS,YAAY,CAAC;AAAA,MACjD,WAAU,cAAS,QAAQ,IAAI,cAAc,MAAnC,YAAwC;AAAA,IACpD;AAAA,EACF,SAAS,OAAO;AACd,QAAI,cAAc,gBAAgB,KAAK,GAAG;AACxC,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,cAAc,EAAE,KAAK,SAAS,OAAO,MAAM,CAAC;AAAA,EACxD;AACF;;;ACnCA,SAAS,+BAA+B;AACxC;AAAA,EACE;AAAA,EACA;AAAA,OACK;AA+BA,SAAS,+BACd,SACY;AACZ,MAAI,mBAAmB,YAAY;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,aAAO,0BAA0B,OAAO;AAAA,IAC1C,SAAS,OAAO;AACd,YAAM,IAAI,wBAAwB;AAAA,QAChC,SACE;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,aAAa;AAClC,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAEA,QAAM,IAAI,wBAAwB,EAAE,QAAQ,CAAC;AAC/C;;;AC5DO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAGjD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,IAAI;AAAA,EAC1C,GAGG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,0BACL,OACkC;AAClC,WACE,iBAAiB,SACjB,MAAM,SAAS,gCACf,OAAQ,MAAkC,SAAS;AAAA,EAEvD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;AJrBA,eAAsB,6BAA6B;AAAA,EACjD;AAAA,EACA,yBAAyB;AAAA,EACzB,yBAAyB;AAC3B,GAImC;AACjC,QAAM,wBAA+C,CAAC;AAEtD,MAAI,OAAO,UAAU,MAAM;AACzB,0BAAsB,KAAK,EAAE,MAAM,UAAU,SAAS,OAAO,OAAO,CAAC;AAAA,EACvE;AAEA,QAAM,mBACJ,0BAA0B,OAAO,YAAY,OACzC,OACA,MAAM,eAAe,OAAO,UAAU,sBAAsB;AAElE,QAAM,aAAa,OAAO;AAC1B,UAAQ,YAAY;AAAA,IAClB,KAAK,UAAU;AACb,4BAAsB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,OAAO,CAAC;AAAA,MACjD,CAAC;AACD;AAAA,IACF;AAAA,IAEA,KAAK,YAAY;AACf,4BAAsB;AAAA,QACpB,GAAG,OAAO,SAAS;AAAA,UACjB,CAAC,YACC,8BAA8B,SAAS,gBAAgB;AAAA,QAC3D;AAAA,MACF;AACA;AAAA,IACF;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,4BAA4B,gBAAgB,EAAE;AAAA,IAChE;AAAA,EACF;AAEA,SAAO;AACT;AASO,SAAS,8BACd,SACA,kBAIwB;AACxB,QAAM,OAAO,QAAQ;AACrB,UAAQ,MAAM;AAAA,IACZ,KAAK,UAAU;AACb,aAAO,EAAE,MAAM,UAAU,SAAS,QAAQ,QAAQ;AAAA,IACpD;AAAA,IAEA,KAAK,QAAQ;AACX,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ;AAAA,UACvB,CAAC,SAA6D;AA/FxE;AAgGY,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO;AAAA,cACT;AAAA,cAEA,KAAK,SAAS;AACZ,oBAAI,KAAK,iBAAiB,KAAK;AAC7B,sBAAI,oBAAoB,MAAM;AAC5B,2BAAO;AAAA,sBACL,MAAM;AAAA,sBACN,OAAO,KAAK;AAAA,sBACZ,UAAU,KAAK;AAAA,oBACjB;AAAA,kBACF,OAAO;AACL,0BAAM,kBACJ,iBAAiB,KAAK,MAAM,SAAS,CAAC;AACxC,2BAAO;AAAA,sBACL,MAAM;AAAA,sBACN,OAAO,gBAAgB;AAAA,sBACvB,WAAU,UAAK,aAAL,YAAiB,gBAAgB;AAAA,oBAC7C;AAAA,kBACF;AAAA,gBACF;AAGA,oBAAI,OAAO,KAAK,UAAU,UAAU;AAClC,sBAAI;AACF,0BAAM,MAAM,IAAI,IAAI,KAAK,KAAK;AAE9B,4BAAQ,IAAI,UAAU;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK,UAAU;AACb,4BAAI,oBAAoB,MAAM;AAC5B,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OAAO;AAAA,4BACP,UAAU,KAAK;AAAA,0BACjB;AAAA,wBACF,OAAO;AACL,gCAAM,kBAAkB,iBAAiB,KAAK,KAAK;AACnD,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OAAO,gBAAgB;AAAA,4BACvB,WAAU,UAAK,aAAL,YAAiB,gBAAgB;AAAA,0BAC7C;AAAA,wBACF;AAAA,sBACF;AAAA,sBACA,KAAK,SAAS;AACZ,4BAAI;AACF,gCAAM,CAAC,QAAQ,aAAa,IAAI,KAAK,MAAM,MAAM,GAAG;AACpD,gCAAM,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAElD,8BAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,kCAAM,IAAI,MAAM,yBAAyB;AAAA,0BAC3C;AAEA,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OACE,+BAA+B,aAAa;AAAA,4BAC9C;AAAA,0BACF;AAAA,wBACF,SAAS,OAAO;AACd,gCAAM,IAAI;AAAA,4BACR,8BAA8BC;AAAA,8BAC5B;AAAA,4BACF,CAAC;AAAA,0BACH;AAAA,wBACF;AAAA,sBACF;AAAA,sBACA,SAAS;AACP,8BAAM,IAAI;AAAA,0BACR,6BAA6B,IAAI,QAAQ;AAAA,wBAC3C;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,SAAS,UAAU;AAAA,kBAEnB;AAAA,gBACF;AAEA,sBAAM,aAAa,+BAA+B,KAAK,KAAK;AAE5D,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,WAAU,UAAK,aAAL,YAAiB,oBAAoB,UAAU;AAAA,gBAC3D;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,KAAK,aAAa;AAChB,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ;AAAA;AAAA,UAEvB,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,aAAO;AAAA,IACT;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,eAAe,eACb,UACA,wBAC6E;AAC7E,QAAM,OAAO,SACV,OAAO,aAAW,QAAQ,SAAS,MAAM,EACzC,IAAI,aAAW,QAAQ,OAAO,EAC9B;AAAA,IAAO,CAAC,YACP,MAAM,QAAQ,OAAO;AAAA,EACvB,EACC,KAAK,EACL,OAAO,CAAC,SAA4B,KAAK,SAAS,OAAO,EACzD,IAAI,UAAQ,KAAK,KAAK,EACtB;AAAA,IAAI;AAAA;AAAA,MAEH,OAAO,SAAS,aACf,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ,KACjD,IAAI,IAAI,IAAI,IACZ;AAAA;AAAA,EACN,EACC,OAAO,CAAC,UAAwB,iBAAiB,GAAG;AAGvD,QAAM,mBAAmB,MAAM,QAAQ;AAAA,IACrC,KAAK,IAAI,OAAM,SAAQ;AAAA,MACrB;AAAA,MACA,MAAM,MAAM,uBAAuB,EAAE,IAAI,CAAC;AAAA,IAC5C,EAAE;AAAA,EACJ;AAEA,SAAO,OAAO;AAAA,IACZ,iBAAiB,IAAI,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC;AAAA,EAChE;AACF;;;AK5PA,SAAS,0BAA0B;AAkB5B,SAAS,mBAAmB,QAAiC;AAClE,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,MAAM;AAC3B,eAAW,WAAW,OAAO,UAAU;AACrC,UAAI,QAAQ,SAAS,YAAY,OAAO,QAAQ,YAAY,UAAU;AACpE,cAAM,IAAI,mBAAmB;AAAA,UAC3B;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,OAAO,UAAU,OACpB;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,OAAO;AAAA,IACf,UAAU;AAAA,IACV,QAAQ,OAAO;AAAA,EACjB,IACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,OAAO;AAAA;AAAA,IACjB,QAAQ,OAAO;AAAA,EACjB;AACN;;;ACzDA,SAAS,4BAA4B;AAM9B,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA+B;AAC7B,MAAI,aAAa,MAAM;AACrB,QAAI,CAAC,OAAO,UAAU,SAAS,GAAG;AAChC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,YAAY,GAAG;AACjB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,eAAe,MAAM;AACvB,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,OAAO,oBAAoB,UAAU;AACvC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,oBAAoB,MAAM;AAC5B,QAAI,OAAO,qBAAqB,UAAU;AACxC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,cAAc,MAAM;AACtB,QAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AACjC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,aAAa,GAAG;AAClB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,oCAAe;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA,eACE,iBAAiB,QAAQ,cAAc,SAAS,IAC5C,gBACA;AAAA,IACN;AAAA,IACA,YAAY,kCAAc;AAAA,EAC5B;AACF;;;ACrFO,SAAS,8BAA8B,OAGrB;AACvB,SAAO;AAAA,IACL,cAAc,MAAM;AAAA,IACpB,kBAAkB,MAAM;AAAA,IACxB,aAAa,MAAM,eAAe,MAAM;AAAA,EAC1C;AACF;;;ACvCA,SAAoB,uBAAuB;AAG3C,OAAO,qBAAqB;AAK5B,IAAM,eAAe,OAAO,kBAAkB;AAyBvC,SAAS,WACdC,aACA;AAAA,EACE;AACF,IAII,CAAC,GACW;AAChB,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,IAChB,OAAO;AAAA;AAAA,IACP,CAAC,eAAe,GAAG;AAAA,IACnB,YAAAA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAiC;AACjD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,MAAM,YAAY,MAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,QACgB;AAChB,SAAO,SAAS,MAAM,IAAI,SAAS,UAAU,MAAM;AACrD;AAEO,SAAS,UAAkBC,YAA6C;AAC7E,SAAO;AAAA;AAAA,IAEL,gBAAgBA,UAAS;AAAA,IACzB;AAAA,MACE,UAAU,WAAS;AACjB,cAAM,SAASA,WAAU,UAAU,KAAK;AACxC,eAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;;;AClFO,SAAS,iBACd,QACmC;AACnC,SAAO,UAAU,QAAQ,OAAO,KAAK,MAAM,EAAE,SAAS;AACxD;;;ACKO,SAAS,0BAEd;AAAA,EACA;AAAA,EACA;AACF,GAME;AACA,MAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO;AAAA,MAClD,MAAM;AAAA,MACN;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,YAAY,SAAS,KAAK,UAAU,EAAE;AAAA,IACxC,EAAE;AAAA,IACF,YACE,cAAc,OACV,EAAE,MAAM,OAAO,IACf,OAAO,eAAe,WACtB,EAAE,MAAM,WAAW,IACnB,EAAE,MAAM,QAAiB,UAAU,WAAW,SAAmB;AAAA,EACzE;AACF;;;AC1CA;AAAA,EACE;AAAA,OAIK;AAoEA,SAAS,6BACd,cAC0E;AAC1E,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI;AAEJ,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,MAAM,YAA2B;AACrC,0BAAoB;AAAA,QAClB,CAAC,UAA2C;AAC1C,cACG,UAAU,SACT,MAAM,SAAS,WACf,MAAM,SAAS;AAAA;AAAA,UAGhB,MAAc,UAAU,QACzB;AACA,uBAAW,UAAU;AACrB;AAAA,UACF;AAEA,cAAI,UAAU,OAAO;AACnB,kBAAM,gBAAgB,eAClB,aAAa,MAAM,MAAM;AAAA,cACvB,OAAO,MAAM;AAAA,YACf,CAAC,IACD,MAAM;AACV,gBAAI;AAAe,yBAAW,QAAQ,aAAa;AAAA,UACrD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,UAAU,OAAO;AACf,wBAAkB,KAAK,YAAY,OAAO,KAAK,CAAC;AAAA,IAClD;AAAA,EACF,CAAC;AACH;AAwBO,SAAS,2BACd,IAC0E;AAC1E,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,qBAAqB;AACzB,QAAM,YAAY,MAAM,CAAC;AAEzB,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,QAAuB;AAC3B,UAAI,UAAU;AAAS,cAAM,UAAU,QAAQ;AAAA,IACjD;AAAA,IAEA,MAAM,UAAU,SAAS,YAA2B;AAClD,YAAM,UAAU,OAAO,YAAY,WAAW,UAAU,QAAQ;AAEhE,iBAAW,QAAQ,YAAY,OAAO,OAAO,CAAC;AAE9C,4BAAsB;AAEtB,UAAI,UAAU;AAAS,cAAM,UAAU,QAAQ,OAAO;AACtD,UAAI,UAAU,UAAU,OAAO,YAAY,UAAU;AACnD,cAAM,UAAU,OAAO,OAAO;AAAA,MAChC;AAAA,IACF;AAAA,IAEA,MAAM,QAAuB;AAC3B,YAAM,oBAAoB,8BAA8B,SAAS;AAGjE,UAAI,UAAU,cAAc;AAC1B,cAAM,UAAU,aAAa,kBAAkB;AAAA,MACjD;AAEA,UAAI,UAAU,WAAW,CAAC,mBAAmB;AAC3C,cAAM,UAAU,QAAQ,kBAAkB;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,8BACP,WACoC;AACpC,SAAO,iCAAiC;AAC1C;AAgBO,SAAS,0BAAoD;AAClE,MAAI,gBAAgB;AAEpB,SAAO,CAAC,SAAyB;AAC/B,QAAI,eAAe;AACjB,aAAO,KAAK,UAAU;AACtB,UAAI;AAAM,wBAAgB;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;AAoBO,SAAS,SACd,UACA,cACA,WAC4B;AAC5B,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,MAAM;AACjB,YAAM,SAAS,SAAS,KAAK,UAAU;AACvC,aAAO,IAAI,eAAe;AAAA,QACxB,MAAM,MAAM,YAAY;AACtB,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI,CAAC,MAAM;AACT,kBAAM,YAAY,IAAI,YAAY,EAAE,OAAO,KAAK;AAChD,uBAAW,MAAM,IAAI,MAAM,mBAAmB,SAAS,EAAE,CAAC;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO,IAAI,eAAe;AAAA,QACxB,MAAM,YAAY;AAChB,qBAAW,MAAM,IAAI,MAAM,kCAAkC,CAAC;AAAA,QAChE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,qBAAqB,SAAS,QAAQ,0BAA0B;AAEtE,SAAO,mBACJ,YAAY,6BAA6B,YAAY,CAAC,EACtD,YAAY,2BAA2B,SAAS,CAAC;AACtD;AAeA,SAAS,4BAA4C;AACnD,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,YAAY;AAChB,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAMO,SAAS,0BAA6B,UAA4B;AACvE,MAAI,KAAK,SAAS,OAAO,aAAa,EAAE;AACxC,SAAO,IAAI,eAAkB;AAAA,IAC3B,MAAM,KAAK,YAAY;AACrB,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,GAAG,KAAK;AACtC,UAAI;AAAM,mBAAW,MAAM;AAAA;AACtB,mBAAW,QAAQ,KAAK;AAAA,IAC/B;AAAA,IAEA,MAAM,OAAO,QAAQ;AApSzB;AAqSM,cAAM,QAAG,WAAH,4BAAY;AAAA,IACpB;AAAA,EACF,CAAC;AACH;;;ACxSA,SAAoB,wBAAwB;AA0FrC,SAAS,8BAA8B;AAC5C,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,IAAI,gBAAgB;AAAA,IACzB,WAAW,OAAO,OAAO,eAAe;AACtC,YAAM,UAAU,QAAQ,OAAO,KAAK;AACpC,iBAAW,QAAQ,QAAQ,OAAO,iBAAiB,QAAQ,OAAO,CAAC,CAAC;AAAA,IACtE;AAAA,EACF,CAAC;AACH;;;ACnGA;AAAA,EAKE;AAAA,EACA,oBAAAC;AAAA,OACK;AAsQP,SAAS,oBAE+C;AACtD,QAAM,UAAU,YAAY;AAC5B,SAAO,UAAQ,QAAQ,KAAK,MAAM,IAAI,CAA4B;AACpE;AAOA,gBAAgB,WAAW,QAA8C;AACvE,QAAM,UAAU,YAAY;AAE5B,iBAAe,SAAS,QAAQ;AAG9B,QAAI,yBAAyB,OAAO;AAClC,cAAQ;AAAA,QACN,IAAI,MAAM;AAAA,QACV,SAAS,MAAM,QAAQ,QAAQ;AAAA,QAC/B,QAAS,MAAc;AAAA;AAAA,QACvB,OAAQ,MAAc;AAAA;AAAA,QACtB,SAAS,MAAM,QAAQ,IAAI,YAAO;AArS1C;AAqS8C;AAAA,YACpC,OAAO;AAAA,cACL,UAAS,YAAO,UAAP,mBAAc;AAAA,cACvB,gBAAe,YAAO,UAAP,mBAAc;AAAA,cAC7B,OAAM,YAAO,UAAP,mBAAc;AAAA,cACpB,cAAY,kBAAO,UAAP,mBAAc,cAAd,mBAAyB,WACjC,kBAAO,UAAP,mBAAc,cAAd,mBAAyB,IAAI,CAAC,UAAU,WAAW;AAAA,gBACjD;AAAA,gBACA,IAAI,SAAS;AAAA,gBACb,UAAU,SAAS;AAAA,gBACnB,MAAM,SAAS;AAAA,cACjB,MACA;AAAA,YACN;AAAA,YACA,eAAe,OAAO;AAAA,YACtB,OAAO,OAAO;AAAA,UAChB;AAAA,SAAE;AAAA,MACJ;AAAA,IACF;AAEA,UAAM,OAAO,QAAQ,KAAK;AAE1B,QAAI;AAAM,YAAM;AAAA,EAClB;AACF;AAEA,SAAS,cAE+C;AACtD,QAAM,oBAAoB,wBAAwB;AAClD,MAAI;AACJ,SAAO,UAAQ;AApUjB;AAqUI,QAAI,sBAAsB,IAAI,GAAG;AAC/B,YAAM,SAAQ,UAAK,QAAQ,CAAC,MAAd,mBAAiB;AAC/B,WAAI,WAAM,kBAAN,mBAAqB,MAAM;AAC7B,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,+BAA+B,MAAM,cAAc,IAAI;AAAA,QAClE;AAAA,MACF,YAAW,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,MAAM;AAChD,gCAAwB;AACxB,cAAM,WAAW,MAAM,WAAW,CAAC;AACnC,YAAI,SAAS,UAAU,GAAG;AACxB,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,2BAA2B,SAAS,EAAE,iDAAgD,cAAS,aAAT,mBAAmB,IAAI;AAAA,UACxH;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,gBAAgB,SAAS,EAAE,iDAAgD,cAAS,aAAT,mBAAmB,IAAI;AAAA,UAC7G;AAAA,QACF;AAAA,MACF,YAAW,WAAM,kBAAN,mBAAqB,WAAW;AACzC,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,kBAAiB,WAAM,kBAAN,mBAAqB,SAAS;AAAA,QAC1D;AAAA,MACF,YAAW,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,WAAW;AACrD,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,kBAAiB,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,SAAS;AAAA,QACtE;AAAA,MACF,WACE,4BACC,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,qBAClC,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,SACrC;AACA,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS;AAAA,QACX;AAAA,MACF,WACE,2BACA,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,cACnC;AACA,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAEA,UAAM,OAAO;AAAA,MACX,sBAAsB,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,MAAM,UACjD,KAAK,QAAQ,CAAC,EAAE,MAAM,UACtB,aAAa,IAAI,IACjB,KAAK,QAAQ,CAAC,EAAE,OAChB;AAAA,IACN;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,eAAuB;AAC/C,QAAI,qBAAqB,cACtB,QAAQ,OAAO,MAAM,EACrB,QAAQ,OAAO,KAAK,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK;AAEvB,WAAO,GAAG,kBAAkB;AAAA,EAC9B;AACF;AAEA,IAAM,qCAAqC;AAAA,EACzC;AACF;AAYA,SAAS,sBACP,MAC6B;AAC7B,SACE,aAAa,QACb,KAAK,WACL,KAAK,QAAQ,CAAC,KACd,WAAW,KAAK,QAAQ,CAAC;AAE7B;AAEA,SAAS,aAAa,MAAmD;AACvE,SACE,aAAa,QACb,KAAK,WACL,KAAK,QAAQ,CAAC,KACd,UAAU,KAAK,QAAQ,CAAC;AAE5B;AAKO,SAAS,aACd,KACA,WACgB;AAEhB,QAAM,KAIG;AAET,MAAI;AACJ,MAAI,OAAO,iBAAiB,KAAK;AAC/B,aAAS,0BAA0B,WAAW,GAAG,CAAC,EAAE;AAAA,MAClD;AAAA,SACE,yBAAI,iCAA+B,yBAAI,2BACnC;AAAA,UACE,GAAG;AAAA,UACH,SAAS;AAAA,QACX,IACA;AAAA,UACE,GAAG;AAAA,QACL;AAAA,MACN;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS;AAAA,MACP;AAAA,MACA,kBAAkB;AAAA,OAClB,yBAAI,iCAA+B,yBAAI,2BACnC;AAAA,QACE,GAAG;AAAA,QACH,SAAS;AAAA,MACX,IACA;AAAA,QACE,GAAG;AAAA,MACL;AAAA,IACN;AAAA,EACF;AAEA,MAAI,OAAO,GAAG,+BAA+B,GAAG,0BAA0B;AACxE,UAAM,0BAA0B,8BAA8B,EAAE;AAChE,WAAO,OAAO,YAAY,uBAAuB;AAAA,EACnD,OAAO;AACL,WAAO,OAAO,YAAY,4BAA4B,CAAC;AAAA,EACzD;AACF;AAEA,SAAS,8BACP,WAGyC;AACzC,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,eAAe;AACnB,MAAI,qBAAqB;AACzB,MAAI,oCAAoC;AACxC,MAAI,wBAAwB;AAE5B,MAAI,uBACF,UAAU,kCAAkC,KAAK,CAAC;AAEpD,QAAM,SAAS,mBAAmB;AAElC,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,UAAU,OAAO,YAA2B;AAChD,YAAM,UAAU,OAAO,KAAK;AAC5B,2CAAqC;AAErC,YAAM,yBACJ,iBACC,QAAQ,WAAW,mBAAmB,KACrC,QAAQ,WAAW,gBAAgB;AAEvC,UAAI,wBAAwB;AAC1B,gCAAwB;AACxB,8BAAsB;AACtB,uBAAe;AACf;AAAA,MACF;AAGA,UAAI,CAAC,uBAAuB;AAC1B,mBAAW;AAAA,UACT,YAAY,OAAOC,kBAAiB,QAAQ,OAAO,CAAC;AAAA,QACtD;AACA;AAAA,MACF,OAAO;AACL,8BAAsB;AAAA,MACxB;AAAA,IACF;AAAA,IACA,MAAM,MAAM,YAA2B;AACrC,UAAI;AACF,YACE,CAAC,gBACD,0BACC,UAAU,+BACT,UAAU,0BACZ;AACA,kCAAwB;AACxB,gBAAM,UAAU,KAAK,MAAM,kBAAkB;AAE7C,cAAI,0BAA2C;AAAA,YAC7C,GAAG;AAAA,UACL;AAEA,cAAI,mBAMY;AAEhB,cAAI,UAAU,6BAA6B;AAIzC,gBAAI,QAAQ,kBAAkB,QAAW;AACvC,sBAAQ;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAEA,kBAAM,mBAAmB,KAAK;AAAA,cAC5B,QAAQ,cAAc;AAAA,YACxB;AAEA,+BAAmB,MAAM,UAAU;AAAA,cACjC;AAAA,gBACE,MAAM,QAAQ,cAAc;AAAA,gBAC5B,WAAW;AAAA,cACb;AAAA,cACA,YAAU;AAER,0CAA0B;AAAA,kBACxB,GAAG;AAAA,kBACH;AAAA,oBACE,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,eAAe,QAAQ;AAAA,kBACzB;AAAA,kBACA;AAAA,oBACE,MAAM;AAAA,oBACN,MAAM,QAAQ,cAAc;AAAA,oBAC5B,SAAS,KAAK,UAAU,MAAM;AAAA,kBAChC;AAAA,gBACF;AAEA,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,cAAI,UAAU,yBAAyB;AACrC,kBAAM,YAA6B;AAAA,cACjC,OAAO,CAAC;AAAA,YACV;AACA,uBAAW,QAAQ,QAAQ,YAAY;AACrC,wBAAU,MAAM,KAAK;AAAA,gBACnB,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,MAAM;AAAA,kBACJ,MAAM,KAAK,SAAS;AAAA,kBACpB,WAAW,KAAK,MAAM,KAAK,SAAS,SAAS;AAAA,gBAC/C;AAAA,cACF,CAAC;AAAA,YACH;AACA,gBAAI,gBAAgB;AACpB,gBAAI;AACF,iCAAmB,MAAM,UAAU;AAAA,gBACjC;AAAA,gBACA,YAAU;AACR,sBAAI,QAAQ;AACV,0BAAM,EAAE,cAAc,eAAe,iBAAiB,IACpD;AAEF,8CAA0B;AAAA,sBACxB,GAAG;AAAA;AAAA,sBAEH,GAAI,kBAAkB,IAClB;AAAA,wBACE;AAAA,0BACE,MAAM;AAAA,0BACN,SAAS;AAAA,0BACT,YAAY,QAAQ,WAAW;AAAA,4BAC7B,CAAC,QAAkB;AAAA,8BACjB,IAAI,GAAG;AAAA,8BACP,MAAM;AAAA,8BACN,UAAU;AAAA,gCACR,MAAM,GAAG,SAAS;AAAA;AAAA,gCAElB,WAAW,KAAK;AAAA,kCACd,GAAG,SAAS;AAAA,gCACd;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,IACA,CAAC;AAAA;AAAA,sBAEL;AAAA,wBACE,MAAM;AAAA,wBACN;AAAA,wBACA,MAAM;AAAA,wBACN,SAAS,KAAK,UAAU,gBAAgB;AAAA,sBAC1C;AAAA,oBACF;AACA;AAAA,kBACF;AAEA,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF,SAAS,GAAG;AACV,sBAAQ,MAAM,0CAA0C,CAAC;AAAA,YAC3D;AAAA,UACF;AAEA,cAAI,CAAC,kBAAkB;AAIrB,uBAAW;AAAA,cACT,YAAY;AAAA,gBACVA;AAAA,kBACE,QAAQ,gBAAgB,kBAAkB;AAAA;AAAA,kBAE1C,KAAK,MAAM,kBAAkB;AAAA,gBAC/B;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF,WAAW,OAAO,qBAAqB,UAAU;AAE/C,uBAAW;AAAA,cACT,YAAY,OAAOA,kBAAiB,QAAQ,gBAAgB,CAAC;AAAA,YAC/D;AACA,gDAAoC;AACpC;AAAA,UACF;AAOA,gBAAM,oBAA2C;AAAA,YAC/C,GAAG;AAAA,YACH,SAAS;AAAA,UACX;AAEA,oBAAU,UAAU;AAEpB,gBAAM,eAAe,aAAa,kBAAkB;AAAA,YAClD,GAAG;AAAA,YACH,CAAC,kCAAkC,GAAG;AAAA,UACxC,CAAgC;AAEhC,gBAAM,SAAS,aAAa,UAAU;AAEtC,iBAAO,MAAM;AACX,kBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,gBAAI,MAAM;AACR;AAAA,YACF;AACA,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,UAAE;AACA,YAAI,UAAU,WAAW,mCAAmC;AAC1D,gBAAM,UAAU,QAAQ,iCAAiC;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC7sBO,IAAM,wBAAwB,OAAO,IAAI,qBAAqB;AAC9D,IAAM,sCAAsC,KAAK;;;AjBoExD,SAAS,mBAAmB,cAAgC;AAC1D,MAAI,eAAe;AACnB,MAAI,SAAS;AACb,MAAI,EAAE,KAAK,SAAS,OAAO,IAAI,qBAAqB,YAAY;AAEhE,WAAS,aAAa,QAAgB;AACpC,QAAI,QAAQ;AACV,YAAM,IAAI,MAAM,SAAS,gCAAgC;AAAA,IAC3D;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,qBAAqB;AAC5B,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,uBAAiB,WAAW,MAAM;AAChC,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,GAAG,mCAAmC;AAAA,IACxC;AAAA,EACF;AACA,qBAAmB;AAEnB,QAAMC,cAAkC;AAAA,IACtC,OAAO;AAAA,IACP,OAAO,OAAwB;AAC7B,mBAAa,WAAW;AAGxB,UAAI,UAAU,cAAc;AAC1B,2BAAmB;AACnB,eAAOA;AAAA,MACT;AAEA,YAAM,aAAa,wBAAwB;AAC3C,qBAAe;AAEf,cAAQ,EAAE,OAAO,cAAc,MAAM,OAAO,MAAM,WAAW,QAAQ,CAAC;AACtE,gBAAU,WAAW;AACrB,eAAS,WAAW;AAEpB,yBAAmB;AAEnB,aAAOA;AAAA,IACT;AAAA,IACA,OAAO,OAAwB;AAC7B,mBAAa,WAAW;AAExB,YAAM,aAAa,wBAAwB;AAC3C,qBAAe;AAEf,cAAQ,EAAE,OAAO,MAAM,OAAO,QAAQ,MAAM,MAAM,WAAW,QAAQ,CAAC;AACtE,gBAAU,WAAW;AACrB,eAAS,WAAW;AAEpB,yBAAmB;AAEnB,aAAOA;AAAA,IACT;AAAA,IACA,MAAM,OAAY;AAChB,mBAAa,UAAU;AAEvB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,aAAO,KAAK;AAEZ,aAAOA;AAAA,IACT;AAAA,IACA,QAAQ,MAA8B;AACpC,mBAAa,SAAS;AAEtB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,UAAI,KAAK,QAAQ;AACf,gBAAQ,EAAE,OAAO,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC;AACtC,eAAOA;AAAA,MACT;AACA,cAAQ,EAAE,OAAO,cAAc,MAAM,KAAK,CAAC;AAE3C,aAAOA;AAAA,IACT;AAAA,EACF;AAEA,SAAOA;AACT;AAEA,IAAM,iCAAiC,OAAO,uBAAuB;AAMrE,SAAS,sBACP,cACA;AACA,QAAM,mBACJ,wBAAwB,kBACvB,OAAO,iBAAiB,YACvB,iBAAiB,QACjB,eAAe,gBACf,OAAO,aAAa,cAAc,cAClC,YAAY,gBACZ,OAAO,aAAa,WAAW;AAEnC,MAAI,CAAC,kBAAkB;AACrB,WAAO,0BAAgC,YAAY;AAAA,EACrD;AAEA,QAAM,kBAAkB,0BAAgC;AAMxD,kBAAgB,8BAA8B,IAAI;AAElD,GAAC,YAAY;AACX,QAAI;AAEF,YAAM,SAAS,aAAa,UAAU;AAEtC,aAAO,MAAM;AACX,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI,MAAM;AACR;AAAA,QACF;AAGA,wBAAgB,8BAA8B,IAAI;AAClD,YAAI,OAAO,UAAU,UAAU;AAC7B,0BAAgB,OAAO,KAAK;AAAA,QAC9B,OAAO;AACL,0BAAgB,OAAO,KAAK;AAAA,QAC9B;AAEA,wBAAgB,8BAA8B,IAAI;AAAA,MACpD;AAEA,sBAAgB,8BAA8B,IAAI;AAClD,sBAAgB,KAAK;AAAA,IACvB,SAAS,GAAG;AACV,sBAAgB,8BAA8B,IAAI;AAClD,sBAAgB,MAAM,CAAC;AAAA,IACzB;AAAA,EACF,GAAG;AAEH,SAAO;AACT;AAuDA,SAAS,0BAA4C,cAAkB;AACrE,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,aAAa,wBAA+C;AAEhE,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI,iBACF,WAAW;AACb,MAAI;AAEJ,WAAS,aAAa,QAAgB;AACpC,QAAI,QAAQ;AACV,YAAM,IAAI,MAAM,SAAS,mCAAmC;AAAA,IAC9D;AACA,QAAI,QAAQ;AACV,YAAM,IAAI;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,qBAAqB;AAC5B,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,uBAAiB,WAAW,MAAM;AAChC,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,GAAG,mCAAmC;AAAA,IACxC;AAAA,EACF;AACA,qBAAmB;AAEnB,WAAS,cAAc,cAA+C;AAEpE,QAAI;AAEJ,QAAI,iBAAiB,QAAW;AAC9B,aAAO,EAAE,OAAO,aAAa;AAAA,IAC/B,OAAO;AACL,UAAI,qBAAqB,CAAC,cAAc;AACtC,eAAO,EAAE,MAAM,kBAAkB;AAAA,MACnC,OAAO;AACL,eAAO,EAAE,MAAM,aAAa;AAAA,MAC9B;AAAA,IACF;AAEA,QAAI,gBAAgB;AAClB,WAAK,OAAO;AAAA,IACd;AAEA,QAAI,cAAc;AAChB,WAAK,OAAO;AAAA,IACd;AAEA,WAAO;AAAA,EACT;AAGA,WAAS,kBAAkB,OAAU;AAEnC,wBAAoB;AACpB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,OAAO,iBAAiB,UAAU;AACpC,YAAI,MAAM,WAAW,YAAY,GAAG;AAClC,8BAAoB,CAAC,GAAG,MAAM,MAAM,aAAa,MAAM,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAEA,mBAAe;AAAA,EACjB;AAEA,QAAMA,cAA2C;AAAA,IAC/C,KAAK,8BAA8B,EAAE,OAAgB;AACnD,eAAS;AAAA,IACX;AAAA,IACA,IAAI,QAAQ;AACV,aAAO,cAAc,IAAI;AAAA,IAC3B;AAAA,IACA,OAAO,OAAU;AACf,mBAAa,WAAW;AAExB,YAAM,kBAAkB,WAAW;AACnC,mBAAa,wBAAwB;AAErC,wBAAkB,KAAK;AACvB,uBAAiB,WAAW;AAC5B,sBAAgB,cAAc,CAAC;AAE/B,yBAAmB;AAEnB,aAAOA;AAAA,IACT;AAAA,IACA,OAAO,OAAU;AACf,mBAAa,WAAW;AAExB,UACE,OAAO,iBAAiB,YACxB,OAAO,iBAAiB,aACxB;AACA,cAAM,IAAI;AAAA,UACR,2DAA2D,OAAO,YAAY;AAAA,QAChF;AAAA,MACF;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI;AAAA,UACR,mDAAmD,OAAO,KAAK;AAAA,QACjE;AAAA,MACF;AAEA,YAAM,kBAAkB,WAAW;AACnC,mBAAa,wBAAwB;AAErC,UAAI,OAAO,iBAAiB,UAAU;AACpC,4BAAoB,CAAC,GAAG,KAAK;AAC7B,QAAC,eAA0B,eAAe;AAAA,MAC5C,OAAO;AACL,4BAAoB;AACpB,uBAAe;AAAA,MACjB;AAEA,uBAAiB,WAAW;AAC5B,sBAAgB,cAAc,CAAC;AAE/B,yBAAmB;AAEnB,aAAOA;AAAA,IACT;AAAA,IACA,MAAM,OAAY;AAChB,mBAAa,UAAU;AAEvB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,qBAAe;AACf,uBAAiB;AAEjB,iBAAW,QAAQ,EAAE,MAAM,CAAC;AAE5B,aAAOA;AAAA,IACT;AAAA,IACA,QAAQ,MAAgB;AACtB,mBAAa,SAAS;AAEtB,UAAI,gBAAgB;AAClB,qBAAa,cAAc;AAAA,MAC7B;AACA,eAAS;AACT,uBAAiB;AAEjB,UAAI,KAAK,QAAQ;AACf,0BAAkB,KAAK,CAAC,CAAC;AACzB,mBAAW,QAAQ,cAAc,CAAC;AAClC,eAAOA;AAAA,MACT;AAEA,iBAAW,QAAQ,CAAC,CAAC;AAErB,aAAOA;AAAA,IACT;AAAA,EACF;AAEA,SAAOA;AACT;AAoBO,SAAS,OAOd,SA+CY;AACZ,QAAM,KAAK,mBAAmB,QAAQ,OAAO;AAG7C,QAAM,OAAO,QAAQ,OACjB,QAAQ,OACR,CAAC,EAAE,QAAQ,MAA2B;AAE1C,QAAM,YAAY,QAAQ,YACtB,OAAO,QAAQ,QAAQ,SAAS,EAAE;AAAA,IAChC,CAAC,CAAC,MAAM,EAAE,aAAa,WAAW,CAAC,MAAM;AACvC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,YAAYC,iBAAgB,UAAU;AAAA,MACxC;AAAA,IACF;AAAA,EACF,IACA;AAEJ,QAAM,QAAQ,QAAQ,QAClB,OAAO,QAAQ,QAAQ,KAAK,EAAE;AAAA,IAC5B,CAAC,CAAC,MAAM,EAAE,aAAa,WAAW,CAAC,MAAM;AACvC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,YAAYA,iBAAgB,UAAU;AAAA,QAIxC;AAAA,MACF;AAAA,IACF;AAAA,EACF,IACA;AAEJ,MAAI,aAAa,OAAO;AACtB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI;AAEJ,iBAAe,aACb,MACA,UACA,KACA;AACA,QAAI,CAAC;AAAU;AAEf,UAAM,aAAa,wBAA8B;AAEjD,QAAI,UAAU;AACZ,iBAAW,SAAS,KAAK,MAAM,WAAW,OAAO;AAAA,IACnD,OAAO;AACL,iBAAW,WAAW;AAAA,IACxB;AAEA,UAAM,QAAQ,SAAS,IAAI;AAC3B,QACE,iBAAiB,WAChB,SACC,OAAO,UAAU,YACjB,UAAU,SACV,OAAO,MAAM,SAAS,YACxB;AACA,YAAM,OAAO,MAAO;AACpB,UAAI,OAAO,IAAI;AACf,iBAAW,QAAQ,MAAM;AAAA,IAC3B,WACE,SACA,OAAO,UAAU,YACjB,OAAO,iBAAiB,OACxB;AACA,YAAM,KAAK;AAKX,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,OAAAC,OAAM,IAAI,MAAM,GAAG,KAAK;AACtC,YAAI,OAAOA,MAAK;AAChB,YAAI;AAAM;AAAA,MACZ;AACA,iBAAW,QAAQ,MAAM;AAAA,IAC3B,WAAW,SAAS,OAAO,UAAU,YAAY,OAAO,YAAY,OAAO;AACzE,YAAM,KAAK;AACX,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,OAAAA,OAAM,IAAI,GAAG,KAAK;AAChC,YAAI,OAAOA,MAAK;AAChB,YAAI;AAAM;AAAA,MACZ;AACA,iBAAW,QAAQ,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,OAAO,KAAK;AAChB,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,GAAC,YAAY;AACX,QAAI,cAAc;AAClB,QAAI,UAAU;AAEd;AAAA,MACE;AAAA,QACG,MAAM,QAAQ,SAAS,KAAK,YAAY,OAAO;AAAA,UAC9C,OAAO,QAAQ;AAAA,UACf,UAAU,QAAQ;AAAA,UAClB,aAAa,QAAQ;AAAA,UACrB,QAAQ;AAAA,UACR,GAAI,YACA;AAAA,YACE;AAAA,UACF,IACA,CAAC;AAAA,UACL,GAAI,QACA;AAAA,YACE;AAAA,UACF,IACA,CAAC;AAAA,QACP,CAAC;AAAA,QACD;AAAA,UACE,GAAI,YACA;AAAA,YACE,MAAM,4BAA4B,qBAAqB;AAxoBvE;AAyoBkB,4BAAc;AACd;AAAA,gBACE,oBAAoB;AAAA,iBACpB,mBAAQ,cAAR,mBAAoB,oBAAoB,UAAxC,mBACI;AAAA,gBACJ;AAAA,cACF;AAAA,YACF;AAAA,UACF,IACA,CAAC;AAAA,UACL,GAAI,QACA;AAAA,YACE,MAAM,wBAAwB,iBAAsB;AArpBpE;AAspBkB,4BAAc;AAGd,yBAAW,QAAQ,gBAAgB,OAAO;AACxC;AAAA,kBACE,KAAK,KAAK;AAAA,mBACV,mBAAQ,UAAR,mBAAgB,KAAK,KAAK,UAA1B,mBAAwC;AAAA,kBACxC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,IACA,CAAC;AAAA,UACL,OAAO,OAAO;AACZ,uBAAW;AACX,yBAAa,EAAE,SAAS,MAAM,OAAO,OAAO,MAAM,GAAG,MAAM,EAAE;AAAA,UAC/D;AAAA,UACA,MAAM,UAAU;AACd,gBAAI,aAAa;AACf,oBAAM;AACN,iBAAG,KAAK;AACR;AAAA,YACF;AAEA,yBAAa,EAAE,SAAS,MAAM,KAAK,GAAG,MAAM,EAAE;AAC9C,kBAAM;AACN,eAAG,KAAK;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAEH,SAAO,GAAG;AACZ;;;AkBxrBA;AAAA,EACE;AAAA,EAEA;AAAA,OACK;AAIP,SAAS,qBAAqB;AA+D9B,IAAM,sBAAkC,CAAC,EAAE,QAAQ,MACjD;AAKF,eAAsB,SAEpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAmD4B;AAE1B,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe,UAAU;AAC3B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc,UAAU;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO;AACT,eAAW,CAAC,MAAM,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,UAAI,YAAY,MAAM;AACpB,cAAM,IAAI;AAAA,UACR,6GACE;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,KAAK,mBAAmB,OAAO;AAGrC,QAAM,aAAa,QAAQ;AAE3B,MAAI;AAEJ,iBAAe,aACb,MACA,UACA,KACA,WAAW,OACX;AACA,QAAI,CAAC;AAAU;AAEf,UAAM,aAAa,wBAA8B;AAEjD,QAAI,UAAU;AACZ,iBAAW,SAAS,KAAK,MAAM,WAAW,OAAO;AAAA,IACnD,OAAO;AACL,iBAAW,WAAW;AAAA,IACxB;AAEA,UAAM,QAAQ,SAAS,GAAG,IAAI;AAC9B,QACE,iBAAiB,WAChB,SACC,OAAO,UAAU,YACjB,UAAU,SACV,OAAO,MAAM,SAAS,YACxB;AACA,YAAM,OAAO,MAAO;AAEpB,UAAI,UAAU;AACZ,YAAI,KAAK,IAAI;AAAA,MACf,OAAO;AACL,YAAI,OAAO,IAAI;AAAA,MACjB;AAEA,iBAAW,QAAQ,MAAM;AAAA,IAC3B,WACE,SACA,OAAO,UAAU,YACjB,OAAO,iBAAiB,OACxB;AACA,YAAM,KAAK;AAKX,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,OAAAC,OAAM,IAAI,MAAM,GAAG,KAAK;AACtC,YAAI,YAAY,MAAM;AACpB,cAAI,KAAKA,MAAK;AAAA,QAChB,OAAO;AACL,cAAI,OAAOA,MAAK;AAAA,QAClB;AACA,YAAI;AAAM;AAAA,MACZ;AACA,iBAAW,QAAQ,MAAM;AAAA,IAC3B,WAAW,SAAS,OAAO,UAAU,YAAY,OAAO,YAAY,OAAO;AACzE,YAAM,KAAK;AACX,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,OAAAA,OAAM,IAAI,GAAG,KAAK;AAChC,YAAI,YAAY,MAAM;AACpB,cAAI,KAAKA,MAAK;AAAA,QAChB,OAAO;AACL,cAAI,OAAOA,MAAK;AAAA,QAClB;AACA,YAAI;AAAM;AAAA,MACZ;AACA,iBAAW,QAAQ,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,UAAU;AACZ,YAAI,KAAK,KAAK;AAAA,MAChB,OAAO;AACL,YAAI,OAAO,KAAK;AAAA,MAClB;AACA,iBAAW,QAAQ,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,QAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AACxD,QAAM,kBAAkB,mBAAmB,EAAE,QAAQ,QAAQ,SAAS,CAAC;AACvE,QAAM,SAAS,MAAM;AAAA,IAAM,YACzB,MAAM,SAAS;AAAA,MACb,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,GAAG,0BAA0B,EAAE,OAAO,WAAW,CAAC;AAAA,MACpD;AAAA,MACA,GAAG,oBAAoB,QAAQ;AAAA,MAC/B,aAAa,gBAAgB;AAAA,MAC7B,QAAQ,MAAM,6BAA6B;AAAA,QACzC,QAAQ;AAAA,QACR,wBAAwB,MAAM;AAAA,MAChC,CAAC;AAAA,MACD;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,OAAO,IAAI;AAEjD,GAAC,YAAY;AACX,QAAI;AAGF,UAAI,UAAU;AACd,UAAI,cAAc;AAElB,YAAM,SAAS,aAAa,UAAU;AACtC,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI;AAAM;AAEV,gBAAQ,MAAM,MAAM;AAAA,UAClB,KAAK,cAAc;AACjB,uBAAW,MAAM;AACjB;AAAA,cACE,CAAC,EAAE,SAAS,MAAM,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,cACjD;AAAA,cACA;AAAA,YACF;AACA;AAAA,UACF;AAAA,UAEA,KAAK,mBAAmB;AACtB,0BAAc;AACd;AAAA,UACF;AAAA,UAEA,KAAK,aAAa;AAChB,kBAAM,WAAW,MAAM;AAEvB,gBAAI,CAAC,OAAO;AACV,oBAAM,IAAI,gBAAgB,EAAE,SAAmB,CAAC;AAAA,YAClD;AAEA,kBAAM,OAAO,MAAM,QAAQ;AAC3B,gBAAI,CAAC,MAAM;AACT,oBAAM,IAAI,gBAAgB;AAAA,gBACxB;AAAA,gBACA,gBAAgB,OAAO,KAAK,KAAK;AAAA,cACnC,CAAC;AAAA,YACH;AAEA,0BAAc;AACd,kBAAM,cAAc,cAAc;AAAA,cAChC,MAAM,MAAM;AAAA,cACZ,QAAQ,KAAK;AAAA,YACf,CAAC;AAED,gBAAI,YAAY,YAAY,OAAO;AACjC,oBAAM,IAAI,0BAA0B;AAAA,gBAClC;AAAA,gBACA,UAAU,MAAM;AAAA,gBAChB,OAAO,YAAY;AAAA,cACrB,CAAC;AAAA,YACH;AAEA;AAAA,cACE;AAAA,gBACE,YAAY;AAAA,gBACZ;AAAA,kBACE;AAAA,kBACA,YAAY,MAAM;AAAA,gBACpB;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,SAAS;AACZ,kBAAM,MAAM;AAAA,UACd;AAAA,UAEA,KAAK,UAAU;AACb,iDAAW;AAAA,cACT,cAAc,MAAM;AAAA,cACpB,OAAO,8BAA8B,MAAM,KAAK;AAAA,cAChD,OAAO,GAAG;AAAA,cACV,UAAU,OAAO;AAAA,cACjB,aAAa,OAAO;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,aAAa;AACf,cAAM;AAAA,MACR,OAAO;AACL,qBAAa,CAAC,EAAE,SAAS,MAAM,KAAK,CAAC,GAAG,YAAY,IAAI,IAAI;AAC5D,cAAM;AAAA,MACR;AAAA,IACF,SAAS,OAAO;AAGd,SAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF,GAAG;AAEH,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA,OAAO,GAAG;AAAA,EACZ;AACF;;;AC5XA,YAAYC,YAAW;AACvB,SAAS,0BAA0B;AAoI7B,gBAAAC,YAAA;AApHN,eAAe,YACb;AAAA,EACE;AAAA,EACA;AACF,GACA,UACG,MACH;AACA;AACA,SAAO,MAAM;AAAA,IACX;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,IACA,YAAY;AACV,YAAM,SAAS,MAAM,OAAO,GAAG,IAAI;AACnC,yBAAmB;AACnB,aAAO,CAAC,uBAAuB,GAAiB,MAAM;AAAA,IACxD;AAAA,EACF;AACF;AAEA,SAAS,WACP,QACA,SACA;AACA,SAAO,YAAY,KAAK,MAAM,EAAE,QAAQ,QAAQ,CAAC;AACnD;AAEO,SAAS,SAId;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AACF,GAwCG;AAED,QAAM,iBAAuC,CAAC;AAC9C,aAAW,QAAQ,SAAS;AAC1B,mBAAe,IAAI,IAAI,WAAW,QAAQ,IAAI,GAAG;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,qBAAqB,eACvB,WAAW,cAAc,CAAC,CAAC,IAC3B;AAEJ,QAAM,KAA4C,OAAM,UAAS;AAhHnE;AAiHI,QAAI,cAAcC,QAAO;AAIvB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAU,WAAM,mBAAN,YAAwB;AACtC,QAAI,WAAU,WAAM,mBAAN,YAAwB;AACtC,QAAI,eAAe;AAEnB,QAAI,oBAAoB;AACtB,YAAM,CAAC,iBAAiB,UAAU,IAAI,MAAM,mBAAmB,OAAO;AACtE,UAAI,eAAe,QAAW;AAC5B,uBAAe;AACf,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,WACE,gBAAAD;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QAEpB,gBAAM;AAAA;AAAA,IACT;AAAA,EAEJ;AAEA,SAAO;AACT;", "names": ["zodToJsonSchema", "getErrorMessage", "getErrorMessage", "jsonSchema", "zodSchema", "formatStreamPart", "formatStreamPart", "streamable", "zodToJsonSchema", "value", "value", "React", "jsx", "React"]}