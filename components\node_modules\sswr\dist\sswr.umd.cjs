(function(r,h){typeof exports=="object"&&typeof module<"u"?h(exports,require("swrev"),require("svelte")):typeof define=="function"&&define.amd?define(["exports","swrev","svelte"],h):(r=typeof globalThis<"u"?globalThis:r||self,h(r.sswr={},r.swrev,r.svelte))})(this,function(r,h,m){"use strict";function d(){}function q(e){return e()}function D(e){e.forEach(q)}function O(e){return typeof e=="function"}function j(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function K(e,...t){if(e==null){for(const n of t)n(void 0);return d}const s=e.subscribe(...t);return s.unsubscribe?()=>s.unsubscribe():s}const b=[];function T(e,t){return{subscribe:y(e,t).subscribe}}function y(e,t=d){let s;const n=new Set;function c(o){if(j(e,o)&&(e=o,s)){const l=!b.length;for(const i of n)i[1](),b.push(i,e);if(l){for(let i=0;i<b.length;i+=2)b[i][0](b[i+1]);b.length=0}}}function a(o){c(o(e))}function v(o,l=d){const i=[o,l];return n.add(i),n.size===1&&(s=t(c,a)||d),o(e),()=>{n.delete(i),n.size===0&&s&&(s(),s=null)}}return{set:c,update:a,subscribe:v}}function E(e,t,s){const n=!Array.isArray(e),c=n?[e]:e;if(!c.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=t.length<2;return T(s,(v,o)=>{let l=!1;const i=[];let S=0,u=d;const f=()=>{if(S)return;u();const w=t(n?i[0]:i,v,o);a?v(w):u=O(w)?w:d},g=c.map((w,R)=>K(w,$=>{i[R]=$,S&=~(1<<R),l&&f()},()=>{S|=1<<R}));return l=!0,f(),function(){D(g),u(),l=!1}})}class _ extends h.SWR{useSWR(t,s){let n;const c=y(void 0,()=>()=>n==null?void 0:n()),a=y(void 0,()=>()=>n==null?void 0:n());m.beforeUpdate(()=>{const u=g=>{a.set(void 0),c.set(g)},f=g=>a.set(g);n||(n=this.subscribe(t,u,f,{loadInitialCache:!0,...s}).unsubscribe)}),m.onDestroy(()=>n==null?void 0:n());const v=(u,f)=>this.mutate(this.resolveKey(t),u,{revalidateOptions:s,...f}),o=u=>this.revalidate(this.resolveKey(t),{...s,...u}),l=u=>this.clear(this.resolveKey(t),u),i=E([c,a],([u,f])=>u===void 0&&f===void 0),S=E([c,a],([u,f])=>u!==void 0&&f===void 0);return{data:c,error:a,mutate:v,revalidate:o,clear:l,isLoading:i,isValid:S}}}const W=e=>new _(e);r.swr=W();const z=e=>(r.swr=W(e),r.swr),A=(e,t)=>r.swr.subscribeData(e,t),B=(e,t)=>r.swr.subscribeErrors(e,t),C=e=>r.swr.get(e),I=e=>r.swr.getWait(e),L=(e,t,s,n)=>r.swr.subscribe(e,t,s,n),M=(e,t)=>r.swr.useSWR(e,t),P=(e,t,s)=>r.swr.mutate(e,t,s),U=(e,t)=>r.swr.revalidate(e,t),V=(e,t)=>r.swr.clear(e,t);r.SSWR=_,r.clear=V,r.createDefaultSWR=z,r.createSWR=W,r.get=C,r.getOrWait=I,r.mutate=P,r.revalidate=U,r.subscribe=A,r.subscribeErrors=B,r.use=L,r.useSWR=M,Object.defineProperty(r,Symbol.toStringTag,{value:"Module"})});
