{"name": "swrv", "version": "1.0.4", "scripts": {"build": "yarn build:esm && yarn build:cjs", "lint": "vue-cli-service lint", "ac": "all-contributors", "build:cjs": "tsc --module CommonJs -p tsconfig.build.json", "build:esm": "tsc --module ES6 --outDir esm -p tsconfig.build.json", "test": "vue-cli-service test:unit", "test:compat": "./tests/test-compat-all.sh", "types:check": "tsc --noEmit", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs"}, "main": "./dist/index.js", "module": "./esm/index.js", "files": ["dist/**", "esm/**", "src/**"], "devDependencies": {"@types/jest": "^24.0.19", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/cli-plugin-unit-jest": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^9.1.0", "@vue/test-utils": "^2.1.0", "@vue/vue3-jest": "^29.1.1", "all-contributors-cli": "^6.20.0", "babel-loader": "^8.2.5", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.7.1", "file-loader": "^5.0.2", "jest": "^27.1.0", "jest-date-mock": "^1.0.8", "ts-jest": "^27.0.4", "typescript": "~4.5.5", "vitepress": "^1.0.0-alpha.61", "vue": "^3.3.4", "vue-template-compiler": "^2.7.14", "webpack": "~4.0.0"}, "peerDependencies": {"vue": ">=3.2.26 < 4"}, "repository": {"type": "git", "url": "https://github.com/Kong/swrv.git"}, "license": "Apache-2.0", "types": "./dist/index.d.ts", "volta": {"node": "16.16.0"}}