/// <reference types="svelte" />
import { SWR, S<PERSON><PERSON>ey, SWROptions, SWRMutateOptions, SWRMutateValue, SWRRevalidateOptions, CacheClearOptions } from 'swrev';
/**
 * Exports the extended SWR class with an extra method
 * build for svelte.
 */
export declare class SSWR extends SWR {
    /**
     * Svelte specific use of SWR.
     */
    useSWR<D = any, E = Error>(key: SWRKey | undefined | (() => SWRKey | undefined), options?: Partial<SWROptions<D>>): {
        data: import("svelte/store").Writable<D | undefined>;
        error: import("svelte/store").Writable<E | undefined>;
        mutate: (value: SWRMutateValue<D>, ops?: Partial<SWRMutateOptions<D>>) => Promise<D>;
        revalidate: (ops?: Partial<SWRRevalidateOptions<D>>) => Promise<D>;
        clear: (ops?: Partial<CacheClearOptions>) => void;
        isLoading: import("svelte/store").Readable<boolean>;
        isValid: import("svelte/store").Readable<boolean>;
    };
}
/**
 * Creates a mew SWR instance and exports basic methods to
 * work with without the need for method calling.
 */
export declare const createSWR: <D = any>(options?: Partial<SWROptions<D>> | undefined) => SSWR;
/**
 * Default SWR instance. Can be replaced
 * with the `createDefaultSWR` function.
 */
export declare let swr: SSWR;
/**
 * Creates and sets a default SWR instance given
 * the options.
 */
export declare const createDefaultSWR: <D = any>(options?: Partial<SWROptions<D>> | undefined) => SSWR;
/**
 * Gets the data of the given key. Keep in mind
 * this data will be stale and revalidate in the background
 * unless specified otherwise.
 */
export declare const subscribe: <D>(key: SWRKey | undefined, onData: (value: D) => any) => () => void;
/**
 * Subscribes to errors on the given key.
 */
export declare const subscribeErrors: <E>(key: SWRKey | undefined, onError: (error: E) => any) => () => void;
/**
 * Gets the current cached data of the given key.
 * This does not trigger any revalidation nor mutation
 * of the data.
 * - If the data has never been validated
 * (there is no cache) it will return undefined.
 * - If the item is pending to resolve (there is a request
 * pending to resolve) it will return undefined.
 */
export declare const get: <D = any>(key?: SWRKey) => D | undefined;
/**
 * Gets an element from the cache. The difference
 * with the get is that this method returns a promise
 * that will resolve the the value. If there's no item
 * in the cache, it will wait for it before resolving.
 */
export declare const getOrWait: <D = any>(key: SWRKey) => Promise<D>;
/**
 * Use a SWR value given the key and
 * subscribe to future changes.
 */
export declare const use: <D = any, E = Error>(key: SWRKey | undefined | (() => SWRKey | undefined), onData: (value: D) => void, onError: (error: E) => void, options?: Partial<SWROptions<D>> | undefined) => {
    unsubscribe: () => void;
    dataPromise: Promise<undefined> | Promise<D>;
    revalidatePromise: Promise<undefined> | Promise<D>;
};
/**
 * Use the SWR with a vue application.
 */
export declare const useSWR: <D = any, E = Error>(key: SWRKey | undefined | (() => SWRKey | undefined), options?: Partial<SWROptions<D>> | undefined) => {
    data: import("svelte/store").Writable<D | undefined>;
    error: import("svelte/store").Writable<E | undefined>;
    mutate: (value: SWRMutateValue<D>, ops?: Partial<SWRMutateOptions<D>> | undefined) => Promise<D>;
    revalidate: (ops?: Partial<SWRRevalidateOptions<D>> | undefined) => Promise<D>;
    clear: (ops?: Partial<CacheClearOptions>) => void;
    isLoading: import("svelte/store").Readable<boolean>;
    isValid: import("svelte/store").Readable<boolean>;
};
/**
 * Mutates the data of a given key with a new value.
 * This is used to replace the cache contents of the
 * given key manually.
 */
export declare const mutate: <D = any>(key: SWRKey, value: SWRMutateValue<D>, options?: Partial<SWRMutateOptions<D>> | undefined) => Promise<D>;
/**
 * Revalidates the key and mutates the cache if needed.
 */
export declare const revalidate: <D>(key: SWRKey, options?: Partial<SWRRevalidateOptions<D>> | undefined) => Promise<D>;
/**
 * Clear the specified keys from the cache. If no keys
 * are specified, it clears all the cache keys.
 */
export declare const clear: (keys?: string | string[], options?: Partial<CacheClearOptions>) => void;
