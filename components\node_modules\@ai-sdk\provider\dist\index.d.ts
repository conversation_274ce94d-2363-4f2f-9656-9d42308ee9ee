import { JSONSchema7 } from 'json-schema';

/**
An embedding is a vector, i.e. an array of numbers.
It is e.g. used to represent a text as a vector of word embeddings.
 */
type EmbeddingModelV1Embedding = Array<number>;

/**
Experimental: Specification for an embedding model that implements the embedding model
interface version 1.

VALUE is the type of the values that the model can embed.
This will allow us to go beyond text embeddings in the future,
e.g. to support image embeddings
 */
type EmbeddingModelV1<VALUE> = {
    /**
  The embedding model must specify which embedding model interface
  version it implements. This will allow us to evolve the embedding
  model interface and retain backwards compatibility. The different
  implementation versions can be handled as a discriminated union
  on our side.
     */
    readonly specificationVersion: 'v1';
    /**
  Name of the provider for logging purposes.
     */
    readonly provider: string;
    /**
  Provider-specific model ID for logging purposes.
     */
    readonly modelId: string;
    /**
  Limit of how many embeddings can be generated in a single API call.
     */
    readonly maxEmbeddingsPerCall: number | undefined;
    /**
  True if the model can handle multiple embedding calls in parallel.
     */
    readonly supportsParallelCalls: boolean;
    /**
  Generates a list of embeddings for the given input text.
  
  Naming: "do" prefix to prevent accidental direct usage of the method
  by the user.
     */
    doEmbed(options: {
        /**
    List of values to embed.
         */
        values: Array<VALUE>;
        /**
    Abort signal for cancelling the operation.
         */
        abortSignal?: AbortSignal;
        /**
      Additional HTTP headers to be sent with the request.
      Only applicable for HTTP-based providers.
         */
        headers?: Record<string, string | undefined>;
    }): PromiseLike<{
        /**
    Generated embeddings. They are in the same order as the input values.
         */
        embeddings: Array<EmbeddingModelV1Embedding>;
        /**
    Token usage. We only have input tokens for embeddings.
        */
        usage?: {
            tokens: number;
        };
        /**
    Optional raw response information for debugging purposes.
         */
        rawResponse?: {
            /**
      Response headers.
             */
            headers?: Record<string, string>;
        };
    }>;
};

declare class APICallError extends Error {
    readonly url: string;
    readonly requestBodyValues: unknown;
    readonly statusCode?: number;
    readonly responseHeaders?: Record<string, string>;
    readonly responseBody?: string;
    readonly cause?: unknown;
    readonly isRetryable: boolean;
    readonly data?: unknown;
    constructor({ message, url, requestBodyValues, statusCode, responseHeaders, responseBody, cause, isRetryable, // server error
    data, }: {
        message: string;
        url: string;
        requestBodyValues: unknown;
        statusCode?: number;
        responseHeaders?: Record<string, string>;
        responseBody?: string;
        cause?: unknown;
        isRetryable?: boolean;
        data?: unknown;
    });
    static isAPICallError(error: unknown): error is APICallError;
    toJSON(): {
        name: string;
        message: string;
        url: string;
        requestBodyValues: unknown;
        statusCode: number | undefined;
        responseHeaders: Record<string, string> | undefined;
        responseBody: string | undefined;
        cause: unknown;
        isRetryable: boolean;
        data: unknown;
    };
}

declare class DownloadError extends Error {
    readonly url: string;
    readonly statusCode?: number;
    readonly statusText?: string;
    readonly cause?: unknown;
    constructor({ url, statusCode, statusText, cause, message, }: {
        url: string;
        statusCode?: number;
        statusText?: string;
        message?: string;
        cause?: unknown;
    });
    static isDownloadError(error: unknown): error is DownloadError;
    toJSON(): {
        name: string;
        message: string;
        url: string;
        statusCode: number | undefined;
        statusText: string | undefined;
        cause: unknown;
    };
}

declare class EmptyResponseBodyError extends Error {
    constructor({ message }?: {
        message?: string;
    });
    static isEmptyResponseBodyError(error: unknown): error is EmptyResponseBodyError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
    };
}

declare class InvalidArgumentError extends Error {
    readonly parameter: string;
    readonly value: unknown;
    constructor({ parameter, value, message, }: {
        parameter: string;
        value: unknown;
        message: string;
    });
    static isInvalidArgumentError(error: unknown): error is InvalidArgumentError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        parameter: string;
        value: unknown;
    };
}

declare class InvalidDataContentError extends Error {
    readonly content: unknown;
    readonly cause?: unknown;
    constructor({ content, cause, message, }: {
        content: unknown;
        cause?: unknown;
        message?: string;
    });
    static isInvalidDataContentError(error: unknown): error is InvalidDataContentError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        cause: unknown;
        content: unknown;
    };
}

declare class InvalidPromptError extends Error {
    readonly prompt: unknown;
    constructor({ prompt, message }: {
        prompt: unknown;
        message: string;
    });
    static isInvalidPromptError(error: unknown): error is InvalidPromptError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        prompt: unknown;
    };
}

/**
Server returned a response with invalid data content. This should be thrown by providers when they
cannot parse the response from the API.
 */
declare class InvalidResponseDataError extends Error {
    readonly data: unknown;
    constructor({ data, message, }: {
        data: unknown;
        message?: string;
    });
    static isInvalidResponseDataError(error: unknown): error is InvalidResponseDataError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        data: unknown;
    };
}

declare class InvalidToolArgumentsError extends Error {
    readonly toolName: string;
    readonly toolArgs: string;
    readonly cause: unknown;
    constructor({ toolArgs, toolName, cause, message, }: {
        message?: string;
        toolArgs: string;
        toolName: string;
        cause: unknown;
    });
    static isInvalidToolArgumentsError(error: unknown): error is InvalidToolArgumentsError;
    toJSON(): {
        name: string;
        message: string;
        cause: unknown;
        stack: string | undefined;
        toolName: string;
        toolArgs: string;
    };
}

declare class JSONParseError extends Error {
    readonly text: string;
    readonly cause: unknown;
    constructor({ text, cause }: {
        text: string;
        cause: unknown;
    });
    static isJSONParseError(error: unknown): error is JSONParseError;
    toJSON(): {
        name: string;
        message: string;
        cause: unknown;
        stack: string | undefined;
        valueText: string;
    };
}

declare class LoadAPIKeyError extends Error {
    constructor({ message }: {
        message: string;
    });
    static isLoadAPIKeyError(error: unknown): error is LoadAPIKeyError;
    toJSON(): {
        name: string;
        message: string;
    };
}

declare class LoadSettingError extends Error {
    constructor({ message }: {
        message: string;
    });
    static isLoadSettingError(error: unknown): error is LoadSettingError;
    toJSON(): {
        name: string;
        message: string;
    };
}

/**
Thrown when the AI provider fails to generate any content.
 */
declare class NoContentGeneratedError extends Error {
    readonly cause: unknown;
    constructor({ message, }?: {
        message?: string;
    });
    static isNoContentGeneratedError(error: unknown): error is NoContentGeneratedError;
    toJSON(): {
        name: string;
        cause: unknown;
        message: string;
        stack: string | undefined;
    };
}

/**
Thrown when the AI provider fails to generate a parsable object.
 */
declare class NoObjectGeneratedError extends Error {
    readonly cause: unknown;
    constructor({ message }?: {
        message?: string;
    });
    static isNoObjectGeneratedError(error: unknown): error is NoObjectGeneratedError;
    toJSON(): {
        name: string;
        cause: unknown;
        message: string;
        stack: string | undefined;
    };
}

declare class NoSuchToolError extends Error {
    readonly toolName: string;
    readonly availableTools: string[] | undefined;
    constructor({ toolName, availableTools, message, }: {
        toolName: string;
        availableTools?: string[] | undefined;
        message?: string;
    });
    static isNoSuchToolError(error: unknown): error is NoSuchToolError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        toolName: string;
        availableTools: string[] | undefined;
    };
}

type RetryErrorReason = 'maxRetriesExceeded' | 'errorNotRetryable' | 'abort';
declare class RetryError extends Error {
    readonly reason: RetryErrorReason;
    readonly lastError: unknown;
    readonly errors: Array<unknown>;
    constructor({ message, reason, errors, }: {
        message: string;
        reason: RetryErrorReason;
        errors: Array<unknown>;
    });
    static isRetryError(error: unknown): error is RetryError;
    toJSON(): {
        name: string;
        message: string;
        reason: RetryErrorReason;
        lastError: unknown;
        errors: unknown[];
    };
}

declare class TooManyEmbeddingValuesForCallError extends Error {
    readonly provider: string;
    readonly modelId: string;
    readonly maxEmbeddingsPerCall: number;
    readonly values: Array<unknown>;
    constructor(options: {
        provider: string;
        modelId: string;
        maxEmbeddingsPerCall: number;
        values: Array<unknown>;
    });
    static isInvalidPromptError(error: unknown): error is TooManyEmbeddingValuesForCallError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        provider: string;
        modelId: string;
        maxEmbeddingsPerCall: number;
        values: unknown[];
    };
}

/**
A tool has a name, a description, and a set of parameters.

Note: this is **not** the user-facing tool definition. The AI SDK methods will
map the user-facing tool definitions to this format.
 */
type LanguageModelV1FunctionTool = {
    /**
  The type of the tool. Only functions for now, but this gives us room to
  add more specific tool types in the future and use a discriminated union.
     */
    type: 'function';
    /**
  The name of the tool. Unique within this model call.
     */
    name: string;
    /**
  A description of the tool. The language model uses this to understand the
  tool's purpose and to provide better completion suggestions.
     */
    description?: string;
    /**
  The parameters that the tool expects. The language model uses this to
  understand the tool's input requirements and to provide matching suggestions.
     */
    parameters: JSONSchema7;
};

declare class ToolCallParseError extends Error {
    readonly cause: unknown;
    readonly text: string;
    readonly tools: LanguageModelV1FunctionTool[];
    constructor({ cause, text, tools, message, }: {
        cause: unknown;
        text: string;
        tools: LanguageModelV1FunctionTool[];
        message?: string;
    });
    static isToolCallParseError(error: unknown): error is ToolCallParseError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        cause: unknown;
        text: string;
        tools: LanguageModelV1FunctionTool[];
    };
}

declare class TypeValidationError extends Error {
    readonly value: unknown;
    readonly cause: unknown;
    constructor({ value, cause }: {
        value: unknown;
        cause: unknown;
    });
    static isTypeValidationError(error: unknown): error is TypeValidationError;
    toJSON(): {
        name: string;
        message: string;
        cause: unknown;
        stack: string | undefined;
        value: unknown;
    };
}

declare class UnsupportedFunctionalityError extends Error {
    readonly functionality: string;
    constructor({ functionality }: {
        functionality: string;
    });
    static isUnsupportedFunctionalityError(error: unknown): error is UnsupportedFunctionalityError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        functionality: string;
    };
}

declare class UnsupportedJSONSchemaError extends Error {
    readonly reason: string;
    readonly schema: unknown;
    constructor({ schema, reason, message, }: {
        schema: unknown;
        reason: string;
        message?: string;
    });
    static isUnsupportedJSONSchemaError(error: unknown): error is UnsupportedJSONSchemaError;
    toJSON(): {
        name: string;
        message: string;
        stack: string | undefined;
        reason: string;
        schema: unknown;
    };
}

type LanguageModelV1CallSettings = {
    /**
  Maximum number of tokens to generate.
     */
    maxTokens?: number;
    /**
  Temperature setting.
  
  It is recommended to set either `temperature` or `topP`, but not both.
     */
    temperature?: number;
    /**
  Stop sequences.
  If set, the model will stop generating text when one of the stop sequences is generated.
  Providers may have limits on the number of stop sequences.
     */
    stopSequences?: string[];
    /**
  Nucleus sampling.
  
  It is recommended to set either `temperature` or `topP`, but not both.
     */
    topP?: number;
    /**
  Only sample from the top K options for each subsequent token.
  
  Used to remove "long tail" low probability responses.
  Recommended for advanced use cases only. You usually only need to use temperature.
     */
    topK?: number;
    /**
  Presence penalty setting. It affects the likelihood of the model to
  repeat information that is already in the prompt.
     */
    presencePenalty?: number;
    /**
  Frequency penalty setting. It affects the likelihood of the model
  to repeatedly use the same words or phrases.
     */
    frequencyPenalty?: number;
    /**
  Response format. The output can either be text or JSON. Default is text.
  
  If JSON is selected, a schema can optionally be provided to guide the LLM.
     */
    responseFormat?: {
        type: 'text';
    } | {
        type: 'json';
        schema?: JSONSchema7;
    };
    /**
  The seed (integer) to use for random sampling. If set and supported
  by the model, calls will generate deterministic results.
     */
    seed?: number;
    /**
  Abort signal for cancelling the operation.
     */
    abortSignal?: AbortSignal;
    /**
  Additional HTTP headers to be sent with the request.
  Only applicable for HTTP-based providers.
     */
    headers?: Record<string, string | undefined>;
};

/**
A prompt is a list of messages.

Note: Not all models and prompt formats support multi-modal inputs and
tool calls. The validation happens at runtime.

Note: This is not a user-facing prompt. The AI SDK methods will map the
user-facing prompt types such as chat or instruction prompts to this format.
 */
type LanguageModelV1Prompt = Array<LanguageModelV1Message>;
type LanguageModelV1Message = {
    role: 'system';
    content: string;
} | {
    role: 'user';
    content: Array<LanguageModelV1TextPart | LanguageModelV1ImagePart>;
} | {
    role: 'assistant';
    content: Array<LanguageModelV1TextPart | LanguageModelV1ToolCallPart>;
} | {
    role: 'tool';
    content: Array<LanguageModelV1ToolResultPart>;
};
/**
Text content part of a prompt. It contains a string of text.
 */
interface LanguageModelV1TextPart {
    type: 'text';
    /**
  The text content.
     */
    text: string;
}
/**
Image content part of a prompt. It contains an image.
 */
interface LanguageModelV1ImagePart {
    type: 'image';
    /**
  Image data as a Uint8Array (e.g. from a Blob or Buffer) or a URL.
     */
    image: Uint8Array | URL;
    /**
  Optional mime type of the image.
     */
    mimeType?: string;
}
/**
Tool call content part of a prompt. It contains a tool call (usually generated by the AI model).
 */
interface LanguageModelV1ToolCallPart {
    type: 'tool-call';
    /**
  ID of the tool call. This ID is used to match the tool call with the tool result.
   */
    toolCallId: string;
    /**
  Name of the tool that is being called.
   */
    toolName: string;
    /**
  Arguments of the tool call. This is a JSON-serializable object that matches the tool's input schema.
     */
    args: unknown;
}
/**
Tool result content part of a prompt. It contains the result of the tool call with the matching ID.
 */
interface LanguageModelV1ToolResultPart {
    type: 'tool-result';
    /**
  ID of the tool call that this result is associated with.
   */
    toolCallId: string;
    /**
  Name of the tool that generated this result.
    */
    toolName: string;
    /**
  Result of the tool call. This is a JSON-serializable object.
     */
    result: unknown;
    /**
  Optional flag if the result is an error or an error message.
     */
    isError?: boolean;
}

type LanguageModelV1ToolChoice = {
    type: 'auto';
} | {
    type: 'none';
} | {
    type: 'required';
} | {
    type: 'tool';
    toolName: string;
};

type LanguageModelV1CallOptions = LanguageModelV1CallSettings & {
    /**
  Whether the user provided the input as messages or as
  a prompt. This can help guide non-chat models in the
  expansion, bc different expansions can be needed for
  chat/non-chat use cases.
     */
    inputFormat: 'messages' | 'prompt';
    /**
  The mode affects the behavior of the language model. It is required to
  support provider-independent streaming and generation of structured objects.
  The model can take this information and e.g. configure json mode, the correct
  low level grammar, etc. It can also be used to optimize the efficiency of the
  streaming, e.g. tool-delta stream parts are only needed in the
  object-tool mode.
  
  @deprecated mode will be removed in v2.
  All necessary settings will be directly supported through the call settings.
     */
    mode: {
        type: 'regular';
        /**
The tools that are available for the model.
         */
        tools?: Array<LanguageModelV1FunctionTool>;
        /**
Specifies how the tool should be selected. Defaults to 'auto'.
         */
        toolChoice?: LanguageModelV1ToolChoice;
    } | {
        type: 'object-json';
    } | {
        type: 'object-tool';
        tool: LanguageModelV1FunctionTool;
    };
    /**
  A language mode prompt is a standardized prompt type.
  
  Note: This is **not** the user-facing prompt. The AI SDK methods will map the
  user-facing prompt types such as chat or instruction prompts to this format.
  That approach allows us to evolve the user  facing prompts without breaking
  the language model interface.
     */
    prompt: LanguageModelV1Prompt;
};

/**
Warning from the model provider for this call. The call will proceed, but e.g.
some settings might not be supported, which can lead to suboptimal results.
 */
type LanguageModelV1CallWarning = {
    type: 'unsupported-setting';
    setting: keyof LanguageModelV1CallSettings;
    details?: string;
} | {
    type: 'other';
    message: string;
};

/**
Reason why a language model finished generating a response.

Can be one of the following:
- `stop`: model generated stop sequence
- `length`: model generated maximum number of tokens
- `content-filter`: content filter violation stopped the model
- `tool-calls`: model triggered tool calls
- `error`: model stopped because of an error
- `other`: model stopped for other reasons
- `unknown`: the model has not transmitted a finish reason
 */
type LanguageModelV1FinishReason = 'stop' | 'length' | 'content-filter' | 'tool-calls' | 'error' | 'other' | 'unknown';

type LanguageModelV1FunctionToolCall = {
    toolCallType: 'function';
    toolCallId: string;
    toolName: string;
    /**
  Stringified JSON object with the tool call arguments. Must match the
  parameters schema of the tool.
     */
    args: string;
};

/**
Log probabilities for each token and its top log probabilities.
 */
type LanguageModelV1LogProbs = Array<{
    token: string;
    logprob: number;
    topLogprobs: Array<{
        token: string;
        logprob: number;
    }>;
}>;

/**
Specification for a language model that implements the language model interface version 1.
 */
type LanguageModelV1 = {
    /**
  The language model must specify which language model interface
  version it implements. This will allow us to evolve the language
  model interface and retain backwards compatibility. The different
  implementation versions can be handled as a discriminated union
  on our side.
     */
    readonly specificationVersion: 'v1';
    /**
  Name of the provider for logging purposes.
     */
    readonly provider: string;
    /**
  Provider-specific model ID for logging purposes.
     */
    readonly modelId: string;
    /**
  Default object generation mode that should be used with this model when
  no mode is specified. Should be the mode with the best results for this
  model. `undefined` can be returned if object generation is not supported.
  
  This is needed to generate the best objects possible w/o requiring the
  user to explicitly specify the object generation mode.
     */
    readonly defaultObjectGenerationMode: 'json' | 'tool' | undefined;
    /**
  Flag whether this model supports image URLs. Default is `true`.
  
  When the flag is set to `false`, the AI SDK will download the image and
  pass the image data to the model.
     */
    readonly supportsImageUrls?: boolean;
    /**
  Generates a language model output (non-streaming).
  
  Naming: "do" prefix to prevent accidental direct usage of the method
  by the user.
     */
    doGenerate(options: LanguageModelV1CallOptions): PromiseLike<{
        /**
    Text that the model has generated. Can be undefined if the model
    has only generated tool calls.
         */
        text?: string;
        /**
    Tool calls that the model has generated. Can be undefined if the
    model has only generated text.
         */
        toolCalls?: Array<LanguageModelV1FunctionToolCall>;
        /**
    Finish reason.
         */
        finishReason: LanguageModelV1FinishReason;
        /**
      Usage information.
         */
        usage: {
            promptTokens: number;
            completionTokens: number;
        };
        /**
    Raw prompt and setting information for observability provider integration.
         */
        rawCall: {
            /**
      Raw prompt after expansion and conversion to the format that the
      provider uses to send the information to their API.
             */
            rawPrompt: unknown;
            /**
      Raw settings that are used for the API call. Includes provider-specific
      settings.
             */
            rawSettings: Record<string, unknown>;
        };
        /**
    Optional raw response information for debugging purposes.
         */
        rawResponse?: {
            /**
      Response headers.
            */
            headers?: Record<string, string>;
        };
        warnings?: LanguageModelV1CallWarning[];
        /**
      Logprobs for the completion.
      `undefined` if the mode does not support logprobs or if was not enabled
         */
        logprobs?: LanguageModelV1LogProbs;
    }>;
    /**
  Generates a language model output (streaming).
  
  Naming: "do" prefix to prevent accidental direct usage of the method
  by the user.
     *
  @return A stream of higher-level language model output parts.
     */
    doStream(options: LanguageModelV1CallOptions): PromiseLike<{
        stream: ReadableStream<LanguageModelV1StreamPart>;
        /**
    Raw prompt and setting information for observability provider integration.
         */
        rawCall: {
            /**
      Raw prompt after expansion and conversion to the format that the
      provider uses to send the information to their API.
             */
            rawPrompt: unknown;
            /**
      Raw settings that are used for the API call. Includes provider-specific
      settings.
             */
            rawSettings: Record<string, unknown>;
        };
        /**
    Optional raw response data.
         */
        rawResponse?: {
            /**
      Response headers.
             */
            headers?: Record<string, string>;
        };
        warnings?: LanguageModelV1CallWarning[];
    }>;
};
type LanguageModelV1StreamPart = {
    type: 'text-delta';
    textDelta: string;
} | ({
    type: 'tool-call';
} & LanguageModelV1FunctionToolCall) | {
    type: 'tool-call-delta';
    toolCallType: 'function';
    toolCallId: string;
    toolName: string;
    argsTextDelta: string;
} | {
    type: 'finish';
    finishReason: LanguageModelV1FinishReason;
    logprobs?: LanguageModelV1LogProbs;
    usage: {
        promptTokens: number;
        completionTokens: number;
    };
} | {
    type: 'error';
    error: unknown;
};
type LanguageModelV1ResponseMetadata = {};

export { APICallError, DownloadError, type EmbeddingModelV1, type EmbeddingModelV1Embedding, EmptyResponseBodyError, InvalidArgumentError, InvalidDataContentError, InvalidPromptError, InvalidResponseDataError, InvalidToolArgumentsError, JSONParseError, type LanguageModelV1, type LanguageModelV1CallOptions, type LanguageModelV1CallWarning, type LanguageModelV1FinishReason, type LanguageModelV1FunctionTool, type LanguageModelV1FunctionToolCall, type LanguageModelV1ImagePart, type LanguageModelV1LogProbs, type LanguageModelV1Message, type LanguageModelV1Prompt, type LanguageModelV1ResponseMetadata, type LanguageModelV1StreamPart, type LanguageModelV1TextPart, type LanguageModelV1ToolCallPart, type LanguageModelV1ToolChoice, type LanguageModelV1ToolResultPart, LoadAPIKeyError, LoadSettingError, NoContentGeneratedError, NoObjectGeneratedError, NoSuchToolError, RetryError, type RetryErrorReason, TooManyEmbeddingValuesForCallError, ToolCallParseError, TypeValidationError, UnsupportedFunctionalityError, UnsupportedJSONSchemaError };
