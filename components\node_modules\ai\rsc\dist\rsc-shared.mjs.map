{"version": 3, "sources": ["../shared-client/streamable.tsx", "../constants.ts", "../shared-client/context.tsx", "../utils.tsx"], "sourcesContent": ["import { startTransition, useLayoutEffect, useState } from 'react';\nimport { STREAMABLE_VALUE_TYPE } from '../constants';\nimport type { StreamableValue } from '../types';\n\nfunction hasReadableValueSignature(value: unknown): value is StreamableValue {\n  return !!(\n    value &&\n    typeof value === 'object' &&\n    'type' in value &&\n    value.type === STREAMABLE_VALUE_TYPE\n  );\n}\n\nfunction assertStreamableValue(\n  value: unknown,\n): asserts value is StreamableValue {\n  if (!hasReadableValueSignature(value)) {\n    throw new Error(\n      'Invalid value: this hook only accepts values created via `createStreamableValue`.',\n    );\n  }\n}\n\nfunction isStreamableValue(value: unknown): value is StreamableValue {\n  const hasSignature = hasReadableValueSignature(value);\n\n  if (!hasSignature && typeof value !== 'undefined') {\n    throw new Error(\n      'Invalid value: this hook only accepts values created via `createStreamableValue`.',\n    );\n  }\n\n  return hasSignature;\n}\n\n/**\n * `readStreamableValue` takes a streamable value created via the `createStreamableValue().value` API,\n * and returns an async iterator.\n *\n * ```js\n * // Inside your AI action:\n *\n * async function action() {\n *   'use server'\n *   const streamable = createStreamableValue();\n *\n *   streamable.update(1);\n *   streamable.update(2);\n *   streamable.done(3);\n *   // ...\n *   return streamable.value;\n * }\n * ```\n *\n * And to read the value:\n *\n * ```js\n * const streamableValue = await action()\n * for await (const v of readStreamableValue(streamableValue)) {\n *   console.log(v)\n * }\n * ```\n *\n * This logs out 1, 2, 3 on console.\n */\nexport function readStreamableValue<T = unknown>(\n  streamableValue: StreamableValue<T>,\n): AsyncIterable<T | undefined> {\n  assertStreamableValue(streamableValue);\n\n  return {\n    [Symbol.asyncIterator]() {\n      let row: StreamableValue<T> | Promise<StreamableValue<T>> =\n        streamableValue;\n      let curr = row.curr;\n      let done = false;\n      let initial = true;\n\n      return {\n        async next() {\n          if (done) return { value: curr, done: true };\n\n          row = await row;\n\n          if (typeof row.error !== 'undefined') {\n            throw row.error;\n          }\n          if ('curr' in row || row.diff) {\n            if (row.diff) {\n              switch (row.diff[0]) {\n                case 0:\n                  if (typeof curr !== 'string') {\n                    throw new Error(\n                      'Invalid patch: can only append to string types. This is a bug in the AI SDK.',\n                    );\n                  } else {\n                    (curr as string) = curr + row.diff[1];\n                  }\n                  break;\n              }\n            } else {\n              curr = row.curr;\n            }\n\n            // The last emitted { done: true } won't be used as the value\n            // by the for await...of syntax.\n            if (!row.next) {\n              done = true;\n              return {\n                value: curr,\n                done: false,\n              };\n            }\n          }\n\n          if (!row.next) {\n            return {\n              value: curr,\n              done: true,\n            };\n          }\n\n          row = row.next;\n          if (initial) {\n            initial = false;\n            if (typeof curr === 'undefined') {\n              // This is the initial chunk and there isn't an initial value yet.\n              // Let's skip this one.\n              return this.next();\n            }\n          }\n\n          return {\n            value: curr,\n            done: false,\n          };\n        },\n      };\n    },\n  };\n}\n\n/**\n * `useStreamableValue` is a React hook that takes a streamable value created via the `createStreamableValue().value` API,\n * and returns the current value, error, and pending state.\n *\n * This is useful for consuming streamable values received from a component's props. For example:\n *\n * ```js\n * function MyComponent({ streamableValue }) {\n *   const [data, error, pending] = useStreamableValue(streamableValue);\n *\n *   if (pending) return <div>Loading...</div>;\n *   if (error) return <div>Error: {error.message}</div>;\n *\n *   return <div>Data: {data}</div>;\n * }\n * ```\n */\nexport function useStreamableValue<T = unknown, Error = unknown>(\n  streamableValue?: StreamableValue<T>,\n): [data: T | undefined, error: Error | undefined, pending: boolean] {\n  const [curr, setCurr] = useState<T | undefined>(\n    isStreamableValue(streamableValue) ? streamableValue.curr : undefined,\n  );\n  const [error, setError] = useState<Error | undefined>(\n    isStreamableValue(streamableValue) ? streamableValue.error : undefined,\n  );\n  const [pending, setPending] = useState<boolean>(\n    isStreamableValue(streamableValue) ? !!streamableValue.next : false,\n  );\n\n  useLayoutEffect(() => {\n    if (!isStreamableValue(streamableValue)) return;\n\n    let cancelled = false;\n\n    const iterator = readStreamableValue(streamableValue);\n    if (streamableValue.next) {\n      startTransition(() => {\n        if (cancelled) return;\n        setPending(true);\n      });\n    }\n\n    (async () => {\n      try {\n        for await (const value of iterator) {\n          if (cancelled) return;\n          startTransition(() => {\n            if (cancelled) return;\n            setCurr(value);\n          });\n        }\n      } catch (e) {\n        if (cancelled) return;\n        startTransition(() => {\n          if (cancelled) return;\n          setError(e as Error);\n        });\n      } finally {\n        if (cancelled) return;\n        startTransition(() => {\n          if (cancelled) return;\n          setPending(false);\n        });\n      }\n    })();\n\n    return () => {\n      cancelled = true;\n    };\n  }, [streamableValue]);\n\n  return [curr, error, pending];\n}\n", "export const STREAMABLE_VALUE_TYPE = Symbol.for('ui.streamable.value');\nexport const DEV_DEFAULT_STREAMABLE_WARNING_TIME = 15 * 1000;\n", "/* eslint-disable react-hooks/exhaustive-deps */\n'use client';\n\nimport * as React from 'react';\n\nimport * as jsondiffpatch from 'jsondiffpatch';\nimport type {\n  InternalAIProviderProps,\n  AIProvider,\n  InferAIState,\n  ValueOrUpdater,\n  InferActions,\n  InferUIState,\n} from '../types';\nimport { isFunction } from '../utils';\n\nconst InternalUIStateProvider = React.createContext<null | any>(null);\nconst InternalAIStateProvider = React.createContext<undefined | any>(undefined);\nconst InternalActionProvider = React.createContext<null | any>(null);\nconst InternalSyncUIStateProvider = React.createContext<null | any>(null);\n\nexport function InternalAIProvider({\n  children,\n  initialUIState,\n  initialAIState,\n  initialAIStatePatch,\n  wrappedActions,\n  wrappedSyncUIState,\n}: InternalAIProviderProps) {\n  if (!('use' in React)) {\n    throw new Error('Unsupported React version.');\n  }\n\n  const uiState = React.useState(initialUIState);\n  const setUIState = uiState[1];\n\n  const resolvedInitialAIStatePatch = initialAIStatePatch\n    ? (React as any).use(initialAIStatePatch)\n    : undefined;\n  initialAIState = React.useMemo(() => {\n    if (resolvedInitialAIStatePatch) {\n      return jsondiffpatch.patch(\n        jsondiffpatch.clone(initialAIState),\n        resolvedInitialAIStatePatch,\n      );\n    }\n    return initialAIState;\n  }, [initialAIState, resolvedInitialAIStatePatch]);\n\n  const aiState = React.useState(initialAIState);\n  const setAIState = aiState[1];\n  const aiStateRef = React.useRef(aiState[0]);\n\n  React.useEffect(() => {\n    aiStateRef.current = aiState[0];\n  }, [aiState[0]]);\n\n  const clientWrappedActions = React.useMemo(\n    () =>\n      Object.fromEntries(\n        Object.entries(wrappedActions).map(([key, action]) => [\n          key,\n          async (...args: any) => {\n            const aiStateSnapshot = aiStateRef.current;\n            const [aiStateDelta, result] = await action(\n              aiStateSnapshot,\n              ...args,\n            );\n            (async () => {\n              const delta = await aiStateDelta;\n              if (delta !== undefined) {\n                aiState[1](\n                  jsondiffpatch.patch(\n                    jsondiffpatch.clone(aiStateSnapshot),\n                    delta,\n                  ),\n                );\n              }\n            })();\n            return result;\n          },\n        ]),\n      ),\n    [wrappedActions],\n  );\n\n  const clientWrappedSyncUIStateAction = React.useMemo(() => {\n    if (!wrappedSyncUIState) {\n      return () => {};\n    }\n\n    return async () => {\n      const aiStateSnapshot = aiStateRef.current;\n      const [aiStateDelta, uiState] = await wrappedSyncUIState!(\n        aiStateSnapshot,\n      );\n\n      if (uiState !== undefined) {\n        setUIState(uiState);\n      }\n\n      const delta = await aiStateDelta;\n      if (delta !== undefined) {\n        const patchedAiState = jsondiffpatch.patch(\n          jsondiffpatch.clone(aiStateSnapshot),\n          delta,\n        );\n        setAIState(patchedAiState);\n      }\n    };\n  }, [wrappedSyncUIState]);\n\n  return (\n    <InternalAIStateProvider.Provider value={aiState}>\n      <InternalUIStateProvider.Provider value={uiState}>\n        <InternalActionProvider.Provider value={clientWrappedActions}>\n          <InternalSyncUIStateProvider.Provider\n            value={clientWrappedSyncUIStateAction}\n          >\n            {children}\n          </InternalSyncUIStateProvider.Provider>\n        </InternalActionProvider.Provider>\n      </InternalUIStateProvider.Provider>\n    </InternalAIStateProvider.Provider>\n  );\n}\n\nexport function useUIState<AI extends AIProvider = any>() {\n  type T = InferUIState<AI, any>;\n\n  const state = React.useContext<\n    [T, (v: T | ((v_: T) => T)) => void] | null | undefined\n  >(InternalUIStateProvider);\n  if (state === null) {\n    throw new Error('`useUIState` must be used inside an <AI> provider.');\n  }\n  if (!Array.isArray(state)) {\n    throw new Error('Invalid state');\n  }\n  if (state[0] === undefined) {\n    throw new Error(\n      '`initialUIState` must be provided to `createAI` or `<AI>`',\n    );\n  }\n  return state;\n}\n\n// TODO: How do we avoid causing a re-render when the AI state changes but you\n// are only listening to a specific key? We need useSES perhaps?\nfunction useAIState<AI extends AIProvider = any>(): [\n  InferAIState<AI, any>,\n  (newState: ValueOrUpdater<InferAIState<AI, any>>) => void,\n];\nfunction useAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): [\n  InferAIState<AI, any>[typeof key],\n  (newState: ValueOrUpdater<InferAIState<AI, any>[typeof key]>) => void,\n];\nfunction useAIState<AI extends AIProvider = any>(\n  ...args: [] | [keyof InferAIState<AI, any>]\n) {\n  type T = InferAIState<AI, any>;\n\n  const state = React.useContext<\n    [T, (newState: ValueOrUpdater<T>) => void] | null | undefined\n  >(InternalAIStateProvider);\n  if (state === null) {\n    throw new Error('`useAIState` must be used inside an <AI> provider.');\n  }\n  if (!Array.isArray(state)) {\n    throw new Error('Invalid state');\n  }\n  if (state[0] === undefined) {\n    throw new Error(\n      '`initialAIState` must be provided to `createAI` or `<AI>`',\n    );\n  }\n  if (args.length >= 1 && typeof state[0] !== 'object') {\n    throw new Error(\n      'When using `useAIState` with a key, the AI state must be an object.',\n    );\n  }\n\n  const key = args[0];\n  const setter = React.useCallback(\n    typeof key === 'undefined'\n      ? state[1]\n      : (newState: ValueOrUpdater<T>) => {\n          if (isFunction(newState)) {\n            return state[1](s => {\n              return { ...s, [key]: newState(s[key]) };\n            });\n          } else {\n            return state[1]({ ...state[0], [key]: newState });\n          }\n        },\n    [key],\n  );\n\n  if (args.length === 0) {\n    return state;\n  } else {\n    return [state[0][args[0]], setter];\n  }\n}\n\nexport function useActions<AI extends AIProvider = any>() {\n  type T = InferActions<AI, any>;\n\n  const actions = React.useContext<T>(InternalActionProvider);\n  return actions;\n}\n\nexport function useSyncUIState() {\n  const syncUIState = React.useContext<() => Promise<void>>(\n    InternalSyncUIStateProvider,\n  );\n\n  if (syncUIState === null) {\n    throw new Error('`useSyncUIState` must be used inside an <AI> provider.');\n  }\n\n  return syncUIState;\n}\n\nexport { useAIState };\n", "import React, { Suspense } from 'react';\n\nexport function createResolvablePromise<T = any>() {\n  let resolve: (value: T) => void, reject: (error: unknown) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  };\n}\n\n// Use the name `R` for `Row` as it will be shorter in the RSC payload.\nconst R = [\n  (async ({\n    c, // current\n    n, // next\n  }: {\n    c: React.ReactNode;\n    n: Promise<any>;\n  }) => {\n    const chunk = await n;\n    if (chunk.done) {\n      return chunk.value;\n    }\n\n    if (chunk.append) {\n      return (\n        <>\n          {c}\n          <Suspense fallback={chunk.value}>\n            <R c={chunk.value} n={chunk.next} />\n          </Suspense>\n        </>\n      );\n    }\n\n    return (\n      <Suspense fallback={chunk.value}>\n        <R c={chunk.value} n={chunk.next} />\n      </Suspense>\n    );\n  }) as unknown as React.FC<{\n    c: React.ReactNode;\n    n: Promise<any>;\n  }>,\n][0];\n\nexport function createSuspensedChunk(initialValue: React.ReactNode) {\n  const { promise, resolve, reject } = createResolvablePromise();\n\n  return {\n    row: (\n      <Suspense fallback={initialValue}>\n        <R c={initialValue} n={promise} />\n      </Suspense>\n    ) as React.ReactNode,\n    resolve,\n    reject,\n  };\n}\n\nexport const isFunction = (x: unknown): x is Function =>\n  typeof x === 'function';\n\nexport const consumeStream = async (stream: ReadableStream) => {\n  const reader = stream.getReader();\n  while (true) {\n    const { done } = await reader.read();\n    if (done) break;\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,iBAAiB,iBAAiB,gBAAgB;;;ACApD,IAAM,wBAAwB,OAAO,IAAI,qBAAqB;AAC9D,IAAM,sCAAsC,KAAK;;;ADGxD,SAAS,0BAA0B,OAA0C;AAC3E,SAAO,CAAC,EACN,SACA,OAAO,UAAU,YACjB,UAAU,SACV,MAAM,SAAS;AAEnB;AAEA,SAAS,sBACP,OACkC;AAClC,MAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,OAA0C;AACnE,QAAM,eAAe,0BAA0B,KAAK;AAEpD,MAAI,CAAC,gBAAgB,OAAO,UAAU,aAAa;AACjD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAgCO,SAAS,oBACd,iBAC8B;AAC9B,wBAAsB,eAAe;AAErC,SAAO;AAAA,IACL,CAAC,OAAO,aAAa,IAAI;AACvB,UAAI,MACF;AACF,UAAI,OAAO,IAAI;AACf,UAAI,OAAO;AACX,UAAI,UAAU;AAEd,aAAO;AAAA,QACL,MAAM,OAAO;AACX,cAAI;AAAM,mBAAO,EAAE,OAAO,MAAM,MAAM,KAAK;AAE3C,gBAAM,MAAM;AAEZ,cAAI,OAAO,IAAI,UAAU,aAAa;AACpC,kBAAM,IAAI;AAAA,UACZ;AACA,cAAI,UAAU,OAAO,IAAI,MAAM;AAC7B,gBAAI,IAAI,MAAM;AACZ,sBAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,gBACnB,KAAK;AACH,sBAAI,OAAO,SAAS,UAAU;AAC5B,0BAAM,IAAI;AAAA,sBACR;AAAA,oBACF;AAAA,kBACF,OAAO;AACL,oBAAC,OAAkB,OAAO,IAAI,KAAK,CAAC;AAAA,kBACtC;AACA;AAAA,cACJ;AAAA,YACF,OAAO;AACL,qBAAO,IAAI;AAAA,YACb;AAIA,gBAAI,CAAC,IAAI,MAAM;AACb,qBAAO;AACP,qBAAO;AAAA,gBACL,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,IAAI,MAAM;AACb,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAEA,gBAAM,IAAI;AACV,cAAI,SAAS;AACX,sBAAU;AACV,gBAAI,OAAO,SAAS,aAAa;AAG/B,qBAAO,KAAK,KAAK;AAAA,YACnB;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAmBO,SAAS,mBACd,iBACmE;AACnE,QAAM,CAAC,MAAM,OAAO,IAAI;AAAA,IACtB,kBAAkB,eAAe,IAAI,gBAAgB,OAAO;AAAA,EAC9D;AACA,QAAM,CAAC,OAAO,QAAQ,IAAI;AAAA,IACxB,kBAAkB,eAAe,IAAI,gBAAgB,QAAQ;AAAA,EAC/D;AACA,QAAM,CAAC,SAAS,UAAU,IAAI;AAAA,IAC5B,kBAAkB,eAAe,IAAI,CAAC,CAAC,gBAAgB,OAAO;AAAA,EAChE;AAEA,kBAAgB,MAAM;AACpB,QAAI,CAAC,kBAAkB,eAAe;AAAG;AAEzC,QAAI,YAAY;AAEhB,UAAM,WAAW,oBAAoB,eAAe;AACpD,QAAI,gBAAgB,MAAM;AACxB,sBAAgB,MAAM;AACpB,YAAI;AAAW;AACf,mBAAW,IAAI;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,KAAC,YAAY;AACX,UAAI;AACF,yBAAiB,SAAS,UAAU;AAClC,cAAI;AAAW;AACf,0BAAgB,MAAM;AACpB,gBAAI;AAAW;AACf,oBAAQ,KAAK;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF,SAAS,GAAG;AACV,YAAI;AAAW;AACf,wBAAgB,MAAM;AACpB,cAAI;AAAW;AACf,mBAAS,CAAU;AAAA,QACrB,CAAC;AAAA,MACH,UAAE;AACA,YAAI;AAAW;AACf,wBAAgB,MAAM;AACpB,cAAI;AAAW;AACf,qBAAW,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AAEH,WAAO,MAAM;AACX,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,SAAO,CAAC,MAAM,OAAO,OAAO;AAC9B;;;AEpNA,YAAYA,YAAW;AAEvB,YAAY,mBAAmB;;;ACL/B,SAAgB,gBAAgB;AA+BxB,mBAGI,KAHJ;AAfR,IAAM,IAAI;AAAA,EACP,OAAO;AAAA,IACN;AAAA;AAAA,IACA;AAAA;AAAA,EACF,MAGM;AACJ,UAAM,QAAQ,MAAM;AACpB,QAAI,MAAM,MAAM;AACd,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,MAAM,QAAQ;AAChB,aACE,iCACG;AAAA;AAAA,QACD,oBAAC,YAAS,UAAU,MAAM,OACxB,8BAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,SACF;AAAA,IAEJ;AAEA,WACE,oBAAC,YAAS,UAAU,MAAM,OACxB,8BAAC,KAAE,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GACpC;AAAA,EAEJ;AAIF,EAAE,CAAC;AAgBI,IAAM,aAAa,CAAC,MACzB,OAAO,MAAM;;;ADkDL,gBAAAC,YAAA;AApGV,IAAM,0BAAgC,qBAA0B,IAAI;AACpE,IAAM,0BAAgC,qBAA+B,MAAS;AAC9E,IAAM,yBAA+B,qBAA0B,IAAI;AACnE,IAAM,8BAAoC,qBAA0B,IAAI;AAEjE,SAAS,mBAAmB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA4B;AAC1B,MAAI,EAAE,SAASC,SAAQ;AACrB,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AAEA,QAAM,UAAgB,gBAAS,cAAc;AAC7C,QAAM,aAAa,QAAQ,CAAC;AAE5B,QAAM,8BAA8B,sBACjB,WAAI,mBAAmB,IACtC;AACJ,mBAAuB,eAAQ,MAAM;AACnC,QAAI,6BAA6B;AAC/B,aAAqB;AAAA,QACL,oBAAM,cAAc;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,gBAAgB,2BAA2B,CAAC;AAEhD,QAAM,UAAgB,gBAAS,cAAc;AAC7C,QAAM,aAAa,QAAQ,CAAC;AAC5B,QAAM,aAAmB,cAAO,QAAQ,CAAC,CAAC;AAE1C,EAAM,iBAAU,MAAM;AACpB,eAAW,UAAU,QAAQ,CAAC;AAAA,EAChC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AAEf,QAAM,uBAA6B;AAAA,IACjC,MACE,OAAO;AAAA,MACL,OAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,KAAK,MAAM,MAAM;AAAA,QACpD;AAAA,QACA,UAAU,SAAc;AACtB,gBAAM,kBAAkB,WAAW;AACnC,gBAAM,CAAC,cAAc,MAAM,IAAI,MAAM;AAAA,YACnC;AAAA,YACA,GAAG;AAAA,UACL;AACA,WAAC,YAAY;AACX,kBAAM,QAAQ,MAAM;AACpB,gBAAI,UAAU,QAAW;AACvB,sBAAQ,CAAC;AAAA,gBACO;AAAA,kBACE,oBAAM,eAAe;AAAA,kBACnC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,GAAG;AACH,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACF,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,iCAAuC,eAAQ,MAAM;AACzD,QAAI,CAAC,oBAAoB;AACvB,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AAEA,WAAO,YAAY;AACjB,YAAM,kBAAkB,WAAW;AACnC,YAAM,CAAC,cAAcC,QAAO,IAAI,MAAM;AAAA,QACpC;AAAA,MACF;AAEA,UAAIA,aAAY,QAAW;AACzB,mBAAWA,QAAO;AAAA,MACpB;AAEA,YAAM,QAAQ,MAAM;AACpB,UAAI,UAAU,QAAW;AACvB,cAAM,iBAA+B;AAAA,UACrB,oBAAM,eAAe;AAAA,UACnC;AAAA,QACF;AACA,mBAAW,cAAc;AAAA,MAC3B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,kBAAkB,CAAC;AAEvB,SACE,gBAAAF,KAAC,wBAAwB,UAAxB,EAAiC,OAAO,SACvC,0BAAAA,KAAC,wBAAwB,UAAxB,EAAiC,OAAO,SACvC,0BAAAA,KAAC,uBAAuB,UAAvB,EAAgC,OAAO,sBACtC,0BAAAA;AAAA,IAAC,4BAA4B;AAAA,IAA5B;AAAA,MACC,OAAO;AAAA,MAEN;AAAA;AAAA,EACH,GACF,GACF,GACF;AAEJ;AAEO,SAAS,aAA0C;AAGxD,QAAM,QAAc,kBAElB,uBAAuB;AACzB,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAcA,SAAS,cACJ,MACH;AAGA,QAAM,QAAc,kBAElB,uBAAuB;AACzB,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,UAAU,KAAK,OAAO,MAAM,CAAC,MAAM,UAAU;AACpD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,SAAe;AAAA,IACnB,OAAO,QAAQ,cACX,MAAM,CAAC,IACP,CAAC,aAAgC;AAC/B,UAAI,WAAW,QAAQ,GAAG;AACxB,eAAO,MAAM,CAAC,EAAE,OAAK;AACnB,iBAAO,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,eAAO,MAAM,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,IACJ,CAAC,GAAG;AAAA,EACN;AAEA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,MAAM;AAAA,EACnC;AACF;AAEO,SAAS,aAA0C;AAGxD,QAAM,UAAgB,kBAAc,sBAAsB;AAC1D,SAAO;AACT;AAEO,SAAS,iBAAiB;AAC/B,QAAM,cAAoB;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AAEA,SAAO;AACT;", "names": ["React", "jsx", "React", "uiState"]}