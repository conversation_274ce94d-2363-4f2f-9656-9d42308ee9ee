"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk";
exports.ids = ["vendor-chunks/@ai-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncGeneratorToReadableStream: () => (/* binding */ convertAsyncGeneratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isParseableJson: () => (/* binding */ isParseableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/secure-json-parse/index.js\");\n/* harmony import */ var eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! eventsource-parser/stream */ \"(ssr)/./node_modules/eventsource-parser/dist/stream.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-generator-to-readable-stream.ts\nfunction convertAsyncGeneratorToReadableStream(stream) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await stream.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\nvar generateId = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(\n  \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  7\n);\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const schema = isValidator(inputSchema) ? inputSchema : zodValidator(inputSchema);\n  try {\n    if (schema.validate == null) {\n      return { success: true, value };\n    }\n    const validationResult = schema.validate(value);\n    if (validationResult.success) {\n      return validationResult;\n    }\n    return {\n      success: false,\n      error: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError({\n        value,\n        cause: validationResult.error\n      })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isTypeValidationError(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isJSONParseError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isTypeValidationError(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return {\n        success: true,\n        value\n      };\n    }\n    return safeValidateTypes({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isJSONParseError(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nvar isParseableJson = isParsableJson;\n\n// src/post-to-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/post-to-api.ts\nvar getOriginalFetch = () => fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch: fetch2\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch: fetch2\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch: fetch2 = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch2(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isAPICallError(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isAPICallError(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/response-handler.ts\n\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(new eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_3__.EventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value\n  };\n};\n\n// src/uint8-utils.ts\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = globalThis.atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return globalThis.btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGFpLXNkay9wcm92aWRlci11dGlscy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9DQUFvQztBQUNuRCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGNBQWM7QUFDOUI7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDbUQ7QUFDbkQsaUJBQWlCLGlFQUFjO0FBQy9CO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2REFBZTtBQUM3QixrQkFBa0IsYUFBYTtBQUMvQixLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsNkRBQWU7QUFDN0Isa0JBQWtCLGFBQWEseUNBQXlDLG9CQUFvQjtBQUM1RixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2REFBZTtBQUM3QixrQkFBa0IsYUFBYSx5Q0FBeUMsb0JBQW9CLHFCQUFxQix5QkFBeUI7QUFDMUksS0FBSztBQUNMO0FBQ0E7QUFDQSxjQUFjLDZEQUFlO0FBQzdCLGtCQUFrQixhQUFhLDZDQUE2Qyx5QkFBeUI7QUFDckcsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw4REFBZ0I7QUFDOUIsa0JBQWtCLGFBQWE7QUFDL0IsS0FBSztBQUNMO0FBQ0E7QUFDQSxjQUFjLDhEQUFnQjtBQUM5QixrQkFBa0IsYUFBYSx5Q0FBeUMsWUFBWTtBQUNwRixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsY0FBYyw4REFBZ0I7QUFDOUIsa0JBQWtCLGFBQWEseUNBQXlDLFlBQVkscUJBQXFCLHlCQUF5QjtBQUNsSSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsOERBQWdCO0FBQzlCLGtCQUFrQixhQUFhLDZDQUE2Qyx5QkFBeUI7QUFDckcsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUMrRjtBQUNwRDs7QUFFM0M7QUFDdUQ7O0FBRXZEO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixvQ0FBb0MsSUFBSTtBQUN0RSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QscUNBQXFDLDRCQUE0QjtBQUNqRTtBQUNBLGNBQWMsaUVBQW1CLEdBQUcsNEJBQTRCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsaUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLGFBQWEsaUVBQW1CLDRDQUE0QyxpRUFBbUIsR0FBRyxxQkFBcUI7QUFDdkg7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0Esa0JBQWtCLG9EQUFnQjtBQUNsQztBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsZUFBZTtBQUMxQyxJQUFJO0FBQ0osUUFBUSw0REFBYyw0QkFBNEIsaUVBQW9CO0FBQ3RFO0FBQ0E7QUFDQSxjQUFjLDREQUFjLEdBQUcsb0JBQW9CO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxrQkFBa0Isb0RBQWdCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixlQUFlO0FBQzlDLElBQUk7QUFDSjtBQUNBO0FBQ0EsYUFBYSw0REFBYyx1Q0FBdUMsNERBQWMsR0FBRyxvQkFBb0I7QUFDdkc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksb0RBQWdCO0FBQ3BCO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2dEOztBQUVoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1IsbUNBQW1DLDBEQUFZO0FBQy9DO0FBQ0E7QUFDQSxrQkFBa0IsMERBQVk7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0EsbUNBQW1DLDBEQUFZO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwREFBWTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDBEQUFZO0FBQzlCLDZDQUE2QyxjQUFjO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUN5RjtBQUd0RDtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsY0FBYyxrQ0FBa0M7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiwwREFBYTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGlCQUFpQiwwREFBYTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxpQkFBaUIsMERBQWE7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLFVBQVU7QUFDM0U7QUFDQTtBQUNBLGNBQWMsb0VBQXNCLEdBQUc7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLDhFQUF1QjtBQUNyRztBQUNBLG9CQUFvQixNQUFNO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLFVBQVU7QUFDMUU7QUFDQTtBQUNBLGNBQWMsb0VBQXNCLEdBQUc7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxrQ0FBa0M7QUFDL0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGNBQWMsMERBQWE7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isa0JBQWtCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBNkJFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9weWxsYW1haW5kZXgtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGFpLXNkay9wcm92aWRlci11dGlscy9kaXN0L2luZGV4Lm1qcz82YzgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb21iaW5lLWhlYWRlcnMudHNcbmZ1bmN0aW9uIGNvbWJpbmVIZWFkZXJzKC4uLmhlYWRlcnMpIHtcbiAgcmV0dXJuIGhlYWRlcnMucmVkdWNlKFxuICAgIChjb21iaW5lZEhlYWRlcnMsIGN1cnJlbnRIZWFkZXJzKSA9PiAoe1xuICAgICAgLi4uY29tYmluZWRIZWFkZXJzLFxuICAgICAgLi4uY3VycmVudEhlYWRlcnMgIT0gbnVsbCA/IGN1cnJlbnRIZWFkZXJzIDoge31cbiAgICB9KSxcbiAgICB7fVxuICApO1xufVxuXG4vLyBzcmMvY29udmVydC1hc3luYy1nZW5lcmF0b3ItdG8tcmVhZGFibGUtc3RyZWFtLnRzXG5mdW5jdGlvbiBjb252ZXJ0QXN5bmNHZW5lcmF0b3JUb1JlYWRhYmxlU3RyZWFtKHN0cmVhbSkge1xuICByZXR1cm4gbmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgICAvKipcbiAgICAgKiBDYWxsZWQgd2hlbiB0aGUgY29uc3VtZXIgd2FudHMgdG8gcHVsbCBtb3JlIGRhdGEgZnJvbSB0aGUgc3RyZWFtLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyPFQ+fSBjb250cm9sbGVyIC0gVGhlIGNvbnRyb2xsZXIgdG8gZW5xdWV1ZSBkYXRhIGludG8gdGhlIHN0cmVhbS5cbiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx2b2lkPn1cbiAgICAgKi9cbiAgICBhc3luYyBwdWxsKGNvbnRyb2xsZXIpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHsgdmFsdWUsIGRvbmUgfSA9IGF3YWl0IHN0cmVhbS5uZXh0KCk7XG4gICAgICAgIGlmIChkb25lKSB7XG4gICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZSh2YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnRyb2xsZXIuZXJyb3IoZXJyb3IpO1xuICAgICAgfVxuICAgIH0sXG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHdoZW4gdGhlIGNvbnN1bWVyIGNhbmNlbHMgdGhlIHN0cmVhbS5cbiAgICAgKi9cbiAgICBjYW5jZWwoKSB7XG4gICAgfVxuICB9KTtcbn1cblxuLy8gc3JjL2V4dHJhY3QtcmVzcG9uc2UtaGVhZGVycy50c1xuZnVuY3Rpb24gZXh0cmFjdFJlc3BvbnNlSGVhZGVycyhyZXNwb25zZSkge1xuICBjb25zdCBoZWFkZXJzID0ge307XG4gIHJlc3BvbnNlLmhlYWRlcnMuZm9yRWFjaCgodmFsdWUsIGtleSkgPT4ge1xuICAgIGhlYWRlcnNba2V5XSA9IHZhbHVlO1xuICB9KTtcbiAgcmV0dXJuIGhlYWRlcnM7XG59XG5cbi8vIHNyYy9nZW5lcmF0ZS1pZC50c1xuaW1wb3J0IHsgY3VzdG9tQWxwaGFiZXQgfSBmcm9tIFwibmFub2lkL25vbi1zZWN1cmVcIjtcbnZhciBnZW5lcmF0ZUlkID0gY3VzdG9tQWxwaGFiZXQoXG4gIFwiMDEyMzQ1Njc4OUFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXpcIixcbiAgN1xuKTtcblxuLy8gc3JjL2dldC1lcnJvci1tZXNzYWdlLnRzXG5mdW5jdGlvbiBnZXRFcnJvck1lc3NhZ2UoZXJyb3IpIHtcbiAgaWYgKGVycm9yID09IG51bGwpIHtcbiAgICByZXR1cm4gXCJ1bmtub3duIGVycm9yXCI7XG4gIH1cbiAgaWYgKHR5cGVvZiBlcnJvciA9PT0gXCJzdHJpbmdcIikge1xuICAgIHJldHVybiBlcnJvcjtcbiAgfVxuICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgIHJldHVybiBlcnJvci5tZXNzYWdlO1xuICB9XG4gIHJldHVybiBKU09OLnN0cmluZ2lmeShlcnJvcik7XG59XG5cbi8vIHNyYy9pcy1hYm9ydC1lcnJvci50c1xuZnVuY3Rpb24gaXNBYm9ydEVycm9yKGVycm9yKSB7XG4gIHJldHVybiBlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIChlcnJvci5uYW1lID09PSBcIkFib3J0RXJyb3JcIiB8fCBlcnJvci5uYW1lID09PSBcIlRpbWVvdXRFcnJvclwiKTtcbn1cblxuLy8gc3JjL2xvYWQtYXBpLWtleS50c1xuaW1wb3J0IHsgTG9hZEFQSUtleUVycm9yIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXJcIjtcbmZ1bmN0aW9uIGxvYWRBcGlLZXkoe1xuICBhcGlLZXksXG4gIGVudmlyb25tZW50VmFyaWFibGVOYW1lLFxuICBhcGlLZXlQYXJhbWV0ZXJOYW1lID0gXCJhcGlLZXlcIixcbiAgZGVzY3JpcHRpb25cbn0pIHtcbiAgaWYgKHR5cGVvZiBhcGlLZXkgPT09IFwic3RyaW5nXCIpIHtcbiAgICByZXR1cm4gYXBpS2V5O1xuICB9XG4gIGlmIChhcGlLZXkgIT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBMb2FkQVBJS2V5RXJyb3Ioe1xuICAgICAgbWVzc2FnZTogYCR7ZGVzY3JpcHRpb259IEFQSSBrZXkgbXVzdCBiZSBhIHN0cmluZy5gXG4gICAgfSk7XG4gIH1cbiAgaWYgKHR5cGVvZiBwcm9jZXNzID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdGhyb3cgbmV3IExvYWRBUElLZXlFcnJvcih7XG4gICAgICBtZXNzYWdlOiBgJHtkZXNjcmlwdGlvbn0gQVBJIGtleSBpcyBtaXNzaW5nLiBQYXNzIGl0IHVzaW5nIHRoZSAnJHthcGlLZXlQYXJhbWV0ZXJOYW1lfScgcGFyYW1ldGVyLiBFbnZpcm9ubWVudCB2YXJpYWJsZXMgaXMgbm90IHN1cHBvcnRlZCBpbiB0aGlzIGVudmlyb25tZW50LmBcbiAgICB9KTtcbiAgfVxuICBhcGlLZXkgPSBwcm9jZXNzLmVudltlbnZpcm9ubWVudFZhcmlhYmxlTmFtZV07XG4gIGlmIChhcGlLZXkgPT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBMb2FkQVBJS2V5RXJyb3Ioe1xuICAgICAgbWVzc2FnZTogYCR7ZGVzY3JpcHRpb259IEFQSSBrZXkgaXMgbWlzc2luZy4gUGFzcyBpdCB1c2luZyB0aGUgJyR7YXBpS2V5UGFyYW1ldGVyTmFtZX0nIHBhcmFtZXRlciBvciB0aGUgJHtlbnZpcm9ubWVudFZhcmlhYmxlTmFtZX0gZW52aXJvbm1lbnQgdmFyaWFibGUuYFxuICAgIH0pO1xuICB9XG4gIGlmICh0eXBlb2YgYXBpS2V5ICE9PSBcInN0cmluZ1wiKSB7XG4gICAgdGhyb3cgbmV3IExvYWRBUElLZXlFcnJvcih7XG4gICAgICBtZXNzYWdlOiBgJHtkZXNjcmlwdGlvbn0gQVBJIGtleSBtdXN0IGJlIGEgc3RyaW5nLiBUaGUgdmFsdWUgb2YgdGhlICR7ZW52aXJvbm1lbnRWYXJpYWJsZU5hbWV9IGVudmlyb25tZW50IHZhcmlhYmxlIGlzIG5vdCBhIHN0cmluZy5gXG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIGFwaUtleTtcbn1cblxuLy8gc3JjL2xvYWQtc2V0dGluZy50c1xuaW1wb3J0IHsgTG9hZFNldHRpbmdFcnJvciB9IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyXCI7XG5mdW5jdGlvbiBsb2FkU2V0dGluZyh7XG4gIHNldHRpbmdWYWx1ZSxcbiAgZW52aXJvbm1lbnRWYXJpYWJsZU5hbWUsXG4gIHNldHRpbmdOYW1lLFxuICBkZXNjcmlwdGlvblxufSkge1xuICBpZiAodHlwZW9mIHNldHRpbmdWYWx1ZSA9PT0gXCJzdHJpbmdcIikge1xuICAgIHJldHVybiBzZXR0aW5nVmFsdWU7XG4gIH1cbiAgaWYgKHNldHRpbmdWYWx1ZSAhPSBudWxsKSB7XG4gICAgdGhyb3cgbmV3IExvYWRTZXR0aW5nRXJyb3Ioe1xuICAgICAgbWVzc2FnZTogYCR7ZGVzY3JpcHRpb259IHNldHRpbmcgbXVzdCBiZSBhIHN0cmluZy5gXG4gICAgfSk7XG4gIH1cbiAgaWYgKHR5cGVvZiBwcm9jZXNzID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdGhyb3cgbmV3IExvYWRTZXR0aW5nRXJyb3Ioe1xuICAgICAgbWVzc2FnZTogYCR7ZGVzY3JpcHRpb259IHNldHRpbmcgaXMgbWlzc2luZy4gUGFzcyBpdCB1c2luZyB0aGUgJyR7c2V0dGluZ05hbWV9JyBwYXJhbWV0ZXIuIEVudmlyb25tZW50IHZhcmlhYmxlcyBpcyBub3Qgc3VwcG9ydGVkIGluIHRoaXMgZW52aXJvbm1lbnQuYFxuICAgIH0pO1xuICB9XG4gIHNldHRpbmdWYWx1ZSA9IHByb2Nlc3MuZW52W2Vudmlyb25tZW50VmFyaWFibGVOYW1lXTtcbiAgaWYgKHNldHRpbmdWYWx1ZSA9PSBudWxsKSB7XG4gICAgdGhyb3cgbmV3IExvYWRTZXR0aW5nRXJyb3Ioe1xuICAgICAgbWVzc2FnZTogYCR7ZGVzY3JpcHRpb259IHNldHRpbmcgaXMgbWlzc2luZy4gUGFzcyBpdCB1c2luZyB0aGUgJyR7c2V0dGluZ05hbWV9JyBwYXJhbWV0ZXIgb3IgdGhlICR7ZW52aXJvbm1lbnRWYXJpYWJsZU5hbWV9IGVudmlyb25tZW50IHZhcmlhYmxlLmBcbiAgICB9KTtcbiAgfVxuICBpZiAodHlwZW9mIHNldHRpbmdWYWx1ZSAhPT0gXCJzdHJpbmdcIikge1xuICAgIHRocm93IG5ldyBMb2FkU2V0dGluZ0Vycm9yKHtcbiAgICAgIG1lc3NhZ2U6IGAke2Rlc2NyaXB0aW9ufSBzZXR0aW5nIG11c3QgYmUgYSBzdHJpbmcuIFRoZSB2YWx1ZSBvZiB0aGUgJHtlbnZpcm9ubWVudFZhcmlhYmxlTmFtZX0gZW52aXJvbm1lbnQgdmFyaWFibGUgaXMgbm90IGEgc3RyaW5nLmBcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gc2V0dGluZ1ZhbHVlO1xufVxuXG4vLyBzcmMvcGFyc2UtanNvbi50c1xuaW1wb3J0IHsgSlNPTlBhcnNlRXJyb3IsIFR5cGVWYWxpZGF0aW9uRXJyb3IgYXMgVHlwZVZhbGlkYXRpb25FcnJvcjIgfSBmcm9tIFwiQGFpLXNkay9wcm92aWRlclwiO1xuaW1wb3J0IFNlY3VyZUpTT04gZnJvbSBcInNlY3VyZS1qc29uLXBhcnNlXCI7XG5cbi8vIHNyYy92YWxpZGF0ZS10eXBlcy50c1xuaW1wb3J0IHsgVHlwZVZhbGlkYXRpb25FcnJvciB9IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyXCI7XG5cbi8vIHNyYy92YWxpZGF0b3IudHNcbnZhciB2YWxpZGF0b3JTeW1ib2wgPSBTeW1ib2woXCJ2ZXJjZWwuYWkudmFsaWRhdG9yXCIpO1xuZnVuY3Rpb24gdmFsaWRhdG9yKHZhbGlkYXRlKSB7XG4gIHJldHVybiB7IFt2YWxpZGF0b3JTeW1ib2xdOiB0cnVlLCB2YWxpZGF0ZSB9O1xufVxuZnVuY3Rpb24gaXNWYWxpZGF0b3IodmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gXCJvYmplY3RcIiAmJiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWxpZGF0b3JTeW1ib2wgaW4gdmFsdWUgJiYgdmFsdWVbdmFsaWRhdG9yU3ltYm9sXSA9PT0gdHJ1ZSAmJiBcInZhbGlkYXRlXCIgaW4gdmFsdWU7XG59XG5mdW5jdGlvbiB6b2RWYWxpZGF0b3Ioem9kU2NoZW1hKSB7XG4gIHJldHVybiB2YWxpZGF0b3IoKHZhbHVlKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0gem9kU2NoZW1hLnNhZmVQYXJzZSh2YWx1ZSk7XG4gICAgcmV0dXJuIHJlc3VsdC5zdWNjZXNzID8geyBzdWNjZXNzOiB0cnVlLCB2YWx1ZTogcmVzdWx0LmRhdGEgfSA6IHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiByZXN1bHQuZXJyb3IgfTtcbiAgfSk7XG59XG5cbi8vIHNyYy92YWxpZGF0ZS10eXBlcy50c1xuZnVuY3Rpb24gdmFsaWRhdGVUeXBlcyh7XG4gIHZhbHVlLFxuICBzY2hlbWE6IGlucHV0U2NoZW1hXG59KSB7XG4gIGNvbnN0IHJlc3VsdCA9IHNhZmVWYWxpZGF0ZVR5cGVzKHsgdmFsdWUsIHNjaGVtYTogaW5wdXRTY2hlbWEgfSk7XG4gIGlmICghcmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICB0aHJvdyBuZXcgVHlwZVZhbGlkYXRpb25FcnJvcih7IHZhbHVlLCBjYXVzZTogcmVzdWx0LmVycm9yIH0pO1xuICB9XG4gIHJldHVybiByZXN1bHQudmFsdWU7XG59XG5mdW5jdGlvbiBzYWZlVmFsaWRhdGVUeXBlcyh7XG4gIHZhbHVlLFxuICBzY2hlbWE6IGlucHV0U2NoZW1hXG59KSB7XG4gIGNvbnN0IHNjaGVtYSA9IGlzVmFsaWRhdG9yKGlucHV0U2NoZW1hKSA/IGlucHV0U2NoZW1hIDogem9kVmFsaWRhdG9yKGlucHV0U2NoZW1hKTtcbiAgdHJ5IHtcbiAgICBpZiAoc2NoZW1hLnZhbGlkYXRlID09IG51bGwpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHZhbHVlIH07XG4gICAgfVxuICAgIGNvbnN0IHZhbGlkYXRpb25SZXN1bHQgPSBzY2hlbWEudmFsaWRhdGUodmFsdWUpO1xuICAgIGlmICh2YWxpZGF0aW9uUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIHJldHVybiB2YWxpZGF0aW9uUmVzdWx0O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogbmV3IFR5cGVWYWxpZGF0aW9uRXJyb3Ioe1xuICAgICAgICB2YWx1ZSxcbiAgICAgICAgY2F1c2U6IHZhbGlkYXRpb25SZXN1bHQuZXJyb3JcbiAgICAgIH0pXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogVHlwZVZhbGlkYXRpb25FcnJvci5pc1R5cGVWYWxpZGF0aW9uRXJyb3IoZXJyb3IpID8gZXJyb3IgOiBuZXcgVHlwZVZhbGlkYXRpb25FcnJvcih7IHZhbHVlLCBjYXVzZTogZXJyb3IgfSlcbiAgICB9O1xuICB9XG59XG5cbi8vIHNyYy9wYXJzZS1qc29uLnRzXG5mdW5jdGlvbiBwYXJzZUpTT04oe1xuICB0ZXh0LFxuICBzY2hlbWFcbn0pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB2YWx1ZSA9IFNlY3VyZUpTT04ucGFyc2UodGV4dCk7XG4gICAgaWYgKHNjaGVtYSA9PSBudWxsKSB7XG4gICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiB2YWxpZGF0ZVR5cGVzKHsgdmFsdWUsIHNjaGVtYSB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBpZiAoSlNPTlBhcnNlRXJyb3IuaXNKU09OUGFyc2VFcnJvcihlcnJvcikgfHwgVHlwZVZhbGlkYXRpb25FcnJvcjIuaXNUeXBlVmFsaWRhdGlvbkVycm9yKGVycm9yKSkge1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICAgIHRocm93IG5ldyBKU09OUGFyc2VFcnJvcih7IHRleHQsIGNhdXNlOiBlcnJvciB9KTtcbiAgfVxufVxuZnVuY3Rpb24gc2FmZVBhcnNlSlNPTih7XG4gIHRleHQsXG4gIHNjaGVtYVxufSkge1xuICB0cnkge1xuICAgIGNvbnN0IHZhbHVlID0gU2VjdXJlSlNPTi5wYXJzZSh0ZXh0KTtcbiAgICBpZiAoc2NoZW1hID09IG51bGwpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIHZhbHVlXG4gICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4gc2FmZVZhbGlkYXRlVHlwZXMoeyB2YWx1ZSwgc2NoZW1hIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiBKU09OUGFyc2VFcnJvci5pc0pTT05QYXJzZUVycm9yKGVycm9yKSA/IGVycm9yIDogbmV3IEpTT05QYXJzZUVycm9yKHsgdGV4dCwgY2F1c2U6IGVycm9yIH0pXG4gICAgfTtcbiAgfVxufVxuZnVuY3Rpb24gaXNQYXJzYWJsZUpzb24oaW5wdXQpIHtcbiAgdHJ5IHtcbiAgICBTZWN1cmVKU09OLnBhcnNlKGlucHV0KTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxudmFyIGlzUGFyc2VhYmxlSnNvbiA9IGlzUGFyc2FibGVKc29uO1xuXG4vLyBzcmMvcG9zdC10by1hcGkudHNcbmltcG9ydCB7IEFQSUNhbGxFcnJvciB9IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyXCI7XG5cbi8vIHNyYy9yZW1vdmUtdW5kZWZpbmVkLWVudHJpZXMudHNcbmZ1bmN0aW9uIHJlbW92ZVVuZGVmaW5lZEVudHJpZXMocmVjb3JkKSB7XG4gIHJldHVybiBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgT2JqZWN0LmVudHJpZXMocmVjb3JkKS5maWx0ZXIoKFtfa2V5LCB2YWx1ZV0pID0+IHZhbHVlICE9IG51bGwpXG4gICk7XG59XG5cbi8vIHNyYy9wb3N0LXRvLWFwaS50c1xudmFyIGdldE9yaWdpbmFsRmV0Y2ggPSAoKSA9PiBmZXRjaDtcbnZhciBwb3N0SnNvblRvQXBpID0gYXN5bmMgKHtcbiAgdXJsLFxuICBoZWFkZXJzLFxuICBib2R5LFxuICBmYWlsZWRSZXNwb25zZUhhbmRsZXIsXG4gIHN1Y2Nlc3NmdWxSZXNwb25zZUhhbmRsZXIsXG4gIGFib3J0U2lnbmFsLFxuICBmZXRjaDogZmV0Y2gyXG59KSA9PiBwb3N0VG9BcGkoe1xuICB1cmwsXG4gIGhlYWRlcnM6IHtcbiAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAuLi5oZWFkZXJzXG4gIH0sXG4gIGJvZHk6IHtcbiAgICBjb250ZW50OiBKU09OLnN0cmluZ2lmeShib2R5KSxcbiAgICB2YWx1ZXM6IGJvZHlcbiAgfSxcbiAgZmFpbGVkUmVzcG9uc2VIYW5kbGVyLFxuICBzdWNjZXNzZnVsUmVzcG9uc2VIYW5kbGVyLFxuICBhYm9ydFNpZ25hbCxcbiAgZmV0Y2g6IGZldGNoMlxufSk7XG52YXIgcG9zdFRvQXBpID0gYXN5bmMgKHtcbiAgdXJsLFxuICBoZWFkZXJzID0ge30sXG4gIGJvZHksXG4gIHN1Y2Nlc3NmdWxSZXNwb25zZUhhbmRsZXIsXG4gIGZhaWxlZFJlc3BvbnNlSGFuZGxlcixcbiAgYWJvcnRTaWduYWwsXG4gIGZldGNoOiBmZXRjaDIgPSBnZXRPcmlnaW5hbEZldGNoKClcbn0pID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoMih1cmwsIHtcbiAgICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgICBoZWFkZXJzOiByZW1vdmVVbmRlZmluZWRFbnRyaWVzKGhlYWRlcnMpLFxuICAgICAgYm9keTogYm9keS5jb250ZW50LFxuICAgICAgc2lnbmFsOiBhYm9ydFNpZ25hbFxuICAgIH0pO1xuICAgIGNvbnN0IHJlc3BvbnNlSGVhZGVycyA9IGV4dHJhY3RSZXNwb25zZUhlYWRlcnMocmVzcG9uc2UpO1xuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGxldCBlcnJvckluZm9ybWF0aW9uO1xuICAgICAgdHJ5IHtcbiAgICAgICAgZXJyb3JJbmZvcm1hdGlvbiA9IGF3YWl0IGZhaWxlZFJlc3BvbnNlSGFuZGxlcih7XG4gICAgICAgICAgcmVzcG9uc2UsXG4gICAgICAgICAgdXJsLFxuICAgICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzOiBib2R5LnZhbHVlc1xuICAgICAgICB9KTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGlmIChpc0Fib3J0RXJyb3IoZXJyb3IpIHx8IEFQSUNhbGxFcnJvci5pc0FQSUNhbGxFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgQVBJQ2FsbEVycm9yKHtcbiAgICAgICAgICBtZXNzYWdlOiBcIkZhaWxlZCB0byBwcm9jZXNzIGVycm9yIHJlc3BvbnNlXCIsXG4gICAgICAgICAgY2F1c2U6IGVycm9yLFxuICAgICAgICAgIHN0YXR1c0NvZGU6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgICB1cmwsXG4gICAgICAgICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzOiBib2R5LnZhbHVlc1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIHRocm93IGVycm9ySW5mb3JtYXRpb24udmFsdWU7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICByZXR1cm4gYXdhaXQgc3VjY2Vzc2Z1bFJlc3BvbnNlSGFuZGxlcih7XG4gICAgICAgIHJlc3BvbnNlLFxuICAgICAgICB1cmwsXG4gICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzOiBib2R5LnZhbHVlc1xuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGlmIChpc0Fib3J0RXJyb3IoZXJyb3IpIHx8IEFQSUNhbGxFcnJvci5pc0FQSUNhbGxFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IEFQSUNhbGxFcnJvcih7XG4gICAgICAgIG1lc3NhZ2U6IFwiRmFpbGVkIHRvIHByb2Nlc3Mgc3VjY2Vzc2Z1bCByZXNwb25zZVwiLFxuICAgICAgICBjYXVzZTogZXJyb3IsXG4gICAgICAgIHN0YXR1c0NvZGU6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgdXJsLFxuICAgICAgICByZXNwb25zZUhlYWRlcnMsXG4gICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzOiBib2R5LnZhbHVlc1xuICAgICAgfSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChpc0Fib3J0RXJyb3IoZXJyb3IpKSB7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UgPT09IFwiZmV0Y2ggZmFpbGVkXCIpIHtcbiAgICAgIGNvbnN0IGNhdXNlID0gZXJyb3IuY2F1c2U7XG4gICAgICBpZiAoY2F1c2UgIT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgQVBJQ2FsbEVycm9yKHtcbiAgICAgICAgICBtZXNzYWdlOiBgQ2Fubm90IGNvbm5lY3QgdG8gQVBJOiAke2NhdXNlLm1lc3NhZ2V9YCxcbiAgICAgICAgICBjYXVzZSxcbiAgICAgICAgICB1cmwsXG4gICAgICAgICAgcmVxdWVzdEJvZHlWYWx1ZXM6IGJvZHkudmFsdWVzLFxuICAgICAgICAgIGlzUmV0cnlhYmxlOiB0cnVlXG4gICAgICAgICAgLy8gcmV0cnkgd2hlbiBuZXR3b3JrIGVycm9yXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuLy8gc3JjL3Jlc3BvbnNlLWhhbmRsZXIudHNcbmltcG9ydCB7IEFQSUNhbGxFcnJvciBhcyBBUElDYWxsRXJyb3IyLCBFbXB0eVJlc3BvbnNlQm9keUVycm9yIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXJcIjtcbmltcG9ydCB7XG4gIEV2ZW50U291cmNlUGFyc2VyU3RyZWFtXG59IGZyb20gXCJldmVudHNvdXJjZS1wYXJzZXIvc3RyZWFtXCI7XG52YXIgY3JlYXRlSnNvbkVycm9yUmVzcG9uc2VIYW5kbGVyID0gKHtcbiAgZXJyb3JTY2hlbWEsXG4gIGVycm9yVG9NZXNzYWdlLFxuICBpc1JldHJ5YWJsZVxufSkgPT4gYXN5bmMgKHsgcmVzcG9uc2UsIHVybCwgcmVxdWVzdEJvZHlWYWx1ZXMgfSkgPT4ge1xuICBjb25zdCByZXNwb25zZUJvZHkgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gIGNvbnN0IHJlc3BvbnNlSGVhZGVycyA9IGV4dHJhY3RSZXNwb25zZUhlYWRlcnMocmVzcG9uc2UpO1xuICBpZiAocmVzcG9uc2VCb2R5LnRyaW0oKSA9PT0gXCJcIikge1xuICAgIHJldHVybiB7XG4gICAgICByZXNwb25zZUhlYWRlcnMsXG4gICAgICB2YWx1ZTogbmV3IEFQSUNhbGxFcnJvcjIoe1xuICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5zdGF0dXNUZXh0LFxuICAgICAgICB1cmwsXG4gICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzLFxuICAgICAgICBzdGF0dXNDb2RlOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIHJlc3BvbnNlSGVhZGVycyxcbiAgICAgICAgcmVzcG9uc2VCb2R5LFxuICAgICAgICBpc1JldHJ5YWJsZTogaXNSZXRyeWFibGUgPT0gbnVsbCA/IHZvaWQgMCA6IGlzUmV0cnlhYmxlKHJlc3BvbnNlKVxuICAgICAgfSlcbiAgICB9O1xuICB9XG4gIHRyeSB7XG4gICAgY29uc3QgcGFyc2VkRXJyb3IgPSBwYXJzZUpTT04oe1xuICAgICAgdGV4dDogcmVzcG9uc2VCb2R5LFxuICAgICAgc2NoZW1hOiBlcnJvclNjaGVtYVxuICAgIH0pO1xuICAgIHJldHVybiB7XG4gICAgICByZXNwb25zZUhlYWRlcnMsXG4gICAgICB2YWx1ZTogbmV3IEFQSUNhbGxFcnJvcjIoe1xuICAgICAgICBtZXNzYWdlOiBlcnJvclRvTWVzc2FnZShwYXJzZWRFcnJvciksXG4gICAgICAgIHVybCxcbiAgICAgICAgcmVxdWVzdEJvZHlWYWx1ZXMsXG4gICAgICAgIHN0YXR1c0NvZGU6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgICAgICByZXNwb25zZUJvZHksXG4gICAgICAgIGRhdGE6IHBhcnNlZEVycm9yLFxuICAgICAgICBpc1JldHJ5YWJsZTogaXNSZXRyeWFibGUgPT0gbnVsbCA/IHZvaWQgMCA6IGlzUmV0cnlhYmxlKHJlc3BvbnNlLCBwYXJzZWRFcnJvcilcbiAgICAgIH0pXG4gICAgfTtcbiAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgIHJldHVybiB7XG4gICAgICByZXNwb25zZUhlYWRlcnMsXG4gICAgICB2YWx1ZTogbmV3IEFQSUNhbGxFcnJvcjIoe1xuICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5zdGF0dXNUZXh0LFxuICAgICAgICB1cmwsXG4gICAgICAgIHJlcXVlc3RCb2R5VmFsdWVzLFxuICAgICAgICBzdGF0dXNDb2RlOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIHJlc3BvbnNlSGVhZGVycyxcbiAgICAgICAgcmVzcG9uc2VCb2R5LFxuICAgICAgICBpc1JldHJ5YWJsZTogaXNSZXRyeWFibGUgPT0gbnVsbCA/IHZvaWQgMCA6IGlzUmV0cnlhYmxlKHJlc3BvbnNlKVxuICAgICAgfSlcbiAgICB9O1xuICB9XG59O1xudmFyIGNyZWF0ZUV2ZW50U291cmNlUmVzcG9uc2VIYW5kbGVyID0gKGNodW5rU2NoZW1hKSA9PiBhc3luYyAoeyByZXNwb25zZSB9KSA9PiB7XG4gIGNvbnN0IHJlc3BvbnNlSGVhZGVycyA9IGV4dHJhY3RSZXNwb25zZUhlYWRlcnMocmVzcG9uc2UpO1xuICBpZiAocmVzcG9uc2UuYm9keSA9PSBudWxsKSB7XG4gICAgdGhyb3cgbmV3IEVtcHR5UmVzcG9uc2VCb2R5RXJyb3Ioe30pO1xuICB9XG4gIHJldHVybiB7XG4gICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgIHZhbHVlOiByZXNwb25zZS5ib2R5LnBpcGVUaHJvdWdoKG5ldyBUZXh0RGVjb2RlclN0cmVhbSgpKS5waXBlVGhyb3VnaChuZXcgRXZlbnRTb3VyY2VQYXJzZXJTdHJlYW0oKSkucGlwZVRocm91Z2goXG4gICAgICBuZXcgVHJhbnNmb3JtU3RyZWFtKHtcbiAgICAgICAgdHJhbnNmb3JtKHsgZGF0YSB9LCBjb250cm9sbGVyKSB7XG4gICAgICAgICAgaWYgKGRhdGEgPT09IFwiW0RPTkVdXCIpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKFxuICAgICAgICAgICAgc2FmZVBhcnNlSlNPTih7XG4gICAgICAgICAgICAgIHRleHQ6IGRhdGEsXG4gICAgICAgICAgICAgIHNjaGVtYTogY2h1bmtTY2hlbWFcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICApXG4gIH07XG59O1xudmFyIGNyZWF0ZUpzb25TdHJlYW1SZXNwb25zZUhhbmRsZXIgPSAoY2h1bmtTY2hlbWEpID0+IGFzeW5jICh7IHJlc3BvbnNlIH0pID0+IHtcbiAgY29uc3QgcmVzcG9uc2VIZWFkZXJzID0gZXh0cmFjdFJlc3BvbnNlSGVhZGVycyhyZXNwb25zZSk7XG4gIGlmIChyZXNwb25zZS5ib2R5ID09IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRW1wdHlSZXNwb25zZUJvZHlFcnJvcih7fSk7XG4gIH1cbiAgbGV0IGJ1ZmZlciA9IFwiXCI7XG4gIHJldHVybiB7XG4gICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgIHZhbHVlOiByZXNwb25zZS5ib2R5LnBpcGVUaHJvdWdoKG5ldyBUZXh0RGVjb2RlclN0cmVhbSgpKS5waXBlVGhyb3VnaChcbiAgICAgIG5ldyBUcmFuc2Zvcm1TdHJlYW0oe1xuICAgICAgICB0cmFuc2Zvcm0oY2h1bmtUZXh0LCBjb250cm9sbGVyKSB7XG4gICAgICAgICAgaWYgKGNodW5rVGV4dC5lbmRzV2l0aChcIlxcblwiKSkge1xuICAgICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKFxuICAgICAgICAgICAgICBzYWZlUGFyc2VKU09OKHtcbiAgICAgICAgICAgICAgICB0ZXh0OiBidWZmZXIgKyBjaHVua1RleHQsXG4gICAgICAgICAgICAgICAgc2NoZW1hOiBjaHVua1NjaGVtYVxuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGJ1ZmZlciA9IFwiXCI7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGJ1ZmZlciArPSBjaHVua1RleHQ7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIClcbiAgfTtcbn07XG52YXIgY3JlYXRlSnNvblJlc3BvbnNlSGFuZGxlciA9IChyZXNwb25zZVNjaGVtYSkgPT4gYXN5bmMgKHsgcmVzcG9uc2UsIHVybCwgcmVxdWVzdEJvZHlWYWx1ZXMgfSkgPT4ge1xuICBjb25zdCByZXNwb25zZUJvZHkgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gIGNvbnN0IHBhcnNlZFJlc3VsdCA9IHNhZmVQYXJzZUpTT04oe1xuICAgIHRleHQ6IHJlc3BvbnNlQm9keSxcbiAgICBzY2hlbWE6IHJlc3BvbnNlU2NoZW1hXG4gIH0pO1xuICBjb25zdCByZXNwb25zZUhlYWRlcnMgPSBleHRyYWN0UmVzcG9uc2VIZWFkZXJzKHJlc3BvbnNlKTtcbiAgaWYgKCFwYXJzZWRSZXN1bHQuc3VjY2Vzcykge1xuICAgIHRocm93IG5ldyBBUElDYWxsRXJyb3IyKHtcbiAgICAgIG1lc3NhZ2U6IFwiSW52YWxpZCBKU09OIHJlc3BvbnNlXCIsXG4gICAgICBjYXVzZTogcGFyc2VkUmVzdWx0LmVycm9yLFxuICAgICAgc3RhdHVzQ29kZTogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgICAgcmVzcG9uc2VCb2R5LFxuICAgICAgdXJsLFxuICAgICAgcmVxdWVzdEJvZHlWYWx1ZXNcbiAgICB9KTtcbiAgfVxuICByZXR1cm4ge1xuICAgIHJlc3BvbnNlSGVhZGVycyxcbiAgICB2YWx1ZTogcGFyc2VkUmVzdWx0LnZhbHVlXG4gIH07XG59O1xuXG4vLyBzcmMvdWludDgtdXRpbHMudHNcbmZ1bmN0aW9uIGNvbnZlcnRCYXNlNjRUb1VpbnQ4QXJyYXkoYmFzZTY0U3RyaW5nKSB7XG4gIGNvbnN0IGJhc2U2NFVybCA9IGJhc2U2NFN0cmluZy5yZXBsYWNlKC8tL2csIFwiK1wiKS5yZXBsYWNlKC9fL2csIFwiL1wiKTtcbiAgY29uc3QgbGF0aW4xc3RyaW5nID0gZ2xvYmFsVGhpcy5hdG9iKGJhc2U2NFVybCk7XG4gIHJldHVybiBVaW50OEFycmF5LmZyb20obGF0aW4xc3RyaW5nLCAoYnl0ZSkgPT4gYnl0ZS5jb2RlUG9pbnRBdCgwKSk7XG59XG5mdW5jdGlvbiBjb252ZXJ0VWludDhBcnJheVRvQmFzZTY0KGFycmF5KSB7XG4gIGxldCBsYXRpbjFzdHJpbmcgPSBcIlwiO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGFycmF5Lmxlbmd0aDsgaSsrKSB7XG4gICAgbGF0aW4xc3RyaW5nICs9IFN0cmluZy5mcm9tQ29kZVBvaW50KGFycmF5W2ldKTtcbiAgfVxuICByZXR1cm4gZ2xvYmFsVGhpcy5idG9hKGxhdGluMXN0cmluZyk7XG59XG5cbi8vIHNyYy93aXRob3V0LXRyYWlsaW5nLXNsYXNoLnRzXG5mdW5jdGlvbiB3aXRob3V0VHJhaWxpbmdTbGFzaCh1cmwpIHtcbiAgcmV0dXJuIHVybCA9PSBudWxsID8gdm9pZCAwIDogdXJsLnJlcGxhY2UoL1xcLyQvLCBcIlwiKTtcbn1cbmV4cG9ydCB7XG4gIGNvbWJpbmVIZWFkZXJzLFxuICBjb252ZXJ0QXN5bmNHZW5lcmF0b3JUb1JlYWRhYmxlU3RyZWFtLFxuICBjb252ZXJ0QmFzZTY0VG9VaW50OEFycmF5LFxuICBjb252ZXJ0VWludDhBcnJheVRvQmFzZTY0LFxuICBjcmVhdGVFdmVudFNvdXJjZVJlc3BvbnNlSGFuZGxlcixcbiAgY3JlYXRlSnNvbkVycm9yUmVzcG9uc2VIYW5kbGVyLFxuICBjcmVhdGVKc29uUmVzcG9uc2VIYW5kbGVyLFxuICBjcmVhdGVKc29uU3RyZWFtUmVzcG9uc2VIYW5kbGVyLFxuICBleHRyYWN0UmVzcG9uc2VIZWFkZXJzLFxuICBnZW5lcmF0ZUlkLFxuICBnZXRFcnJvck1lc3NhZ2UsXG4gIGlzQWJvcnRFcnJvcixcbiAgaXNQYXJzYWJsZUpzb24sXG4gIGlzUGFyc2VhYmxlSnNvbixcbiAgaXNWYWxpZGF0b3IsXG4gIGxvYWRBcGlLZXksXG4gIGxvYWRTZXR0aW5nLFxuICBwYXJzZUpTT04sXG4gIHBvc3RKc29uVG9BcGksXG4gIHBvc3RUb0FwaSxcbiAgc2FmZVBhcnNlSlNPTixcbiAgc2FmZVZhbGlkYXRlVHlwZXMsXG4gIHZhbGlkYXRlVHlwZXMsXG4gIHZhbGlkYXRvcixcbiAgdmFsaWRhdG9yU3ltYm9sLFxuICB3aXRob3V0VHJhaWxpbmdTbGFzaCxcbiAgem9kVmFsaWRhdG9yXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customAlphabet: () => (/* binding */ customAlphabet),\n/* harmony export */   nanoid: () => (/* binding */ nanoid)\n/* harmony export */ });\nlet urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    let i = size\n    while (i--) {\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\nlet nanoid = (size = 21) => {\n  let id = ''\n  let i = size\n  while (i--) {\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGFpLXNkay9wcm92aWRlci11dGlscy9ub2RlX21vZHVsZXMvbmFub2lkL25vbi1zZWN1cmUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2lDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHlsbGFtYWluZGV4LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BhaS1zZGsvcHJvdmlkZXItdXRpbHMvbm9kZV9tb2R1bGVzL25hbm9pZC9ub24tc2VjdXJlL2luZGV4LmpzPzAwZTciXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHVybEFscGhhYmV0ID1cbiAgJ3VzZWFuZG9tLTI2VDE5ODM0MFBYNzVweEpBQ0tWRVJZTUlOREJVU0hXT0xGX0dRWmJmZ2hqa2xxdnd5enJpY3QnXG5sZXQgY3VzdG9tQWxwaGFiZXQgPSAoYWxwaGFiZXQsIGRlZmF1bHRTaXplID0gMjEpID0+IHtcbiAgcmV0dXJuIChzaXplID0gZGVmYXVsdFNpemUpID0+IHtcbiAgICBsZXQgaWQgPSAnJ1xuICAgIGxldCBpID0gc2l6ZVxuICAgIHdoaWxlIChpLS0pIHtcbiAgICAgIGlkICs9IGFscGhhYmV0WyhNYXRoLnJhbmRvbSgpICogYWxwaGFiZXQubGVuZ3RoKSB8IDBdXG4gICAgfVxuICAgIHJldHVybiBpZFxuICB9XG59XG5sZXQgbmFub2lkID0gKHNpemUgPSAyMSkgPT4ge1xuICBsZXQgaWQgPSAnJ1xuICBsZXQgaSA9IHNpemVcbiAgd2hpbGUgKGktLSkge1xuICAgIGlkICs9IHVybEFscGhhYmV0WyhNYXRoLnJhbmRvbSgpICogNjQpIHwgMF1cbiAgfVxuICByZXR1cm4gaWRcbn1cbmV4cG9ydCB7IG5hbm9pZCwgY3VzdG9tQWxwaGFiZXQgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider-utils/node_modules/nanoid/non-secure/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   DownloadError: () => (/* binding */ DownloadError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidDataContentError: () => (/* binding */ InvalidDataContentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   InvalidToolArgumentsError: () => (/* binding */ InvalidToolArgumentsError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoObjectGeneratedError: () => (/* binding */ NoObjectGeneratedError),\n/* harmony export */   NoSuchToolError: () => (/* binding */ NoSuchToolError),\n/* harmony export */   RetryError: () => (/* binding */ RetryError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   ToolCallParseError: () => (/* binding */ ToolCallParseError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   UnsupportedJSONSchemaError: () => (/* binding */ UnsupportedJSONSchemaError)\n/* harmony export */ });\n// src/errors/api-call-error.ts\nvar APICallError = class extends Error {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super(message);\n    this.name = \"AI_APICallError\";\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.cause = cause;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isAPICallError(error) {\n    return error instanceof Error && error.name === \"AI_APICallError\" && typeof error.url === \"string\" && typeof error.requestBodyValues === \"object\" && (error.statusCode == null || typeof error.statusCode === \"number\") && (error.responseHeaders == null || typeof error.responseHeaders === \"object\") && (error.responseBody == null || typeof error.responseBody === \"string\") && (error.cause == null || typeof error.cause === \"object\") && typeof error.isRetryable === \"boolean\" && (error.data == null || typeof error.data === \"object\");\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      requestBodyValues: this.requestBodyValues,\n      statusCode: this.statusCode,\n      responseHeaders: this.responseHeaders,\n      responseBody: this.responseBody,\n      cause: this.cause,\n      isRetryable: this.isRetryable,\n      data: this.data\n    };\n  }\n};\n\n// src/errors/download-error.ts\nvar DownloadError = class extends Error {\n  constructor({\n    url,\n    statusCode,\n    statusText,\n    cause,\n    message = cause == null ? `Failed to download ${url}: ${statusCode} ${statusText}` : `Failed to download ${url}: ${cause}`\n  }) {\n    super(message);\n    this.name = \"AI_DownloadError\";\n    this.url = url;\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n    this.cause = cause;\n  }\n  static isDownloadError(error) {\n    return error instanceof Error && error.name === \"AI_DownloadError\" && typeof error.url === \"string\" && (error.statusCode == null || typeof error.statusCode === \"number\") && (error.statusText == null || typeof error.statusText === \"string\");\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      statusCode: this.statusCode,\n      statusText: this.statusText,\n      cause: this.cause\n    };\n  }\n};\n\n// src/errors/empty-response-body-error.ts\nvar EmptyResponseBodyError = class extends Error {\n  constructor({ message = \"Empty response body\" } = {}) {\n    super(message);\n    this.name = \"AI_EmptyResponseBodyError\";\n  }\n  static isEmptyResponseBodyError(error) {\n    return error instanceof Error && error.name === \"AI_EmptyResponseBodyError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack\n    };\n  }\n};\n\n// src/errors/invalid-argument-error.ts\nvar InvalidArgumentError = class extends Error {\n  constructor({\n    parameter,\n    value,\n    message\n  }) {\n    super(`Invalid argument for parameter ${parameter}: ${message}`);\n    this.name = \"AI_InvalidArgumentError\";\n    this.parameter = parameter;\n    this.value = value;\n  }\n  static isInvalidArgumentError(error) {\n    return error instanceof Error && error.name === \"AI_InvalidArgumentError\" && typeof error.parameter === \"string\" && typeof error.value === \"string\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      parameter: this.parameter,\n      value: this.value\n    };\n  }\n};\n\n// src/errors/invalid-data-content-error.ts\nvar InvalidDataContentError = class extends Error {\n  constructor({\n    content,\n    cause,\n    message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.`\n  }) {\n    super(message);\n    this.name = \"AI_InvalidDataContentError\";\n    this.cause = cause;\n    this.content = content;\n  }\n  static isInvalidDataContentError(error) {\n    return error instanceof Error && error.name === \"AI_InvalidDataContentError\" && error.content != null;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      cause: this.cause,\n      content: this.content\n    };\n  }\n};\n\n// src/errors/invalid-prompt-error.ts\nvar InvalidPromptError = class extends Error {\n  constructor({ prompt: prompt2, message }) {\n    super(`Invalid prompt: ${message}`);\n    this.name = \"AI_InvalidPromptError\";\n    this.prompt = prompt2;\n  }\n  static isInvalidPromptError(error) {\n    return error instanceof Error && error.name === \"AI_InvalidPromptError\" && prompt != null;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      prompt: this.prompt\n    };\n  }\n};\n\n// src/errors/invalid-response-data-error.ts\nvar InvalidResponseDataError = class extends Error {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super(message);\n    this.name = \"AI_InvalidResponseDataError\";\n    this.data = data;\n  }\n  static isInvalidResponseDataError(error) {\n    return error instanceof Error && error.name === \"AI_InvalidResponseDataError\" && error.data != null;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      data: this.data\n    };\n  }\n};\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-tool-arguments-error.ts\nvar InvalidToolArgumentsError = class extends Error {\n  constructor({\n    toolArgs,\n    toolName,\n    cause,\n    message = `Invalid arguments for tool ${toolName}: ${getErrorMessage(\n      cause\n    )}`\n  }) {\n    super(message);\n    this.name = \"AI_InvalidToolArgumentsError\";\n    this.toolArgs = toolArgs;\n    this.toolName = toolName;\n    this.cause = cause;\n  }\n  static isInvalidToolArgumentsError(error) {\n    return error instanceof Error && error.name === \"AI_InvalidToolArgumentsError\" && typeof error.toolName === \"string\" && typeof error.toolArgs === \"string\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n      toolName: this.toolName,\n      toolArgs: this.toolArgs\n    };\n  }\n};\n\n// src/errors/json-parse-error.ts\nvar JSONParseError = class extends Error {\n  constructor({ text, cause }) {\n    super(\n      `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`\n    );\n    this.name = \"AI_JSONParseError\";\n    this.cause = cause;\n    this.text = text;\n  }\n  static isJSONParseError(error) {\n    return error instanceof Error && error.name === \"AI_JSONParseError\" && typeof error.text === \"string\" && typeof error.cause === \"string\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n      valueText: this.text\n    };\n  }\n};\n\n// src/errors/load-api-key-error.ts\nvar LoadAPIKeyError = class extends Error {\n  constructor({ message }) {\n    super(message);\n    this.name = \"AI_LoadAPIKeyError\";\n  }\n  static isLoadAPIKeyError(error) {\n    return error instanceof Error && error.name === \"AI_LoadAPIKeyError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message\n    };\n  }\n};\n\n// src/errors/load-setting-error.ts\nvar LoadSettingError = class extends Error {\n  constructor({ message }) {\n    super(message);\n    this.name = \"AI_LoadSettingError\";\n  }\n  static isLoadSettingError(error) {\n    return error instanceof Error && error.name === \"AI_LoadSettingError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message\n    };\n  }\n};\n\n// src/errors/no-content-generated-error.ts\nvar NoContentGeneratedError = class extends Error {\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super(message);\n    this.name = \"AI_NoContentGeneratedError\";\n  }\n  static isNoContentGeneratedError(error) {\n    return error instanceof Error && error.name === \"AI_NoContentGeneratedError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack\n    };\n  }\n};\n\n// src/errors/no-object-generated-error.ts\nvar NoObjectGeneratedError = class extends Error {\n  constructor({ message = \"No object generated.\" } = {}) {\n    super(message);\n    this.name = \"AI_NoObjectGeneratedError\";\n  }\n  static isNoObjectGeneratedError(error) {\n    return error instanceof Error && error.name === \"AI_NoObjectGeneratedError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack\n    };\n  }\n};\n\n// src/errors/no-such-tool-error.ts\nvar NoSuchToolError = class extends Error {\n  constructor({\n    toolName,\n    availableTools = void 0,\n    message = `Model tried to call unavailable tool '${toolName}'. ${availableTools === void 0 ? \"No tools are available.\" : `Available tools: ${availableTools.join(\", \")}.`}`\n  }) {\n    super(message);\n    this.name = \"AI_NoSuchToolError\";\n    this.toolName = toolName;\n    this.availableTools = availableTools;\n  }\n  static isNoSuchToolError(error) {\n    return error instanceof Error && error.name === \"AI_NoSuchToolError\" && \"toolName\" in error && error.toolName != void 0 && typeof error.name === \"string\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      toolName: this.toolName,\n      availableTools: this.availableTools\n    };\n  }\n};\n\n// src/errors/retry-error.ts\nvar RetryError = class extends Error {\n  constructor({\n    message,\n    reason,\n    errors\n  }) {\n    super(message);\n    this.name = \"AI_RetryError\";\n    this.reason = reason;\n    this.errors = errors;\n    this.lastError = errors[errors.length - 1];\n  }\n  static isRetryError(error) {\n    return error instanceof Error && error.name === \"AI_RetryError\" && typeof error.reason === \"string\" && Array.isArray(error.errors);\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      reason: this.reason,\n      lastError: this.lastError,\n      errors: this.errors\n    };\n  }\n};\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar TooManyEmbeddingValuesForCallError = class extends Error {\n  constructor(options) {\n    super(\n      `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    );\n    this.name = \"AI_TooManyEmbeddingValuesForCallError\";\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInvalidPromptError(error) {\n    return error instanceof Error && error.name === \"AI_TooManyEmbeddingValuesForCallError\" && \"provider\" in error && typeof error.provider === \"string\" && \"modelId\" in error && typeof error.modelId === \"string\" && \"maxEmbeddingsPerCall\" in error && typeof error.maxEmbeddingsPerCall === \"number\" && \"values\" in error && Array.isArray(error.values);\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      provider: this.provider,\n      modelId: this.modelId,\n      maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n      values: this.values\n    };\n  }\n};\n\n// src/errors/tool-call-parse-error.ts\nvar ToolCallParseError = class extends Error {\n  constructor({\n    cause,\n    text,\n    tools,\n    message = `Failed to parse tool calls: ${getErrorMessage(cause)}`\n  }) {\n    super(message);\n    this.name = \"AI_ToolCallParseError\";\n    this.cause = cause;\n    this.text = text;\n    this.tools = tools;\n  }\n  static isToolCallParseError(error) {\n    return error instanceof Error && error.name === \"AI_ToolCallParseError\" && \"cause\" in error && error.cause != void 0 && \"text\" in error && error.text != void 0 && typeof error.text === \"string\" && \"tools\" in error && error.tools != void 0;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      cause: this.cause,\n      text: this.text,\n      tools: this.tools\n    };\n  }\n};\n\n// src/errors/type-validation-error.ts\nvar TypeValidationError = class extends Error {\n  constructor({ value, cause }) {\n    super(\n      `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`\n    );\n    this.name = \"AI_TypeValidationError\";\n    this.cause = cause;\n    this.value = value;\n  }\n  static isTypeValidationError(error) {\n    return error instanceof Error && error.name === \"AI_TypeValidationError\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n      value: this.value\n    };\n  }\n};\n\n// src/errors/unsupported-functionality-error.ts\nvar UnsupportedFunctionalityError = class extends Error {\n  constructor({ functionality }) {\n    super(`'${functionality}' functionality not supported.`);\n    this.name = \"AI_UnsupportedFunctionalityError\";\n    this.functionality = functionality;\n  }\n  static isUnsupportedFunctionalityError(error) {\n    return error instanceof Error && error.name === \"AI_UnsupportedFunctionalityError\" && typeof error.functionality === \"string\";\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      functionality: this.functionality\n    };\n  }\n};\n\n// src/errors/unsupported-json-schema-error.ts\nvar UnsupportedJSONSchemaError = class extends Error {\n  constructor({\n    schema,\n    reason,\n    message = `Unsupported JSON schema: ${reason}`\n  }) {\n    super(message);\n    this.name = \"AI_UnsupportedJSONSchemaError\";\n    this.reason = reason;\n    this.schema = schema;\n  }\n  static isUnsupportedJSONSchemaError(error) {\n    return error instanceof Error && error.name === \"AI_UnsupportedJSONSchemaError\" && \"reason\" in error && error.reason != void 0 && \"schema\" in error && error.schema !== void 0;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      reason: this.reason,\n      schema: this.schema\n    };\n  }\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@ai-sdk/react/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useAssistant: () => (/* binding */ experimental_useAssistant),\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/ui-utils */ \"(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/core/index.mjs\");\n// src/use-assistant.ts\n\n\n\nvar getOriginalFetch = () => fetch;\nfunction useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch: fetch2\n}) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    void 0\n  );\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"awaiting_message\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const handleInputChange = (event) => {\n    setInput(event.target.value);\n  };\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const append = async (message, requestOptions) => {\n    var _a, _b;\n    setStatus(\"in_progress\");\n    setMessages((messages2) => {\n      var _a2;\n      return [\n        ...messages2,\n        {\n          ...message,\n          id: (_a2 = message.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)()\n        }\n      ];\n    });\n    setInput(\"\");\n    const abortController = new AbortController();\n    try {\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        credentials,\n        signal: abortController.signal,\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: (_a = threadIdParam != null ? threadIdParam : currentThreadId) != null ? _a : null,\n          message: message.content,\n          // optional request data:\n          data: requestOptions == null ? void 0 : requestOptions.data\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_b = await response.text()) != null ? _b : \"Failed to fetch the assistant response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      for await (const { type, value } of (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.readDataStream)(\n        response.body.getReader()\n      )) {\n        switch (type) {\n          case \"assistant_message\": {\n            setMessages((messages2) => [\n              ...messages2,\n              {\n                id: value.id,\n                role: value.role,\n                content: value.content[0].text.value\n              }\n            ]);\n            break;\n          }\n          case \"text\": {\n            setMessages((messages2) => {\n              const lastMessage = messages2[messages2.length - 1];\n              return [\n                ...messages2.slice(0, messages2.length - 1),\n                {\n                  id: lastMessage.id,\n                  role: lastMessage.role,\n                  content: lastMessage.content + value\n                }\n              ];\n            });\n            break;\n          }\n          case \"data_message\": {\n            setMessages((messages2) => {\n              var _a2;\n              return [\n                ...messages2,\n                {\n                  id: (_a2 = value.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                  role: \"data\",\n                  content: \"\",\n                  data: value.data\n                }\n              ];\n            });\n            break;\n          }\n          case \"assistant_control_data\": {\n            setCurrentThreadId(value.threadId);\n            setMessages((messages2) => {\n              const lastMessage = messages2[messages2.length - 1];\n              lastMessage.id = value.messageId;\n              return [...messages2.slice(0, messages2.length - 1), lastMessage];\n            });\n            break;\n          }\n          case \"error\": {\n            setError(new Error(value));\n            break;\n          }\n        }\n      }\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus(\"awaiting_message\");\n    }\n  };\n  const submitMessage = async (event, requestOptions) => {\n    var _a;\n    (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n    if (input === \"\") {\n      return;\n    }\n    append({ role: \"user\", content: input }, requestOptions);\n  };\n  const setThreadId = (threadId) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop\n  };\n}\nvar experimental_useAssistant = useAssistant;\n\n// src/use-chat.ts\n\n\n\nvar getStreamedResponse = async (api, chatRequest, mutate, mutateStreamData, existingData, extraMetadataRef, messagesRef, abortControllerRef, generateId2, streamProtocol, onFinish, onResponse, onToolCall, sendExtraMessageFields, experimental_prepareRequestBody, fetch2, keepLastMessageOnError) => {\n  var _a;\n  const previousMessages = messagesRef.current;\n  mutate(chatRequest.messages, false);\n  const constructedMessagesPayload = sendExtraMessageFields ? chatRequest.messages : chatRequest.messages.map(\n    ({\n      role,\n      content,\n      experimental_attachments,\n      name,\n      data,\n      annotations,\n      toolInvocations,\n      function_call,\n      tool_calls,\n      tool_call_id\n    }) => ({\n      role,\n      content,\n      ...experimental_attachments !== void 0 && {\n        experimental_attachments\n      },\n      ...name !== void 0 && { name },\n      ...data !== void 0 && { data },\n      ...annotations !== void 0 && { annotations },\n      ...toolInvocations !== void 0 && { toolInvocations },\n      // outdated function/tool call handling (TODO deprecate):\n      tool_call_id,\n      ...function_call !== void 0 && { function_call },\n      ...tool_calls !== void 0 && { tool_calls }\n    })\n  );\n  return await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callChatApi)({\n    api,\n    body: (_a = experimental_prepareRequestBody == null ? void 0 : experimental_prepareRequestBody({\n      messages: chatRequest.messages,\n      requestData: chatRequest.data,\n      requestBody: chatRequest.body\n    })) != null ? _a : {\n      messages: constructedMessagesPayload,\n      data: chatRequest.data,\n      ...extraMetadataRef.current.body,\n      ...chatRequest.body,\n      ...chatRequest.functions !== void 0 && {\n        functions: chatRequest.functions\n      },\n      ...chatRequest.function_call !== void 0 && {\n        function_call: chatRequest.function_call\n      },\n      ...chatRequest.tools !== void 0 && {\n        tools: chatRequest.tools\n      },\n      ...chatRequest.tool_choice !== void 0 && {\n        tool_choice: chatRequest.tool_choice\n      }\n    },\n    streamProtocol,\n    credentials: extraMetadataRef.current.credentials,\n    headers: {\n      ...extraMetadataRef.current.headers,\n      ...chatRequest.headers\n    },\n    abortController: () => abortControllerRef.current,\n    restoreMessagesOnFailure() {\n      if (!keepLastMessageOnError) {\n        mutate(previousMessages, false);\n      }\n    },\n    onResponse,\n    onUpdate(merged, data) {\n      mutate([...chatRequest.messages, ...merged], false);\n      mutateStreamData([...existingData || [], ...data || []], false);\n    },\n    onToolCall,\n    onFinish,\n    generateId: generateId2,\n    fetch: fetch2\n  });\n};\nfunction useChat({\n  api = \"/api/chat\",\n  id,\n  initialMessages,\n  initialInput = \"\",\n  sendExtraMessageFields,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  onToolCall,\n  experimental_prepareRequestBody,\n  experimental_maxAutomaticRoundtrips = 0,\n  maxAutomaticRoundtrips = experimental_maxAutomaticRoundtrips,\n  maxToolRoundtrips = maxAutomaticRoundtrips,\n  streamMode,\n  streamProtocol,\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId: generateId2 = _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  fetch: fetch2,\n  keepLastMessageOnError = false\n} = {}) {\n  if (streamMode) {\n    streamProtocol != null ? streamProtocol : streamProtocol = streamMode === \"text\" ? \"text\" : void 0;\n  }\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const idKey = id != null ? id : hookId;\n  const chatKey = typeof api === \"string\" ? [api, idKey] : idKey;\n  const [initialMessagesFallback] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const { data: messages, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [chatKey, \"messages\"],\n    null,\n    { fallbackData: initialMessages != null ? initialMessages : initialMessagesFallback }\n  );\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [chatKey, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([chatKey, \"streamData\"], null);\n  const { data: error = void 0, mutate: setError } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([chatKey, \"error\"], null);\n  const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(messages || []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (chatRequest) => {\n      const messageCount = messagesRef.current.length;\n      try {\n        mutateLoading(true);\n        setError(void 0);\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n        await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.processChatStream)({\n          getStreamedResponse: () => getStreamedResponse(\n            api,\n            chatRequest,\n            mutate,\n            mutateStreamData,\n            streamData,\n            extraMetadataRef,\n            messagesRef,\n            abortControllerRef,\n            generateId2,\n            streamProtocol,\n            onFinish,\n            onResponse,\n            onToolCall,\n            sendExtraMessageFields,\n            experimental_prepareRequestBody,\n            fetch2,\n            keepLastMessageOnError\n          ),\n          experimental_onFunctionCall,\n          experimental_onToolCall,\n          updateChatRequest: (chatRequestParam) => {\n            chatRequest = chatRequestParam;\n          },\n          getCurrentMessages: () => messagesRef.current\n        });\n        abortControllerRef.current = null;\n      } catch (err) {\n        if (err.name === \"AbortError\") {\n          abortControllerRef.current = null;\n          return null;\n        }\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n        setError(err);\n      } finally {\n        mutateLoading(false);\n      }\n      const messages2 = messagesRef.current;\n      const lastMessage = messages2[messages2.length - 1];\n      if (\n        // ensure we actually have new messages (to prevent infinite loops in case of errors):\n        messages2.length > messageCount && // ensure there is a last message:\n        lastMessage != null && // check if the feature is enabled:\n        maxToolRoundtrips > 0 && // check that roundtrip is possible:\n        isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic roundtrips:\n        countTrailingAssistantMessages(messages2) <= maxToolRoundtrips\n      ) {\n        await triggerRequest({ messages: messages2 });\n      }\n    },\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamData,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_onFunctionCall,\n      experimental_onToolCall,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxToolRoundtrips,\n      messagesRef,\n      abortControllerRef,\n      generateId2,\n      fetch2,\n      keepLastMessageOnError\n    ]\n  );\n  const append = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (message, {\n      options,\n      functions,\n      function_call,\n      tools,\n      tool_choice,\n      data,\n      headers: headers2,\n      body: body2\n    } = {}) => {\n      if (!message.id) {\n        message.id = generateId2();\n      }\n      const requestOptions = {\n        headers: headers2 != null ? headers2 : options == null ? void 0 : options.headers,\n        body: body2 != null ? body2 : options == null ? void 0 : options.body\n      };\n      const chatRequest = {\n        messages: messagesRef.current.concat(message),\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n        ...functions !== void 0 && { functions },\n        ...function_call !== void 0 && { function_call },\n        ...tools !== void 0 && { tools },\n        ...tool_choice !== void 0 && { tool_choice }\n      };\n      return triggerRequest(chatRequest);\n    },\n    [triggerRequest, generateId2]\n  );\n  const reload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async ({\n      options,\n      functions,\n      function_call,\n      tools,\n      tool_choice,\n      data,\n      headers: headers2,\n      body: body2\n    } = {}) => {\n      if (messagesRef.current.length === 0)\n        return null;\n      const requestOptions = {\n        headers: headers2 != null ? headers2 : options == null ? void 0 : options.headers,\n        body: body2 != null ? body2 : options == null ? void 0 : options.body\n      };\n      const lastMessage = messagesRef.current[messagesRef.current.length - 1];\n      if (lastMessage.role === \"assistant\") {\n        const chatRequest2 = {\n          messages: messagesRef.current.slice(0, -1),\n          options: requestOptions,\n          headers: requestOptions.headers,\n          body: requestOptions.body,\n          data,\n          ...functions !== void 0 && { functions },\n          ...function_call !== void 0 && { function_call },\n          ...tools !== void 0 && { tools },\n          ...tool_choice !== void 0 && { tool_choice }\n        };\n        return triggerRequest(chatRequest2);\n      }\n      const chatRequest = {\n        messages: messagesRef.current,\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data,\n        ...functions !== void 0 && { functions },\n        ...function_call !== void 0 && { function_call },\n        ...tools !== void 0 && { tools },\n        ...tool_choice !== void 0 && { tool_choice }\n      };\n      return triggerRequest(chatRequest);\n    },\n    [triggerRequest]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const setMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      if (typeof messages2 === \"function\") {\n        messages2 = messages2(messagesRef.current);\n      }\n      mutate(messages2, false);\n      messagesRef.current = messages2;\n    },\n    [mutate]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (event, options = {}, metadata) => {\n      var _a, _b, _c, _d, _e;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      if (!input && !options.allowEmptySubmit)\n        return;\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata\n        };\n      }\n      const attachmentsForRequest = [];\n      const attachmentsFromOptions = options.experimental_attachments;\n      if (attachmentsFromOptions) {\n        if (attachmentsFromOptions instanceof FileList) {\n          for (const attachment of Array.from(attachmentsFromOptions)) {\n            const { name, type } = attachment;\n            const dataUrl = await new Promise((resolve, reject) => {\n              const reader = new FileReader();\n              reader.onload = (readerEvent) => {\n                var _a2;\n                resolve((_a2 = readerEvent.target) == null ? void 0 : _a2.result);\n              };\n              reader.onerror = (error2) => reject(error2);\n              reader.readAsDataURL(attachment);\n            });\n            attachmentsForRequest.push({\n              name,\n              contentType: type,\n              url: dataUrl\n            });\n          }\n        } else if (Array.isArray(attachmentsFromOptions)) {\n          for (const file of attachmentsFromOptions) {\n            const { name, url, contentType } = file;\n            attachmentsForRequest.push({\n              name,\n              contentType,\n              url\n            });\n          }\n        } else {\n          throw new Error(\"Invalid attachments type\");\n        }\n      }\n      const requestOptions = {\n        headers: (_c = options.headers) != null ? _c : (_b = options.options) == null ? void 0 : _b.headers,\n        body: (_e = options.body) != null ? _e : (_d = options.options) == null ? void 0 : _d.body\n      };\n      const messages2 = !input && options.allowEmptySubmit ? messagesRef.current : messagesRef.current.concat({\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"user\",\n        content: input,\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0\n      });\n      const chatRequest = {\n        messages: messages2,\n        options: requestOptions,\n        headers: requestOptions.headers,\n        body: requestOptions.body,\n        data: options.data\n      };\n      triggerRequest(chatRequest);\n      setInput(\"\");\n    },\n    [input, generateId2, triggerRequest]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  const addToolResult = ({\n    toolCallId,\n    result\n  }) => {\n    const updatedMessages = messagesRef.current.map(\n      (message, index, arr) => (\n        // update the tool calls in the last assistant message:\n        index === arr.length - 1 && message.role === \"assistant\" && message.toolInvocations ? {\n          ...message,\n          toolInvocations: message.toolInvocations.map(\n            (toolInvocation) => toolInvocation.toolCallId === toolCallId ? { ...toolInvocation, result } : toolInvocation\n          )\n        } : message\n      )\n    );\n    mutate(updatedMessages, false);\n    const lastMessage = updatedMessages[updatedMessages.length - 1];\n    if (isAssistantMessageWithCompletedToolCalls(lastMessage)) {\n      triggerRequest({ messages: updatedMessages });\n    }\n  };\n  return {\n    messages: messages || [],\n    error,\n    append,\n    reload,\n    stop,\n    setMessages,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n    addToolResult,\n    experimental_addToolResult: addToolResult\n  };\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  return message.role === \"assistant\" && message.toolInvocations && message.toolInvocations.length > 0 && message.toolInvocations.every((toolInvocation) => \"result\" in toolInvocation);\n}\nfunction countTrailingAssistantMessages(messages) {\n  let count = 0;\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === \"assistant\") {\n      count++;\n    } else {\n      break;\n    }\n  }\n  return count;\n}\n\n// src/use-completion.ts\n\n\n\nfunction useCompletion({\n  api = \"/api/completion\",\n  id,\n  initialCompletion = \"\",\n  initialInput = \"\",\n  credentials,\n  headers,\n  body,\n  streamMode,\n  streamProtocol,\n  fetch: fetch2,\n  onResponse,\n  onFinish,\n  onError\n} = {}) {\n  if (streamMode) {\n    streamProtocol != null ? streamProtocol : streamProtocol = streamMode === \"text\" ? \"text\" : void 0;\n  }\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id || hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([api, completionId], null, {\n    fallbackData: initialCompletion\n  });\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [completionId, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([completionId, \"streamData\"], null);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const completion = data;\n  const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callCompletionApi)({\n      api,\n      prompt,\n      credentials: extraMetadataRef.current.credentials,\n      headers: { ...extraMetadataRef.current.headers, ...options == null ? void 0 : options.headers },\n      body: {\n        ...extraMetadataRef.current.body,\n        ...options == null ? void 0 : options.body\n      },\n      streamProtocol,\n      fetch: fetch2,\n      setCompletion: (completion2) => mutate(completion2, false),\n      setLoading: mutateLoading,\n      setError,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      onData: (data2) => {\n        mutateStreamData([...streamData || [], ...data2 || []], false);\n      }\n    }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch2,\n      mutateStreamData\n    ]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n  const setCompletion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (completion2) => {\n      mutate(completion2, false);\n    },\n    [mutate]\n  );\n  const complete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      return input ? complete(input) : void 0;\n    },\n    [input, complete]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData\n  };\n}\n\n// src/use-object.ts\n\n\n\n\nvar getOriginalFetch2 = () => fetch;\nfunction useObject({\n  api,\n  id,\n  schema,\n  // required, in the future we will use it for validation\n  initialValue,\n  fetch: fetch2,\n  onError,\n  onFinish\n}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id != null ? id : hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue }\n  );\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a;\n    try {\n      (_a = abortControllerRef.current) == null ? void 0 : _a.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const submit = async (input) => {\n    var _a;\n    try {\n      mutate(void 0);\n      setIsLoading(true);\n      setError(void 0);\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch2();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        signal: abortController.signal,\n        body: JSON.stringify(input)\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_a = await response.text()) != null ? _a : \"Failed to fetch the response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      let accumulatedText = \"\";\n      let latestObject = void 0;\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream({\n          write(chunk) {\n            accumulatedText += chunk;\n            const currentObject = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.parsePartialJson)(\n              accumulatedText\n            );\n            if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestObject, currentObject)) {\n              latestObject = currentObject;\n              mutate(currentObject);\n            }\n          },\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n            if (onFinish != null) {\n              const validationResult = schema.safeParse(latestObject);\n              onFinish(\n                validationResult.success ? { object: validationResult.data, error: void 0 } : { object: void 0, error: validationResult.error }\n              );\n            }\n          }\n        })\n      );\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2)) {\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    }\n  };\n  return {\n    setInput: submit,\n    // Deprecated\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop\n  };\n}\nvar experimental_useObject = useObject;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   createChunkDecoder: () => (/* binding */ createChunkDecoder),\n/* harmony export */   formatStreamPart: () => (/* binding */ formatStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   parseComplexResponse: () => (/* binding */ parseComplexResponse),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   parseStreamPart: () => (/* binding */ parseStreamPart),\n/* harmony export */   processChatStream: () => (/* binding */ processChatStream),\n/* harmony export */   readDataStream: () => (/* binding */ readDataStream)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/secure-json-parse/index.js\");\n// src/index.ts\n\n\n// src/parse-complex-response.ts\n\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText == null) {\n    return void 0;\n  }\n  try {\n    return secure_json_parse__WEBPACK_IMPORTED_MODULE_0__.parse(jsonText);\n  } catch (ignored) {\n    try {\n      const fixedJsonText = fixJson(jsonText);\n      return secure_json_parse__WEBPACK_IMPORTED_MODULE_0__.parse(fixedJsonText);\n    } catch (ignored2) {\n    }\n  }\n  return void 0;\n}\n\n// src/stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar functionCallStreamPart = {\n  code: \"1\",\n  name: \"function_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"function_call\" in value) || typeof value.function_call !== \"object\" || value.function_call == null || !(\"name\" in value.function_call) || !(\"arguments\" in value.function_call) || typeof value.function_call.name !== \"string\" || typeof value.function_call.arguments !== \"string\") {\n      throw new Error(\n        '\"function_call\" parts expect an object with a \"function_call\" property.'\n      );\n    }\n    return {\n      type: \"function_call\",\n      value\n    };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar toolCallsStreamPart = {\n  code: \"7\",\n  name: \"tool_calls\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"tool_calls\" in value) || typeof value.tool_calls !== \"object\" || value.tool_calls == null || !Array.isArray(value.tool_calls) || value.tool_calls.some(\n      (tc) => tc == null || typeof tc !== \"object\" || !(\"id\" in tc) || typeof tc.id !== \"string\" || !(\"type\" in tc) || typeof tc.type !== \"string\" || !(\"function\" in tc) || tc.function == null || typeof tc.function !== \"object\" || !(\"arguments\" in tc.function) || typeof tc.function.name !== \"string\" || typeof tc.function.arguments !== \"string\"\n    )) {\n      throw new Error(\n        '\"tool_calls\" parts expect an object with a ToolCallPayload.'\n      );\n    }\n    return {\n      type: \"tool_calls\",\n      value\n    };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\" || !(\"usage\" in value) || value.usage == null || typeof value.usage !== \"object\" || !(\"promptTokens\" in value.usage) || !(\"completionTokens\" in value.usage)) {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" and \"usage\" property.'\n      );\n    }\n    if (typeof value.usage.promptTokens !== \"number\") {\n      value.usage.promptTokens = Number.NaN;\n    }\n    if (typeof value.usage.completionTokens !== \"number\") {\n      value.usage.completionTokens = Number.NaN;\n    }\n    return {\n      type: \"finish_message\",\n      value\n    };\n  }\n};\nvar streamParts = [\n  textStreamPart,\n  functionCallStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n  toolCallsStreamPart,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart\n];\nvar streamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [functionCallStreamPart.code]: functionCallStreamPart,\n  [dataStreamPart.code]: dataStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n  [toolCallsStreamPart.code]: toolCallsStreamPart,\n  [messageAnnotationsStreamPart.code]: messageAnnotationsStreamPart,\n  [toolCallStreamPart.code]: toolCallStreamPart,\n  [toolResultStreamPart.code]: toolResultStreamPart,\n  [toolCallStreamingStartStreamPart.code]: toolCallStreamingStartStreamPart,\n  [toolCallDeltaStreamPart.code]: toolCallDeltaStreamPart,\n  [finishMessageStreamPart.code]: finishMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [functionCallStreamPart.name]: functionCallStreamPart.code,\n  [dataStreamPart.name]: dataStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n  [toolCallsStreamPart.name]: toolCallsStreamPart.code,\n  [messageAnnotationsStreamPart.name]: messageAnnotationsStreamPart.code,\n  [toolCallStreamPart.name]: toolCallStreamPart.code,\n  [toolResultStreamPart.name]: toolResultStreamPart.code,\n  [toolCallStreamingStartStreamPart.name]: toolCallStreamingStartStreamPart.code,\n  [toolCallDeltaStreamPart.name]: toolCallDeltaStreamPart.code,\n  [finishMessageStreamPart.name]: finishMessageStreamPart.code\n};\nvar validCodes = streamParts.map((part) => part.code);\nvar parseStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return streamPartsByCode[code].parse(jsonValue);\n};\nfunction formatStreamPart(type, value) {\n  const streamPart = streamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/read-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function* readDataStream(reader, {\n  isAborted\n} = {}) {\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts2 = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseStreamPart);\n    for (const streamPart of streamParts2) {\n      yield streamPart;\n    }\n    if (isAborted == null ? void 0 : isAborted()) {\n      reader.cancel();\n      break;\n    }\n  }\n}\n\n// src/parse-complex-response.ts\nfunction assignAnnotationsToMessage(message, annotations) {\n  if (!message || !annotations || !annotations.length)\n    return message;\n  return { ...message, annotations: [...annotations] };\n}\nasync function parseComplexResponse({\n  reader,\n  abortControllerRef,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date()\n}) {\n  var _a;\n  const createdAt = getCurrentDate();\n  const prefixMap = {\n    data: []\n  };\n  let message_annotations = void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  for await (const { type, value } of readDataStream(reader, {\n    isAborted: () => (abortControllerRef == null ? void 0 : abortControllerRef.current) === null\n  })) {\n    if (type === \"text\") {\n      if (prefixMap[\"text\"]) {\n        prefixMap[\"text\"] = {\n          ...prefixMap[\"text\"],\n          content: (prefixMap[\"text\"].content || \"\") + value\n        };\n      } else {\n        prefixMap[\"text\"] = {\n          id: generateId2(),\n          role: \"assistant\",\n          content: value,\n          createdAt\n        };\n      }\n    }\n    if (type === \"finish_message\") {\n      const { completionTokens, promptTokens } = value.usage;\n      finishReason = value.finishReason;\n      usage = {\n        completionTokens,\n        promptTokens,\n        totalTokens: completionTokens + promptTokens\n      };\n    }\n    if (type === \"tool_call_streaming_start\") {\n      if (prefixMap.text == null) {\n        prefixMap.text = {\n          id: generateId2(),\n          role: \"assistant\",\n          content: \"\",\n          createdAt\n        };\n      }\n      if (prefixMap.text.toolInvocations == null) {\n        prefixMap.text.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        toolName: value.toolName,\n        prefixMapIndex: prefixMap.text.toolInvocations.length\n      };\n      prefixMap.text.toolInvocations.push({\n        state: \"partial-call\",\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      });\n    } else if (type === \"tool_call_delta\") {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      prefixMap.text.toolInvocations[partialToolCall.prefixMapIndex] = {\n        state: \"partial-call\",\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: parsePartialJson(partialToolCall.text)\n      };\n      prefixMap.text.internalUpdateId = generateId2();\n    } else if (type === \"tool_call\") {\n      if (partialToolCalls[value.toolCallId] != null) {\n        prefixMap.text.toolInvocations[partialToolCalls[value.toolCallId].prefixMapIndex] = { state: \"call\", ...value };\n      } else {\n        if (prefixMap.text == null) {\n          prefixMap.text = {\n            id: generateId2(),\n            role: \"assistant\",\n            content: \"\",\n            createdAt\n          };\n        }\n        if (prefixMap.text.toolInvocations == null) {\n          prefixMap.text.toolInvocations = [];\n        }\n        prefixMap.text.toolInvocations.push({\n          state: \"call\",\n          ...value\n        });\n      }\n      prefixMap.text.internalUpdateId = generateId2();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          prefixMap.text.toolInvocations[prefixMap.text.toolInvocations.length - 1] = { state: \"result\", ...value, result };\n        }\n      }\n    } else if (type === \"tool_result\") {\n      const toolInvocations = (_a = prefixMap.text) == null ? void 0 : _a.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation) => invocation.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      toolInvocations[toolInvocationIndex] = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n    }\n    let functionCallMessage = null;\n    if (type === \"function_call\") {\n      prefixMap[\"function_call\"] = {\n        id: generateId2(),\n        role: \"assistant\",\n        content: \"\",\n        function_call: value.function_call,\n        name: value.function_call.name,\n        createdAt\n      };\n      functionCallMessage = prefixMap[\"function_call\"];\n    }\n    let toolCallMessage = null;\n    if (type === \"tool_calls\") {\n      prefixMap[\"tool_calls\"] = {\n        id: generateId2(),\n        role: \"assistant\",\n        content: \"\",\n        tool_calls: value.tool_calls,\n        createdAt\n      };\n      toolCallMessage = prefixMap[\"tool_calls\"];\n    }\n    if (type === \"data\") {\n      prefixMap[\"data\"].push(...value);\n    }\n    let responseMessage = prefixMap[\"text\"];\n    if (type === \"message_annotations\") {\n      if (!message_annotations) {\n        message_annotations = [...value];\n      } else {\n        message_annotations.push(...value);\n      }\n      functionCallMessage = assignAnnotationsToMessage(\n        prefixMap[\"function_call\"],\n        message_annotations\n      );\n      toolCallMessage = assignAnnotationsToMessage(\n        prefixMap[\"tool_calls\"],\n        message_annotations\n      );\n      responseMessage = assignAnnotationsToMessage(\n        prefixMap[\"text\"],\n        message_annotations\n      );\n    }\n    if (message_annotations == null ? void 0 : message_annotations.length) {\n      const messagePrefixKeys = [\n        \"text\",\n        \"function_call\",\n        \"tool_calls\"\n      ];\n      messagePrefixKeys.forEach((key) => {\n        if (prefixMap[key]) {\n          prefixMap[key].annotations = [...message_annotations];\n        }\n      });\n    }\n    const merged = [functionCallMessage, toolCallMessage, responseMessage].filter(Boolean).map((message) => ({\n      ...assignAnnotationsToMessage(message, message_annotations)\n    }));\n    update(merged, [...prefixMap[\"data\"]]);\n  }\n  onFinish == null ? void 0 : onFinish({ prefixMap, finishReason, usage });\n  return {\n    messages: [\n      prefixMap.text,\n      prefixMap.function_call,\n      prefixMap.tool_calls\n    ].filter(Boolean),\n    data: prefixMap.data\n  };\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch()\n}) {\n  var _a, _b;\n  const response = await fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }).catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_b = await response.text()) != null ? _b : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  const reader = response.body.getReader();\n  switch (streamProtocol) {\n    case \"text\": {\n      const decoder = createChunkDecoder();\n      const resultMessage = {\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"assistant\",\n        content: \"\"\n      };\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n          break;\n        }\n        resultMessage.content += decoder(value);\n        onUpdate([{ ...resultMessage }], []);\n        if ((abortController == null ? void 0 : abortController()) === null) {\n          reader.cancel();\n          break;\n        }\n      }\n      onFinish == null ? void 0 : onFinish(resultMessage, {\n        usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n        finishReason: \"unknown\"\n      });\n      return {\n        messages: [resultMessage],\n        data: []\n      };\n    }\n    case \"data\": {\n      return await parseComplexResponse({\n        reader,\n        abortControllerRef: abortController != null ? { current: abortController() } : void 0,\n        update: onUpdate,\n        onToolCall,\n        onFinish({ prefixMap, finishReason, usage }) {\n          if (onFinish && prefixMap.text != null) {\n            onFinish(prefixMap.text, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const res = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(res);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!res.ok) {\n      throw new Error(\n        await res.text() || \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!res.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    const reader = res.body.getReader();\n    switch (streamProtocol) {\n      case \"text\": {\n        const decoder = createChunkDecoder();\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) {\n            break;\n          }\n          result += decoder(value);\n          setCompletion(result);\n          if (abortController === null) {\n            reader.cancel();\n            break;\n          }\n        }\n        break;\n      }\n      case \"data\": {\n        for await (const { type, value } of readDataStream(reader, {\n          isAborted: () => abortController === null\n        })) {\n          switch (type) {\n            case \"text\": {\n              result += value;\n              setCompletion(result);\n              break;\n            }\n            case \"data\": {\n              onData == null ? void 0 : onData(value);\n              break;\n            }\n          }\n        }\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/create-chunk-decoder.ts\nfunction createChunkDecoder(complex) {\n  const decoder = new TextDecoder();\n  if (!complex) {\n    return function(chunk) {\n      if (!chunk)\n        return \"\";\n      return decoder.decode(chunk, { stream: true });\n    };\n  }\n  return function(chunk) {\n    const decoded = decoder.decode(chunk, { stream: true }).split(\"\\n\").filter((line) => line !== \"\");\n    return decoded.map(parseStreamPart).filter(Boolean);\n  };\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/process-chat-stream.ts\nasync function processChatStream({\n  getStreamedResponse,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  updateChatRequest,\n  getCurrentMessages\n}) {\n  while (true) {\n    const messagesAndDataOrJustMessage = await getStreamedResponse();\n    if (\"messages\" in messagesAndDataOrJustMessage) {\n      let hasFollowingResponse = false;\n      for (const message of messagesAndDataOrJustMessage.messages) {\n        if ((message.function_call === void 0 || typeof message.function_call === \"string\") && (message.tool_calls === void 0 || typeof message.tool_calls === \"string\")) {\n          continue;\n        }\n        hasFollowingResponse = true;\n        if (experimental_onFunctionCall) {\n          const functionCall = message.function_call;\n          if (typeof functionCall !== \"object\") {\n            console.warn(\n              \"experimental_onFunctionCall should not be defined when using tools\"\n            );\n            continue;\n          }\n          const functionCallResponse = await experimental_onFunctionCall(\n            getCurrentMessages(),\n            functionCall\n          );\n          if (functionCallResponse === void 0) {\n            hasFollowingResponse = false;\n            break;\n          }\n          updateChatRequest(functionCallResponse);\n        }\n        if (experimental_onToolCall) {\n          const toolCalls = message.tool_calls;\n          if (!Array.isArray(toolCalls) || toolCalls.some((toolCall) => typeof toolCall !== \"object\")) {\n            console.warn(\n              \"experimental_onToolCall should not be defined when using tools\"\n            );\n            continue;\n          }\n          const toolCallResponse = await experimental_onToolCall(getCurrentMessages(), toolCalls);\n          if (toolCallResponse === void 0) {\n            hasFollowingResponse = false;\n            break;\n          }\n          updateChatRequest(toolCallResponse);\n        }\n      }\n      if (!hasFollowingResponse) {\n        break;\n      }\n    } else {\n      let fixFunctionCallArguments2 = function(response) {\n        for (const message of response.messages) {\n          if (message.tool_calls !== void 0) {\n            for (const toolCall of message.tool_calls) {\n              if (typeof toolCall === \"object\") {\n                if (toolCall.function.arguments && typeof toolCall.function.arguments !== \"string\") {\n                  toolCall.function.arguments = JSON.stringify(\n                    toolCall.function.arguments\n                  );\n                }\n              }\n            }\n          }\n          if (message.function_call !== void 0) {\n            if (typeof message.function_call === \"object\") {\n              if (message.function_call.arguments && typeof message.function_call.arguments !== \"string\") {\n                message.function_call.arguments = JSON.stringify(\n                  message.function_call.arguments\n                );\n              }\n            }\n          }\n        }\n      };\n      var fixFunctionCallArguments = fixFunctionCallArguments2;\n      const streamedResponseMessage = messagesAndDataOrJustMessage;\n      if ((streamedResponseMessage.function_call === void 0 || typeof streamedResponseMessage.function_call === \"string\") && (streamedResponseMessage.tool_calls === void 0 || typeof streamedResponseMessage.tool_calls === \"string\")) {\n        break;\n      }\n      if (experimental_onFunctionCall) {\n        const functionCall = streamedResponseMessage.function_call;\n        if (!(typeof functionCall === \"object\")) {\n          console.warn(\n            \"experimental_onFunctionCall should not be defined when using tools\"\n          );\n          continue;\n        }\n        const functionCallResponse = await experimental_onFunctionCall(getCurrentMessages(), functionCall);\n        if (functionCallResponse === void 0)\n          break;\n        fixFunctionCallArguments2(functionCallResponse);\n        updateChatRequest(functionCallResponse);\n      }\n      if (experimental_onToolCall) {\n        const toolCalls = streamedResponseMessage.tool_calls;\n        if (!(typeof toolCalls === \"object\")) {\n          console.warn(\n            \"experimental_onToolCall should not be defined when using functions\"\n          );\n          continue;\n        }\n        const toolCallResponse = await experimental_onToolCall(getCurrentMessages(), toolCalls);\n        if (toolCallResponse === void 0)\n          break;\n        fixFunctionCallArguments2(toolCallResponse);\n        updateChatRequest(toolCallResponse);\n      }\n    }\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;