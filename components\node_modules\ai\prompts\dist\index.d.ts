import { Message } from '@ai-sdk/ui-utils';

/**
 * A prompt constructor for Anthropic models.
 * Does not support `function` messages.
 * @see https://docs.anthropic.com/claude/reference/getting-started-with-the-api
 */
declare function experimental_buildAnthropicPrompt(messages: Pick<Message, 'content' | 'role'>[]): string;
/**
 * A prompt constructor for Anthropic V3 models which require Messages API.
 * Does not support message with image content
 * @see https://docs.anthropic.com/claude/reference/messages_post
 */
declare function experimental_buildAnthropicMessages(messages: Pick<Message, 'content' | 'role'>[]): {
    role: "function" | "data" | "system" | "user" | "assistant" | "tool";
    content: {
        type: string;
        text: string;
    }[];
}[];

/**
 * A prompt constructor for the HuggingFace StarChat Beta model.
 * Does not support `function` messages.
 * @see https://huggingface.co/HuggingFaceH4/starchat-beta
 */
declare function experimental_buildStarChatBetaPrompt(messages: Pick<Message, 'content' | 'role'>[]): string;
/**
 * A prompt constructor for HuggingFace OpenAssistant models.
 * Does not support `function` or `system` messages.
 * @see https://huggingface.co/OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5
 */
declare function experimental_buildOpenAssistantPrompt(messages: Pick<Message, 'content' | 'role'>[]): string;
/**
 * A prompt constructor for HuggingFace LLama 2 chat models.
 * Does not support `function` messages.
 * @see https://huggingface.co/meta-llama/Llama-2-70b-chat-hf and https://huggingface.co/blog/llama2#how-to-prompt-llama-2
 */
declare function experimental_buildLlama2Prompt(messages: Pick<Message, 'content' | 'role'>[]): string;

declare function experimental_buildOpenAIMessages(messages: Message[]): ChatCompletionMessageParam[];
type ChatCompletionMessageParam = ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionToolMessageParam | ChatCompletionFunctionMessageParam;
interface ChatCompletionSystemMessageParam {
    /**
     * The contents of the system message.
     */
    content: string;
    /**
     * The role of the messages author, in this case `system`.
     */
    role: 'system';
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
}
interface ChatCompletionUserMessageParam {
    /**
     * The contents of the user message.
     */
    content: string | Array<ChatCompletionContentPart>;
    /**
     * The role of the messages author, in this case `user`.
     */
    role: 'user';
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
}
type ChatCompletionContentPart = ChatCompletionContentPartText | ChatCompletionContentPartImage;
interface ChatCompletionContentPartText {
    /**
     * The text content.
     */
    text: string;
    /**
     * The type of the content part.
     */
    type: 'text';
}
interface ChatCompletionContentPartImage {
    image_url: ChatCompletionContentPartImage.ImageURL;
    /**
     * The type of the content part.
     */
    type: 'image_url';
}
declare namespace ChatCompletionContentPartImage {
    interface ImageURL {
        /**
         * Either a URL of the image or the base64 encoded image data.
         */
        url: string;
        /**
         * Specifies the detail level of the image. Learn more in the
         * [Vision guide](https://platform.openai.com/docs/guides/vision/low-or-high-fidelity-image-understanding).
         */
        detail?: 'auto' | 'low' | 'high';
    }
}
interface ChatCompletionAssistantMessageParam {
    /**
     * The role of the messages author, in this case `assistant`.
     */
    role: 'assistant';
    /**
     * The contents of the assistant message. Required unless `tool_calls` or
     * `function_call` is specified.
     */
    content?: string | null;
    /**
     * @deprecated: Deprecated and replaced by `tool_calls`. The name and arguments of
     * a function that should be called, as generated by the model.
     */
    function_call?: ChatCompletionAssistantMessageParam.FunctionCall;
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
    /**
     * The tool calls generated by the model, such as function calls.
     */
    tool_calls?: Array<ChatCompletionMessageToolCall>;
}
declare namespace ChatCompletionAssistantMessageParam {
    /**
     * @deprecated: Deprecated and replaced by `tool_calls`. The name and arguments of
     * a function that should be called, as generated by the model.
     */
    interface FunctionCall {
        /**
         * The arguments to call the function with, as generated by the model in JSON
         * format. Note that the model does not always generate valid JSON, and may
         * hallucinate parameters not defined by your function schema. Validate the
         * arguments in your code before calling your function.
         */
        arguments: string;
        /**
         * The name of the function to call.
         */
        name: string;
    }
}
interface ChatCompletionMessageToolCall {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The function that the model called.
     */
    function: ChatCompletionMessageToolCall.Function;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
declare namespace ChatCompletionMessageToolCall {
    /**
     * The function that the model called.
     */
    interface Function {
        /**
         * The arguments to call the function with, as generated by the model in JSON
         * format. Note that the model does not always generate valid JSON, and may
         * hallucinate parameters not defined by your function schema. Validate the
         * arguments in your code before calling your function.
         */
        arguments: string;
        /**
         * The name of the function to call.
         */
        name: string;
    }
}
interface ChatCompletionToolMessageParam {
    /**
     * The contents of the tool message.
     */
    content: string;
    /**
     * The role of the messages author, in this case `tool`.
     */
    role: 'tool';
    /**
     * Tool call that this message is responding to.
     */
    tool_call_id: string;
}
interface ChatCompletionFunctionMessageParam {
    /**
     * The return value from the function call, to return to the model.
     */
    content: string | null;
    /**
     * The name of the function to call.
     */
    name: string;
    /**
     * The role of the messages author, in this case `function`.
     */
    role: 'function';
}

export { ChatCompletionAssistantMessageParam, ChatCompletionContentPart, ChatCompletionContentPartImage, ChatCompletionContentPartText, ChatCompletionFunctionMessageParam, ChatCompletionMessageParam, ChatCompletionMessageToolCall, ChatCompletionSystemMessageParam, ChatCompletionToolMessageParam, ChatCompletionUserMessageParam, experimental_buildAnthropicMessages, experimental_buildAnthropicPrompt, experimental_buildLlama2Prompt, experimental_buildOpenAIMessages, experimental_buildOpenAssistantPrompt, experimental_buildStarChatBetaPrompt };
