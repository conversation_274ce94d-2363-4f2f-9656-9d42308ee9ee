"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ChatPage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [response, setResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testAPI = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"发送请求到:\", \"http://localhost:8000/api/chat\");\n            const requestData = {\n                id: \"test-chat-\" + Date.now(),\n                messages: [\n                    {\n                        role: \"user\",\n                        content: message\n                    }\n                ]\n            };\n            console.log(\"请求数据:\", requestData);\n            const res = await fetch(\"http://localhost:8000/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    id: \"test-chat-\" + Date.now(),\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: message\n                        }\n                    ]\n                })\n            });\n            console.log(\"响应状态:\", res.status);\n            console.log(\"响应头:\", Object.fromEntries(res.headers.entries()));\n            if (!res.ok) {\n                const errorText = await res.text();\n                console.error(\"错误响应:\", errorText);\n                setResponse(\"错误 \".concat(res.status, \": \").concat(errorText));\n            } else {\n                const data = await res.text();\n                console.log(\"成功响应:\", data);\n                setResponse(data);\n            }\n        } catch (error) {\n            console.error(\"请求失败:\", error);\n            setResponse(\"请求失败: \".concat(error));\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col items-center justify-center bg-background p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"API 测试页面\"\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        placeholder: \"输入测试消息\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testAPI,\n                        disabled: loading || !message,\n                        className: \"w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400\",\n                        children: loading ? \"发送中...\" : \"测试 API\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    response && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full p-4 bg-gray-100 rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold mb-2\",\n                                children: \"响应:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"whitespace-pre-wrap text-sm\",\n                                children: response\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"HNQis6WOtjZGNtDhu8sPdyABpF4=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});