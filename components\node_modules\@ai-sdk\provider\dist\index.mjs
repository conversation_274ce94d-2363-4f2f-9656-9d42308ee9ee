// src/errors/api-call-error.ts
var APICallError = class extends Error {
  constructor({
    message,
    url,
    requestBodyValues,
    statusCode,
    responseHeaders,
    responseBody,
    cause,
    isRetryable = statusCode != null && (statusCode === 408 || // request timeout
    statusCode === 409 || // conflict
    statusCode === 429 || // too many requests
    statusCode >= 500),
    // server error
    data
  }) {
    super(message);
    this.name = "AI_APICallError";
    this.url = url;
    this.requestBodyValues = requestBodyValues;
    this.statusCode = statusCode;
    this.responseHeaders = responseHeaders;
    this.responseBody = responseBody;
    this.cause = cause;
    this.isRetryable = isRetryable;
    this.data = data;
  }
  static isAPICallError(error) {
    return error instanceof Error && error.name === "AI_APICallError" && typeof error.url === "string" && typeof error.requestBodyValues === "object" && (error.statusCode == null || typeof error.statusCode === "number") && (error.responseHeaders == null || typeof error.responseHeaders === "object") && (error.responseBody == null || typeof error.responseBody === "string") && (error.cause == null || typeof error.cause === "object") && typeof error.isRetryable === "boolean" && (error.data == null || typeof error.data === "object");
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      url: this.url,
      requestBodyValues: this.requestBodyValues,
      statusCode: this.statusCode,
      responseHeaders: this.responseHeaders,
      responseBody: this.responseBody,
      cause: this.cause,
      isRetryable: this.isRetryable,
      data: this.data
    };
  }
};

// src/errors/download-error.ts
var DownloadError = class extends Error {
  constructor({
    url,
    statusCode,
    statusText,
    cause,
    message = cause == null ? `Failed to download ${url}: ${statusCode} ${statusText}` : `Failed to download ${url}: ${cause}`
  }) {
    super(message);
    this.name = "AI_DownloadError";
    this.url = url;
    this.statusCode = statusCode;
    this.statusText = statusText;
    this.cause = cause;
  }
  static isDownloadError(error) {
    return error instanceof Error && error.name === "AI_DownloadError" && typeof error.url === "string" && (error.statusCode == null || typeof error.statusCode === "number") && (error.statusText == null || typeof error.statusText === "string");
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      url: this.url,
      statusCode: this.statusCode,
      statusText: this.statusText,
      cause: this.cause
    };
  }
};

// src/errors/empty-response-body-error.ts
var EmptyResponseBodyError = class extends Error {
  constructor({ message = "Empty response body" } = {}) {
    super(message);
    this.name = "AI_EmptyResponseBodyError";
  }
  static isEmptyResponseBodyError(error) {
    return error instanceof Error && error.name === "AI_EmptyResponseBodyError";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack
    };
  }
};

// src/errors/invalid-argument-error.ts
var InvalidArgumentError = class extends Error {
  constructor({
    parameter,
    value,
    message
  }) {
    super(`Invalid argument for parameter ${parameter}: ${message}`);
    this.name = "AI_InvalidArgumentError";
    this.parameter = parameter;
    this.value = value;
  }
  static isInvalidArgumentError(error) {
    return error instanceof Error && error.name === "AI_InvalidArgumentError" && typeof error.parameter === "string" && typeof error.value === "string";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      parameter: this.parameter,
      value: this.value
    };
  }
};

// src/errors/invalid-data-content-error.ts
var InvalidDataContentError = class extends Error {
  constructor({
    content,
    cause,
    message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.`
  }) {
    super(message);
    this.name = "AI_InvalidDataContentError";
    this.cause = cause;
    this.content = content;
  }
  static isInvalidDataContentError(error) {
    return error instanceof Error && error.name === "AI_InvalidDataContentError" && error.content != null;
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      cause: this.cause,
      content: this.content
    };
  }
};

// src/errors/invalid-prompt-error.ts
var InvalidPromptError = class extends Error {
  constructor({ prompt: prompt2, message }) {
    super(`Invalid prompt: ${message}`);
    this.name = "AI_InvalidPromptError";
    this.prompt = prompt2;
  }
  static isInvalidPromptError(error) {
    return error instanceof Error && error.name === "AI_InvalidPromptError" && prompt != null;
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      prompt: this.prompt
    };
  }
};

// src/errors/invalid-response-data-error.ts
var InvalidResponseDataError = class extends Error {
  constructor({
    data,
    message = `Invalid response data: ${JSON.stringify(data)}.`
  }) {
    super(message);
    this.name = "AI_InvalidResponseDataError";
    this.data = data;
  }
  static isInvalidResponseDataError(error) {
    return error instanceof Error && error.name === "AI_InvalidResponseDataError" && error.data != null;
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      data: this.data
    };
  }
};

// src/errors/get-error-message.ts
function getErrorMessage(error) {
  if (error == null) {
    return "unknown error";
  }
  if (typeof error === "string") {
    return error;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return JSON.stringify(error);
}

// src/errors/invalid-tool-arguments-error.ts
var InvalidToolArgumentsError = class extends Error {
  constructor({
    toolArgs,
    toolName,
    cause,
    message = `Invalid arguments for tool ${toolName}: ${getErrorMessage(
      cause
    )}`
  }) {
    super(message);
    this.name = "AI_InvalidToolArgumentsError";
    this.toolArgs = toolArgs;
    this.toolName = toolName;
    this.cause = cause;
  }
  static isInvalidToolArgumentsError(error) {
    return error instanceof Error && error.name === "AI_InvalidToolArgumentsError" && typeof error.toolName === "string" && typeof error.toolArgs === "string";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      cause: this.cause,
      stack: this.stack,
      toolName: this.toolName,
      toolArgs: this.toolArgs
    };
  }
};

// src/errors/json-parse-error.ts
var JSONParseError = class extends Error {
  constructor({ text, cause }) {
    super(
      `JSON parsing failed: Text: ${text}.
Error message: ${getErrorMessage(cause)}`
    );
    this.name = "AI_JSONParseError";
    this.cause = cause;
    this.text = text;
  }
  static isJSONParseError(error) {
    return error instanceof Error && error.name === "AI_JSONParseError" && typeof error.text === "string" && typeof error.cause === "string";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      cause: this.cause,
      stack: this.stack,
      valueText: this.text
    };
  }
};

// src/errors/load-api-key-error.ts
var LoadAPIKeyError = class extends Error {
  constructor({ message }) {
    super(message);
    this.name = "AI_LoadAPIKeyError";
  }
  static isLoadAPIKeyError(error) {
    return error instanceof Error && error.name === "AI_LoadAPIKeyError";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message
    };
  }
};

// src/errors/load-setting-error.ts
var LoadSettingError = class extends Error {
  constructor({ message }) {
    super(message);
    this.name = "AI_LoadSettingError";
  }
  static isLoadSettingError(error) {
    return error instanceof Error && error.name === "AI_LoadSettingError";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message
    };
  }
};

// src/errors/no-content-generated-error.ts
var NoContentGeneratedError = class extends Error {
  constructor({
    message = "No content generated."
  } = {}) {
    super(message);
    this.name = "AI_NoContentGeneratedError";
  }
  static isNoContentGeneratedError(error) {
    return error instanceof Error && error.name === "AI_NoContentGeneratedError";
  }
  toJSON() {
    return {
      name: this.name,
      cause: this.cause,
      message: this.message,
      stack: this.stack
    };
  }
};

// src/errors/no-object-generated-error.ts
var NoObjectGeneratedError = class extends Error {
  constructor({ message = "No object generated." } = {}) {
    super(message);
    this.name = "AI_NoObjectGeneratedError";
  }
  static isNoObjectGeneratedError(error) {
    return error instanceof Error && error.name === "AI_NoObjectGeneratedError";
  }
  toJSON() {
    return {
      name: this.name,
      cause: this.cause,
      message: this.message,
      stack: this.stack
    };
  }
};

// src/errors/no-such-tool-error.ts
var NoSuchToolError = class extends Error {
  constructor({
    toolName,
    availableTools = void 0,
    message = `Model tried to call unavailable tool '${toolName}'. ${availableTools === void 0 ? "No tools are available." : `Available tools: ${availableTools.join(", ")}.`}`
  }) {
    super(message);
    this.name = "AI_NoSuchToolError";
    this.toolName = toolName;
    this.availableTools = availableTools;
  }
  static isNoSuchToolError(error) {
    return error instanceof Error && error.name === "AI_NoSuchToolError" && "toolName" in error && error.toolName != void 0 && typeof error.name === "string";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      toolName: this.toolName,
      availableTools: this.availableTools
    };
  }
};

// src/errors/retry-error.ts
var RetryError = class extends Error {
  constructor({
    message,
    reason,
    errors
  }) {
    super(message);
    this.name = "AI_RetryError";
    this.reason = reason;
    this.errors = errors;
    this.lastError = errors[errors.length - 1];
  }
  static isRetryError(error) {
    return error instanceof Error && error.name === "AI_RetryError" && typeof error.reason === "string" && Array.isArray(error.errors);
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      reason: this.reason,
      lastError: this.lastError,
      errors: this.errors
    };
  }
};

// src/errors/too-many-embedding-values-for-call-error.ts
var TooManyEmbeddingValuesForCallError = class extends Error {
  constructor(options) {
    super(
      `Too many values for a single embedding call. The ${options.provider} model "${options.modelId}" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`
    );
    this.name = "AI_TooManyEmbeddingValuesForCallError";
    this.provider = options.provider;
    this.modelId = options.modelId;
    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;
    this.values = options.values;
  }
  static isInvalidPromptError(error) {
    return error instanceof Error && error.name === "AI_TooManyEmbeddingValuesForCallError" && "provider" in error && typeof error.provider === "string" && "modelId" in error && typeof error.modelId === "string" && "maxEmbeddingsPerCall" in error && typeof error.maxEmbeddingsPerCall === "number" && "values" in error && Array.isArray(error.values);
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      provider: this.provider,
      modelId: this.modelId,
      maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,
      values: this.values
    };
  }
};

// src/errors/tool-call-parse-error.ts
var ToolCallParseError = class extends Error {
  constructor({
    cause,
    text,
    tools,
    message = `Failed to parse tool calls: ${getErrorMessage(cause)}`
  }) {
    super(message);
    this.name = "AI_ToolCallParseError";
    this.cause = cause;
    this.text = text;
    this.tools = tools;
  }
  static isToolCallParseError(error) {
    return error instanceof Error && error.name === "AI_ToolCallParseError" && "cause" in error && error.cause != void 0 && "text" in error && error.text != void 0 && typeof error.text === "string" && "tools" in error && error.tools != void 0;
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      cause: this.cause,
      text: this.text,
      tools: this.tools
    };
  }
};

// src/errors/type-validation-error.ts
var TypeValidationError = class extends Error {
  constructor({ value, cause }) {
    super(
      `Type validation failed: Value: ${JSON.stringify(value)}.
Error message: ${getErrorMessage(cause)}`
    );
    this.name = "AI_TypeValidationError";
    this.cause = cause;
    this.value = value;
  }
  static isTypeValidationError(error) {
    return error instanceof Error && error.name === "AI_TypeValidationError";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      cause: this.cause,
      stack: this.stack,
      value: this.value
    };
  }
};

// src/errors/unsupported-functionality-error.ts
var UnsupportedFunctionalityError = class extends Error {
  constructor({ functionality }) {
    super(`'${functionality}' functionality not supported.`);
    this.name = "AI_UnsupportedFunctionalityError";
    this.functionality = functionality;
  }
  static isUnsupportedFunctionalityError(error) {
    return error instanceof Error && error.name === "AI_UnsupportedFunctionalityError" && typeof error.functionality === "string";
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      functionality: this.functionality
    };
  }
};

// src/errors/unsupported-json-schema-error.ts
var UnsupportedJSONSchemaError = class extends Error {
  constructor({
    schema,
    reason,
    message = `Unsupported JSON schema: ${reason}`
  }) {
    super(message);
    this.name = "AI_UnsupportedJSONSchemaError";
    this.reason = reason;
    this.schema = schema;
  }
  static isUnsupportedJSONSchemaError(error) {
    return error instanceof Error && error.name === "AI_UnsupportedJSONSchemaError" && "reason" in error && error.reason != void 0 && "schema" in error && error.schema !== void 0;
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      reason: this.reason,
      schema: this.schema
    };
  }
};
export {
  APICallError,
  DownloadError,
  EmptyResponseBodyError,
  InvalidArgumentError,
  InvalidDataContentError,
  InvalidPromptError,
  InvalidResponseDataError,
  InvalidToolArgumentsError,
  JSONParseError,
  LoadAPIKeyError,
  LoadSettingError,
  NoContentGeneratedError,
  NoObjectGeneratedError,
  NoSuchToolError,
  RetryError,
  TooManyEmbeddingValuesForCallError,
  ToolCallParseError,
  TypeValidationError,
  UnsupportedFunctionalityError,
  UnsupportedJSONSchemaError
};
//# sourceMappingURL=index.mjs.map