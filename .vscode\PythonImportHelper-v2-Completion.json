[{"label": "runpy", "kind": 6, "isExtraImport": true, "importPath": "runpy", "description": "runpy", "detail": "runpy", "documentation": {}}, {"label": "annotations", "importPath": "__future__", "description": "__future__", "isExtraImport": true, "detail": "__future__", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core.indices", "description": "llama_index.core.indices", "isExtraImport": true, "detail": "llama_index.core.indices", "documentation": {}}, {"label": "ChatRequest", "importPath": "llama_index.server.api.models", "description": "llama_index.server.api.models", "isExtraImport": true, "detail": "llama_index.server.api.models", "documentation": {}}, {"label": "ChatRequest", "importPath": "llama_index.server.api.models", "description": "llama_index.server.api.models", "isExtraImport": true, "detail": "llama_index.server.api.models", "documentation": {}}, {"label": "load_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "get_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "get_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "get_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "get_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "load_storage_context", "importPath": "app.storage_config", "description": "app.storage_config", "isExtraImport": true, "detail": "app.storage_config", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "sqlite3", "kind": 6, "isExtraImport": true, "importPath": "sqlite3", "description": "sqlite3", "detail": "sqlite3", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "BaseDocumentStore", "importPath": "llama_index.core.storage.docstore.types", "description": "llama_index.core.storage.docstore.types", "isExtraImport": true, "detail": "llama_index.core.storage.docstore.types", "documentation": {}}, {"label": "BaseIndexStore", "importPath": "llama_index.core.storage.index_store.types", "description": "llama_index.core.storage.index_store.types", "isExtraImport": true, "detail": "llama_index.core.storage.index_store.types", "documentation": {}}, {"label": "BaseNode", "importPath": "llama_index.core.schema", "description": "llama_index.core.schema", "isExtraImport": true, "detail": "llama_index.core.schema", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core.schema", "description": "llama_index.core.schema", "isExtraImport": true, "detail": "llama_index.core.schema", "documentation": {}}, {"label": "TextNode", "importPath": "llama_index.core.schema", "description": "llama_index.core.schema", "isExtraImport": true, "detail": "llama_index.core.schema", "documentation": {}}, {"label": "TextNode", "importPath": "llama_index.core.schema", "description": "llama_index.core.schema", "isExtraImport": true, "detail": "llama_index.core.schema", "documentation": {}}, {"label": "IndexStruct", "importPath": "llama_index.core.data_structs.data_structs", "description": "llama_index.core.data_structs.data_structs", "isExtraImport": true, "detail": "llama_index.core.data_structs.data_structs", "documentation": {}}, {"label": "StorageContext", "importPath": "llama_index.core.storage.storage_context", "description": "llama_index.core.storage.storage_context", "isExtraImport": true, "detail": "llama_index.core.storage.storage_context", "documentation": {}}, {"label": "ChromaVectorStore", "importPath": "llama_index.vector_stores.chroma", "description": "llama_index.vector_stores.chroma", "isExtraImport": true, "detail": "llama_index.vector_stores.chroma", "documentation": {}}, {"label": "chromadb", "kind": 6, "isExtraImport": true, "importPath": "chromadb", "description": "chromadb", "detail": "chromadb", "documentation": {}}, {"label": "Settings", "importPath": "chromadb.config", "description": "chromadb.config", "isExtraImport": true, "detail": "chromadb.config", "documentation": {}}, {"label": "Settings", "importPath": "chromadb.config", "description": "chromadb.config", "isExtraImport": true, "detail": "chromadb.config", "documentation": {}}, {"label": "Settings", "importPath": "chromadb.config", "description": "chromadb.config", "isExtraImport": true, "detail": "chromadb.config", "documentation": {}}, {"label": "Settings", "importPath": "chromadb.config", "description": "chromadb.config", "isExtraImport": true, "detail": "chromadb.config", "documentation": {}}, {"label": "SQLiteDocumentStore", "importPath": "app.sqlite_stores", "description": "app.sqlite_stores", "isExtraImport": true, "detail": "app.sqlite_stores", "documentation": {}}, {"label": "SQLiteIndexStore", "importPath": "app.sqlite_stores", "description": "app.sqlite_stores", "isExtraImport": true, "detail": "app.sqlite_stores", "documentation": {}}, {"label": "get_index", "importPath": "app.index", "description": "app.index", "isExtraImport": true, "detail": "app.index", "documentation": {}}, {"label": "STORAGE_DIR", "importPath": "app.index", "description": "app.index", "isExtraImport": true, "detail": "app.index", "documentation": {}}, {"label": "get_index", "importPath": "app.index", "description": "app.index", "isExtraImport": true, "detail": "app.index", "documentation": {}}, {"label": "STORAGE_DIR", "importPath": "app.index", "description": "app.index", "isExtraImport": true, "detail": "app.index", "documentation": {}}, {"label": "AgentWorkflow", "importPath": "llama_index.core.agent.workflow", "description": "llama_index.core.agent.workflow", "isExtraImport": true, "detail": "llama_index.core.agent.workflow", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core.settings", "description": "llama_index.core.settings", "isExtraImport": true, "detail": "llama_index.core.settings", "documentation": {}}, {"label": "get_query_engine_tool", "importPath": "llama_index.server.tools.index", "description": "llama_index.server.tools.index", "isExtraImport": true, "detail": "llama_index.server.tools.index", "documentation": {}}, {"label": "CITATION_SYSTEM_PROMPT", "importPath": "llama_index.server.tools.index.citation", "description": "llama_index.server.tools.index.citation", "isExtraImport": true, "detail": "llama_index.server.tools.index.citation", "documentation": {}}, {"label": "enable_citation", "importPath": "llama_index.server.tools.index.citation", "description": "llama_index.server.tools.index.citation", "isExtraImport": true, "detail": "llama_index.server.tools.index.citation", "documentation": {}}, {"label": "TTFont", "importPath": "fontTools.ttLib", "description": "fontTools.ttLib", "isExtraImport": true, "detail": "fontTools.ttLib", "documentation": {}}, {"label": "sfnt", "importPath": "fontTools.ttLib", "description": "fontTools.ttLib", "isExtraImport": true, "detail": "fontTools.ttLib", "documentation": {}}, {"label": "TTFont", "importPath": "fontTools.ttLib", "description": "fontTools.ttLib", "isExtraImport": true, "detail": "fontTools.ttLib", "documentation": {}}, {"label": "timestampNow", "importPath": "fontTools.misc.timeTools", "description": "fontTools.misc.timeTools", "isExtraImport": true, "detail": "fontTools.misc.timeTools", "documentation": {}}, {"label": "collections", "kind": 6, "isExtraImport": true, "importPath": "collections", "description": "collections", "detail": "collections", "documentation": {}}, {"label": "Counter", "importPath": "collections", "description": "collections", "isExtraImport": true, "detail": "collections", "documentation": {}}, {"label": "parse_tfm", "kind": 6, "isExtraImport": true, "importPath": "parse_tfm", "description": "parse_tfm", "detail": "parse_tfm", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "SimpleDirectoryReader", "importPath": "llama_index.core.readers", "description": "llama_index.core.readers", "isExtraImport": true, "detail": "llama_index.core.readers", "documentation": {}}, {"label": "SimpleDirectoryReader", "importPath": "llama_index.core.readers", "description": "llama_index.core.readers", "isExtraImport": true, "detail": "llama_index.core.readers", "documentation": {}}, {"label": "SimpleDirectoryReader", "importPath": "llama_index.core.readers", "description": "llama_index.core.readers", "isExtraImport": true, "detail": "llama_index.core.readers", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "SentenceSplitter", "importPath": "llama_index.core.node_parser", "description": "llama_index.core.node_parser", "isExtraImport": true, "detail": "llama_index.core.node_parser", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "init_settings", "importPath": "app.settings", "description": "app.settings", "isExtraImport": true, "detail": "app.settings", "documentation": {}}, {"label": "create_workflow", "importPath": "app.workflow", "description": "app.workflow", "isExtraImport": true, "detail": "app.workflow", "documentation": {}}, {"label": "LlamaIndexServer", "importPath": "llama_index.server", "description": "llama_index.server", "isExtraImport": true, "detail": "llama_index.server", "documentation": {}}, {"label": "UIConfig", "importPath": "llama_index.server", "description": "llama_index.server", "isExtraImport": true, "detail": "llama_index.server", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "StaticFiles", "importPath": "fastapi.staticfiles", "description": "fastapi.staticfiles", "isExtraImport": true, "detail": "fastapi.staticfiles", "documentation": {}}, {"label": "CORSMiddleware", "importPath": "fastapi.middleware.cors", "description": "fastapi.middleware.cors", "isExtraImport": true, "detail": "fastapi.middleware.cors", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "traceback", "kind": 6, "isExtraImport": true, "importPath": "traceback", "description": "traceback", "detail": "traceback", "documentation": {}}, {"label": "bin_dir", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "bin_dir = os.path.dirname(abs_file)\nbase = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"app\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "base", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "base = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"app\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"PATH\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"app\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"app\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV_PROMPT\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV_PROMPT\"] = \"app\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "prev_length", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "prev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.path[:]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.real_prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "get_index", "kind": 2, "importPath": "app.index", "description": "app.index", "peekOfCode": "def get_index(chat_request: Optional[ChatRequest] = None):\n    # check if storage already exists\n    if not os.path.exists(STORAGE_DIR):\n        return None\n    # load the existing storage context with SQLite and ChromaDB\n    logger.info(f\"Loading index from {STORAGE_DIR} using SQLite and ChromaDB...\")\n    storage_context = load_storage_context(STORAGE_DIR)\n    if storage_context is None:\n        logger.warning(f\"Could not load storage context from {STORAGE_DIR}\")\n        return None", "detail": "app.index", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.index", "description": "app.index", "peekOfCode": "logger = logging.getLogger(\"uvicorn\")\nSTORAGE_DIR = \"storage\"\ndef get_index(chat_request: Optional[ChatRequest] = None):\n    # check if storage already exists\n    if not os.path.exists(STORAGE_DIR):\n        return None\n    # load the existing storage context with SQLite and ChromaDB\n    logger.info(f\"Loading index from {STORAGE_DIR} using SQLite and ChromaDB...\")\n    storage_context = load_storage_context(STORAGE_DIR)\n    if storage_context is None:", "detail": "app.index", "documentation": {}}, {"label": "STORAGE_DIR", "kind": 5, "importPath": "app.index", "description": "app.index", "peekOfCode": "STORAGE_DIR = \"storage\"\ndef get_index(chat_request: Optional[ChatRequest] = None):\n    # check if storage already exists\n    if not os.path.exists(STORAGE_DIR):\n        return None\n    # load the existing storage context with SQLite and ChromaDB\n    logger.info(f\"Loading index from {STORAGE_DIR} using SQLite and ChromaDB...\")\n    storage_context = load_storage_context(STORAGE_DIR)\n    if storage_context is None:\n        logger.warning(f\"Could not load storage context from {STORAGE_DIR}\")", "detail": "app.index", "documentation": {}}, {"label": "init_settings", "kind": 2, "importPath": "app.settings", "description": "app.settings", "peekOfCode": "def init_settings():\n    if os.getenv(\"OPENAI_API_KEY\") is None:\n        raise RuntimeError(\"OPENAI_API_KEY is missing in environment variables\")\n    Settings.llm = OpenAI(model=\"gpt-4o-mini\")\n    Settings.embed_model = OpenAIEmbedding(model=\"text-embedding-3-large\")", "detail": "app.settings", "documentation": {}}, {"label": "SQLiteDocumentStore", "kind": 6, "importPath": "app.sqlite_stores", "description": "app.sqlite_stores", "peekOfCode": "class SQLiteDocumentStore(BaseDocumentStore):\n    \"\"\"SQLite-based document store for better performance and concurrency.\"\"\"\n    def __init__(self, db_path: str):\n        \"\"\"Initialize SQLite document store.\n        Args:\n            db_path: Path to SQLite database file\n        \"\"\"\n        self.db_path = db_path\n        logger.info(f\"🔥 Initializing SQLiteDocumentStore at {db_path}\")\n        self._init_db()", "detail": "app.sqlite_stores", "documentation": {}}, {"label": "SQLiteIndexStore", "kind": 6, "importPath": "app.sqlite_stores", "description": "app.sqlite_stores", "peekOfCode": "class SQLiteIndexStore(BaseIndexStore):\n    \"\"\"SQLite-based index store for better performance and concurrency.\"\"\"\n    def __init__(self, db_path: str):\n        \"\"\"Initialize SQLite index store.\n        Args:\n            db_path: Path to SQLite database file\n        \"\"\"\n        self.db_path = db_path\n        self._init_db()\n    def _init_db(self):", "detail": "app.sqlite_stores", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.sqlite_stores", "description": "app.sqlite_stores", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SQLiteDocumentStore(BaseDocumentStore):\n    \"\"\"SQLite-based document store for better performance and concurrency.\"\"\"\n    def __init__(self, db_path: str):\n        \"\"\"Initialize SQLite document store.\n        Args:\n            db_path: Path to SQLite database file\n        \"\"\"\n        self.db_path = db_path\n        logger.info(f\"🔥 Initializing SQLiteDocumentStore at {db_path}\")", "detail": "app.sqlite_stores", "documentation": {}}, {"label": "get_storage_context", "kind": 2, "importPath": "app.storage_config", "description": "app.storage_config", "peekOfCode": "def get_storage_context(storage_dir: str = \"storage\") -> StorageContext:\n    \"\"\"\n    Create a storage context using SQLite for docstore/index store and ChromaDB for vector store.\n    Optimized for text-only processing - no graph store or image vector store.\n    Args:\n        storage_dir: Directory to store the databases\n    Returns:\n        StorageContext configured with SQLite and ChromaDB backends\n    \"\"\"\n    # Ensure storage directory exists", "detail": "app.storage_config", "documentation": {}}, {"label": "load_storage_context", "kind": 2, "importPath": "app.storage_config", "description": "app.storage_config", "peekOfCode": "def load_storage_context(storage_dir: str = \"storage\") -> Optional[StorageContext]:\n    \"\"\"\n    Load existing storage context from ChromaDB and SQLite stores.\n    Args:\n        storage_dir: Directory containing the databases\n    Returns:\n        StorageContext if databases exist, None otherwise\n    \"\"\"\n    # Check if storage directory exists\n    if not os.path.exists(storage_dir):", "detail": "app.storage_config", "documentation": {}}, {"label": "migrate_json_to_sqlite", "kind": 2, "importPath": "app.storage_config", "description": "app.storage_config", "peekOfCode": "def migrate_json_to_sqlite(storage_dir: str = \"storage\") -> bool:\n    \"\"\"\n    Migrate existing JSON storage to SQLite.\n    Args:\n        storage_dir: Directory containing the storage files\n    Returns:\n        True if migration was successful, False otherwise\n    \"\"\"\n    import json\n    import sqlite3", "detail": "app.storage_config", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "app.storage_config", "description": "app.storage_config", "peekOfCode": "logger = logging.getLogger(__name__)\ndef get_storage_context(storage_dir: str = \"storage\") -> StorageContext:\n    \"\"\"\n    Create a storage context using SQLite for docstore/index store and ChromaDB for vector store.\n    Optimized for text-only processing - no graph store or image vector store.\n    Args:\n        storage_dir: Directory to store the databases\n    Returns:\n        StorageContext configured with SQLite and ChromaDB backends\n    \"\"\"", "detail": "app.storage_config", "documentation": {}}, {"label": "create_workflow", "kind": 2, "importPath": "app.workflow", "description": "app.workflow", "peekOfCode": "def create_workflow(chat_request: Optional[ChatRequest] = None) -> AgentWorkflow:\n    index = get_index(chat_request=chat_request)\n    if index is None:\n        raise RuntimeError(\n            \"Index not found! Please run `uv run generate` to index the data first.\"\n        )\n    # Create a query tool with citations enabled\n    query_tool = enable_citation(get_query_engine_tool(index=index))\n    # Define the system prompt for the agent\n    # Append the citation system prompt to the system prompt", "detail": "app.workflow", "documentation": {}}, {"label": "_Known", "kind": 6, "importPath": "components.node_modules.flatted.python.flatted", "description": "components.node_modules.flatted.python.flatted", "peekOfCode": "class _Known:\n    def __init__(self):\n        self.key = []\n        self.value = []\nclass _String:\n    def __init__(self, value):\n        self.value = value\ndef _array_keys(value):\n    keys = []\n    i = 0", "detail": "components.node_modules.flatted.python.flatted", "documentation": {}}, {"label": "_String", "kind": 6, "importPath": "components.node_modules.flatted.python.flatted", "description": "components.node_modules.flatted.python.flatted", "peekOfCode": "class _String:\n    def __init__(self, value):\n        self.value = value\ndef _array_keys(value):\n    keys = []\n    i = 0\n    for _ in value:\n        keys.append(i)\n        i += 1\n    return keys", "detail": "components.node_modules.flatted.python.flatted", "documentation": {}}, {"label": "parse", "kind": 2, "importPath": "components.node_modules.flatted.python.flatted", "description": "components.node_modules.flatted.python.flatted", "peekOfCode": "def parse(value, *args, **kwargs):\n    json = _json.loads(value, *args, **kwargs)\n    wrapped = []\n    for value in json:\n        wrapped.append(_wrap(value))\n    input = []\n    for value in wrapped:\n        if isinstance(value, _String):\n            input.append(value.value)\n        else:", "detail": "components.node_modules.flatted.python.flatted", "documentation": {}}, {"label": "stringify", "kind": 2, "importPath": "components.node_modules.flatted.python.flatted", "description": "components.node_modules.flatted.python.flatted", "peekOfCode": "def stringify(value, *args, **kwargs):\n    known = _Known()\n    input = []\n    output = []\n    i = int(_index(known, input, value))\n    while i < len(input):\n        output.append(_transform(known, input, input[i]))\n        i += 1\n    return _json.dumps(output, *args, **kwargs)", "detail": "components.node_modules.flatted.python.flatted", "documentation": {}}, {"label": "sfnt.USE_ZOPFLI", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "sfnt.USE_ZOPFLI = True\nif len(sys.argv) < 2:\n    print(\"Usage: %s <font file>\" % sys.argv[0])\n    sys.exit(1)\nfont_file = sys.argv[1]\nfont_name = os.path.splitext(os.path.basename(font_file))[0]\nfont = TTFont(font_file, recalcBBoxes=False, recalcTimestamp=False)\n# fix timestamp to the epoch\nfont['head'].created = 0\nfont['head'].modified = 0", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font_file", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font_file = sys.argv[1]\nfont_name = os.path.splitext(os.path.basename(font_file))[0]\nfont = TTFont(font_file, recalcBBoxes=False, recalcTimestamp=False)\n# fix timestamp to the epoch\nfont['head'].created = 0\nfont['head'].modified = 0\n# remove fontforge timestamps\nif 'FFTM' in font:\n    del font['FFTM']\n# remove redundant GDEF table", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font_name", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font_name = os.path.splitext(os.path.basename(font_file))[0]\nfont = TTFont(font_file, recalcBBoxes=False, recalcTimestamp=False)\n# fix timestamp to the epoch\nfont['head'].created = 0\nfont['head'].modified = 0\n# remove fontforge timestamps\nif 'FFTM' in font:\n    del font['FFTM']\n# remove redundant GDEF table\nif 'GDEF' in font:", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font = TTFont(font_file, recalcBBoxes=False, recalcTimestamp=False)\n# fix timestamp to the epoch\nfont['head'].created = 0\nfont['head'].modified = 0\n# remove fontforge timestamps\nif 'FFTM' in font:\n    del font['FFTM']\n# remove redundant GDEF table\nif 'GDEF' in font:\n    del font['GDEF']", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['head'].created", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['head'].created = 0\nfont['head'].modified = 0\n# remove fontforge timestamps\nif 'FFTM' in font:\n    del font['FFTM']\n# remove redundant GDEF table\nif 'GDEF' in font:\n    del font['GDEF']\n# remove Macintosh table\n# https://developer.apple.com/fonts/TrueType-Reference-Manual/RM06/Chap6cmap.html", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['head'].modified", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['head'].modified = 0\n# remove fontforge timestamps\nif 'FFTM' in font:\n    del font['FFTM']\n# remove redundant GDEF table\nif 'GDEF' in font:\n    del font['GDEF']\n# remove Macintosh table\n# https://developer.apple.com/fonts/TrueType-Reference-Manual/RM06/Chap6cmap.html\nfont['name'].names = [record for record in font['name'].names if record.platformID != 1]", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['name'].names", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['name'].names = [record for record in font['name'].names if record.platformID != 1]\nfont['cmap'].tables = [table for table in font['cmap'].tables if table.platformID != 1]\n# fix OS/2 and hhea metrics\nglyf = font['glyf']\nascent = int(max(glyf[c].yMax for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMax\")))\ndescent = -int(min(glyf[c].yMin for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMin\")))\nfont['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['cmap'].tables", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['cmap'].tables = [table for table in font['cmap'].tables if table.platformID != 1]\n# fix OS/2 and hhea metrics\nglyf = font['glyf']\nascent = int(max(glyf[c].yMax for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMax\")))\ndescent = -int(min(glyf[c].yMin for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMin\")))\nfont['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "glyf", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "glyf = font['glyf']\nascent = int(max(glyf[c].yMax for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMax\")))\ndescent = -int(min(glyf[c].yMin for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMin\")))\nfont['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "ascent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "ascent = int(max(glyf[c].yMax for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMax\")))\ndescent = -int(min(glyf[c].yMin for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMin\")))\nfont['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "descent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "descent = -int(min(glyf[c].yMin for c in font.getGlyphOrder() if hasattr(glyf[c], \"yMin\")))\nfont['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['OS/2'].usWinAscent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['OS/2'].usWinAscent = ascent\nfont['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)\n# save WOFF2", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['OS/2'].usWinDescent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['OS/2'].usWinDescent = descent\nfont['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)\n# save WOFF2\nfont.flavor = 'woff2'", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['hhea'].ascent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['hhea'].ascent = ascent\nfont['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)\n# save WOFF2\nfont.flavor = 'woff2'\nfont.save(os.path.join('woff2', font_name + '.woff2'), reorderTables=None)", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font['hhea'].descent", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font['hhea'].descent = -descent\n# save TTF\nfont.save(font_file, reorderTables=None)\n# save WOFF\nfont.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)\n# save WOFF2\nfont.flavor = 'woff2'\nfont.save(os.path.join('woff2', font_name + '.woff2'), reorderTables=None)", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font.flavor", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font.flavor = 'woff'\nfont.save(os.path.join('woff', font_name + '.woff'), reorderTables=None)\n# save WOFF2\nfont.flavor = 'woff2'\nfont.save(os.path.join('woff2', font_name + '.woff2'), reorderTables=None)", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "font.flavor", "kind": 5, "importPath": "components.node_modules.katex.src.fonts.generate_fonts", "description": "components.node_modules.katex.src.fonts.generate_fonts", "peekOfCode": "font.flavor = 'woff2'\nfont.save(os.path.join('woff2', font_name + '.woff2'), reorderTables=None)", "detail": "components.node_modules.katex.src.fonts.generate_fonts", "documentation": {}}, {"label": "find_font_path", "kind": 2, "importPath": "components.node_modules.katex.src.metrics.extract_tfms", "description": "components.node_modules.katex.src.metrics.extract_tfms", "peekOfCode": "def find_font_path(font_name):\n    try:\n        font_path = subprocess.check_output(['kpsewhich', font_name])\n    except OSError:\n        raise RuntimeError(\"Couldn't find kpsewhich program, make sure you\" +\n                           \" have TeX installed\")\n    except subprocess.CalledProcessError:\n        raise RuntimeError(\"Couldn't find font metrics: '%s'\" % font_name)\n    return font_path.strip()\ndef main():", "detail": "components.node_modules.katex.src.metrics.extract_tfms", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "components.node_modules.katex.src.metrics.extract_tfms", "description": "components.node_modules.katex.src.metrics.extract_tfms", "peekOfCode": "def main():\n    mapping = json.load(sys.stdin)\n    fonts = [\n        'cmbsy10.tfm',\n        'cmbx10.tfm',\n        'cmbxti10.tfm',\n        'cmex10.tfm',\n        'cmmi10.tfm',\n        'cmmib10.tfm',\n        'cmr10.tfm',", "detail": "components.node_modules.katex.src.metrics.extract_tfms", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "components.node_modules.katex.src.metrics.extract_ttfs", "description": "components.node_modules.katex.src.metrics.extract_ttfs", "peekOfCode": "def main():\n    start_json = json.load(sys.stdin)\n    for font in start_json:\n        fontInfo = TTFont(\"../../fonts/KaTeX_\" + font + \".ttf\")\n        glyf = fontInfo[\"glyf\"]\n        widths = fontInfo.getGlyphSet()\n        unitsPerEm = float(fontInfo[\"head\"].unitsPerEm)\n        # We keep ALL Unicode cmaps, not just fontInfo[\"cmap\"].getcmap(3, 1).\n        # This is playing it extra safe, since it reports inconsistencies.\n        # Platform 0 is Unicode, platform 3 is Windows. For platform 3,", "detail": "components.node_modules.katex.src.metrics.extract_ttfs", "documentation": {}}, {"label": "metrics_to_extract", "kind": 5, "importPath": "components.node_modules.katex.src.metrics.extract_ttfs", "description": "components.node_modules.katex.src.metrics.extract_ttfs", "peekOfCode": "metrics_to_extract = {\n    # Font name\n    \"AMS-Regular\": {\n        u\"\\u21e2\": None,  # \\dashrightarrow\n        u\"\\u21e0\": None,  # \\dashleftarrow\n    },\n    \"Main-Regular\": {\n        # Skew and italic metrics can't be easily parsed from the TTF. Instead,\n        # we map each character to a \"base character\", which is a character\n        # from the same font with correct italic and skew metrics. A character", "detail": "components.node_modules.katex.src.metrics.extract_ttfs", "documentation": {}}, {"label": "props", "kind": 5, "importPath": "components.node_modules.katex.src.metrics.format_json", "description": "components.node_modules.katex.src.metrics.format_json", "peekOfCode": "props = ['depth', 'height', 'italic', 'skew']\nif len(sys.argv) > 1:\n    if sys.argv[1] == '--width':\n        props.append('width')\ndata = json.load(sys.stdin)\nsys.stdout.write(\n  \"// This file is GENERATED by buildMetrics.sh. DO NOT MODIFY.\\n\")\nsep = \"export default {\\n    \"\nfor font in sorted(data):\n    sys.stdout.write(sep + json.dumps(font))", "detail": "components.node_modules.katex.src.metrics.format_json", "documentation": {}}, {"label": "data", "kind": 5, "importPath": "components.node_modules.katex.src.metrics.format_json", "description": "components.node_modules.katex.src.metrics.format_json", "peekOfCode": "data = json.load(sys.stdin)\nsys.stdout.write(\n  \"// This file is GENERATED by buildMetrics.sh. DO NOT MODIFY.\\n\")\nsep = \"export default {\\n    \"\nfor font in sorted(data):\n    sys.stdout.write(sep + json.dumps(font))\n    sep = \": {\\n        \"\n    for glyph in sorted(data[font], key=int):\n        sys.stdout.write(sep + json.dumps(glyph) + \": \")\n        values = [value if value != 0.0 else 0 for value in", "detail": "components.node_modules.katex.src.metrics.format_json", "documentation": {}}, {"label": "sep", "kind": 5, "importPath": "components.node_modules.katex.src.metrics.format_json", "description": "components.node_modules.katex.src.metrics.format_json", "peekOfCode": "sep = \"export default {\\n    \"\nfor font in sorted(data):\n    sys.stdout.write(sep + json.dumps(font))\n    sep = \": {\\n        \"\n    for glyph in sorted(data[font], key=int):\n        sys.stdout.write(sep + json.dumps(glyph) + \": \")\n        values = [value if value != 0.0 else 0 for value in\n                  [data[font][glyph][key] for key in props]]\n        sys.stdout.write(json.dumps(values))\n        sep = \",\\n        \"", "detail": "components.node_modules.katex.src.metrics.format_json", "documentation": {}}, {"label": "CharInfoWord", "kind": 6, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "class CharInfoWord(object):\n    def __init__(self, word):\n        b1, b2, b3, b4 = (word >> 24,\n                          (word & 0xff0000) >> 16,\n                          (word & 0xff00) >> 8,\n                          word & 0xff)\n        self.width_index = b1\n        self.height_index = b2 >> 4\n        self.depth_index = b2 & 0x0f\n        self.italic_index = (b3 & 0b11111100) >> 2", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "LigKernProgram", "kind": 6, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "class LigKernProgram(object):\n    def __init__(self, program):\n        self.program = program\n    def execute(self, start, next_char):\n        curr_instruction = start\n        while True:\n            instruction = self.program[curr_instruction]\n            (skip, inst_next_char, op, remainder) = instruction\n            if inst_next_char == next_char:\n                if op < 128:", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "TfmCharMetrics", "kind": 6, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "class TfmCharMetrics(object):\n    def __init__(self, width, height, depth, italic, kern_table):\n        self.width = width\n        self.height = height\n        self.depth = depth\n        self.italic_correction = italic\n        self.kern_table = kern_table\nclass TfmFile(object):\n    def __init__(self, start_char, end_char, char_info, width_table,\n                 height_table, depth_table, italic_table, ligkern_table,", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "TfmFile", "kind": 6, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "class TfmFile(object):\n    def __init__(self, start_char, end_char, char_info, width_table,\n                 height_table, depth_table, italic_table, ligkern_table,\n                 kern_table):\n        self.start_char = start_char\n        self.end_char = end_char\n        self.char_info = char_info\n        self.width_table = width_table\n        self.height_table = height_table\n        self.depth_table = depth_table", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "TfmReader", "kind": 6, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "class TfmReader(object):\n    def __init__(self, f):\n        self.f = f\n    def read_byte(self):\n        return ord(self.f.read(1))\n    def read_halfword(self):\n        b1 = self.read_byte()\n        b2 = self.read_byte()\n        return (b1 << 8) | b2\n    def read_word(self):", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "read_tfm_file", "kind": 2, "importPath": "components.node_modules.katex.src.metrics.parse_tfm", "description": "components.node_modules.katex.src.metrics.parse_tfm", "peekOfCode": "def read_tfm_file(file_name):\n    with open(file_name, 'rb') as f:\n        reader = TfmReader(f)\n        # file_size\n        reader.read_halfword()\n        header_size = reader.read_halfword()\n        start_char = reader.read_halfword()\n        end_char = reader.read_halfword()\n        width_table_size = reader.read_halfword()\n        height_table_size = reader.read_halfword()", "detail": "components.node_modules.katex.src.metrics.parse_tfm", "documentation": {}}, {"label": "analyze_data_field", "kind": 2, "importPath": "analyze_data_field", "description": "analyze_data_field", "peekOfCode": "def analyze_data_field():\n    \"\"\"分析 data 字段的内容和结构\"\"\"\n    print(\"🔍 分析 Documents 表的 data 字段\")\n    print(\"=\" * 60)\n    if not os.path.exists('storage/docstore.db'):\n        print(\"❌ docstore.db 不存在\")\n        return\n    conn = sqlite3.connect('storage/docstore.db')\n    cursor = conn.execute('SELECT doc_id, data FROM documents LIMIT 1')\n    row = cursor.fetchone()", "detail": "analyze_data_field", "documentation": {}}, {"label": "analyze_unicode_issue", "kind": 2, "importPath": "analyze_data_field", "description": "analyze_data_field", "peekOfCode": "def analyze_unicode_issue():\n    \"\"\"专门分析 Unicode 编码问题\"\"\"\n    print(\"\\n🔤 Unicode 编码问题分析\")\n    print(\"=\" * 60)\n    conn = sqlite3.connect('storage/docstore.db')\n    cursor = conn.execute('SELECT doc_id, data FROM documents LIMIT 3')\n    rows = cursor.fetchall()\n    for i, (doc_id, data_json) in enumerate(rows, 1):\n        try:\n            data = json.loads(data_json)", "detail": "analyze_data_field", "documentation": {}}, {"label": "suggest_solutions", "kind": 2, "importPath": "analyze_data_field", "description": "analyze_data_field", "peekOfCode": "def suggest_solutions():\n    \"\"\"提出解决方案建议\"\"\"\n    print(\"\\n💡 解决方案建议\")\n    print(\"=\" * 60)\n    print(\"🎯 data 字段优化方案:\")\n    print(\"1. 保持 LlamaIndex 标准格式，确保兼容性\")\n    print(\"2. 在后端 API 中解析 data 字段，提取需要的信息\")\n    print(\"3. 前端只接收处理后的简化数据，避免复杂的 Unicode 处理\")\n    print(\"\\n🔧 Unicode 处理方案:\")\n    print(\"1. 后端负责 Unicode 解码，确保文本正确显示\")", "detail": "analyze_data_field", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "analyze_data_field", "description": "analyze_data_field", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    analyze_data_field()\n    analyze_unicode_issue()\n    suggest_solutions()\nif __name__ == \"__main__\":\n    main()", "detail": "analyze_data_field", "documentation": {}}, {"label": "analyze_storage_directory", "kind": 2, "importPath": "analyze_storage_files", "description": "analyze_storage_files", "peekOfCode": "def analyze_storage_directory():\n    \"\"\"分析存储目录中的所有文件\"\"\"\n    storage_dir = Path(\"storage\")\n    print(\"📁 存储目录分析报告\")\n    print(\"=\" * 60)\n    # 1. 分析所有文件\n    print(\"📋 文件清单:\")\n    for file_path in storage_dir.rglob(\"*\"):\n        if file_path.is_file():\n            size = file_path.stat().st_size", "detail": "analyze_storage_files", "documentation": {}}, {"label": "analyze_sqlite_db", "kind": 2, "importPath": "analyze_storage_files", "description": "analyze_storage_files", "peekOfCode": "def analyze_sqlite_db(db_path):\n    \"\"\"分析SQLite数据库\"\"\"\n    try:\n        with sqlite3.connect(db_path) as conn:\n            cursor = conn.cursor()\n            # 获取表列表\n            cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table'\")\n            tables = [row[0] for row in cursor.fetchall()]\n            print(f\"    表数量: {len(tables)}\")\n            for table in tables:", "detail": "analyze_storage_files", "documentation": {}}, {"label": "analyze_json_file", "kind": 2, "importPath": "analyze_storage_files", "description": "analyze_storage_files", "peekOfCode": "def analyze_json_file(json_path):\n    \"\"\"分析JSON文件\"\"\"\n    try:\n        with open(json_path, 'r', encoding='utf-8') as f:\n            data = json.load(f)\n        print(f\"    文件大小: {json_path.stat().st_size:,} bytes\")\n        print(f\"    JSON结构:\")\n        def analyze_json_structure(obj, indent=6):\n            if isinstance(obj, dict):\n                print(f\"{' ' * indent}字典 - {len(obj)} 个键:\")", "detail": "analyze_storage_files", "documentation": {}}, {"label": "check_file_usage", "kind": 2, "importPath": "analyze_storage_files", "description": "analyze_storage_files", "peekOfCode": "def check_file_usage():\n    \"\"\"检查文件的实际使用情况\"\"\"\n    print(\"🔍 文件使用情况分析:\")\n    print(\"=\" * 60)\n    # 检查代码中对这些文件的引用\n    storage_files = [\n        \"docstore.db\",\n        \"index_store.db\", \n        \"chroma.sqlite3\",\n        \"graph_store.json\",", "detail": "analyze_storage_files", "documentation": {}}, {"label": "analyze_docstore", "kind": 2, "importPath": "analyze_storage_issues", "description": "analyze_storage_issues", "peekOfCode": "def analyze_docstore():\n    \"\"\"分析 docstore.db 的问题\"\"\"\n    print(\"🔍 分析 DOCSTORE.DB\")\n    print(\"=\" * 60)\n    if not os.path.exists('storage/docstore.db'):\n        print(\"❌ docstore.db 不存在\")\n        return\n    conn = sqlite3.connect('storage/docstore.db')\n    # 1. 检查 documents 表结构\n    print(\"📋 Documents 表结构:\")", "detail": "analyze_storage_issues", "documentation": {}}, {"label": "analyze_chroma", "kind": 2, "importPath": "analyze_storage_issues", "description": "analyze_storage_issues", "peekOfCode": "def analyze_chroma():\n    \"\"\"分析 ChromaDB 的问题\"\"\"\n    print(\"\\n🧠 分析 CHROMADB\")\n    print(\"=\" * 60)\n    chroma_path = 'storage/chroma_db_new'\n    if not os.path.exists(chroma_path):\n        print(\"❌ ChromaDB 不存在\")\n        return\n    try:\n        client = chromadb.PersistentClient(", "detail": "analyze_storage_issues", "documentation": {}}, {"label": "analyze_id_consistency", "kind": 2, "importPath": "analyze_storage_issues", "description": "analyze_storage_issues", "peekOfCode": "def analyze_id_consistency():\n    \"\"\"分析 docstore 和 chroma 之间的 ID 一致性\"\"\"\n    print(\"\\n🔗 分析 ID 一致性\")\n    print(\"=\" * 60)\n    # 获取 docstore 中的所有 doc_id\n    docstore_ids = set()\n    if os.path.exists('storage/docstore.db'):\n        conn = sqlite3.connect('storage/docstore.db')\n        cursor = conn.execute(\"SELECT doc_id FROM documents\")\n        docstore_ids = {row[0] for row in cursor.fetchall()}", "detail": "analyze_storage_issues", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "analyze_storage_issues", "description": "analyze_storage_issues", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🔍 存储逻辑问题分析\")\n    print(\"=\" * 80)\n    analyze_docstore()\n    analyze_chroma()\n    analyze_id_consistency()\n    print(\"\\n💡 问题总结:\")\n    print(\"1. 检查 documents 表是否真的用于存储文本块信息\")\n    print(\"2. 检查 chunk_index 是否正确设置\")", "detail": "analyze_storage_issues", "documentation": {}}, {"label": "check_chunk_index_issue", "kind": 2, "importPath": "check_chunk_index", "description": "check_chunk_index", "peekOfCode": "def check_chunk_index_issue():\n    \"\"\"检查 chunk_index 的分布和问题\"\"\"\n    print(\"🔍 检查 Documents 表中的 chunk_index 问题\")\n    print(\"=\" * 60)\n    with sqlite3.connect('storage/docstore.db') as conn:\n        # 1. 检查 chunk_index 分布\n        cursor = conn.execute('''\n            SELECT file_name, chunk_index, COUNT(*) as count\n            FROM documents \n            GROUP BY file_name, chunk_index ", "detail": "check_chunk_index", "documentation": {}}, {"label": "analyze_chunking_logic", "kind": 2, "importPath": "check_chunk_index", "description": "check_chunk_index", "peekOfCode": "def analyze_chunking_logic():\n    \"\"\"分析分块逻辑的问题\"\"\"\n    print(f\"\\n🔍 分析分块逻辑问题\")\n    print(\"=\" * 60)\n    print(\"📋 可能的原因:\")\n    print(\"1. generate.py 中的 chunk_index 计算逻辑有误\")\n    print(\"2. 每次运行 generate 都重新生成 node_id，导致重复\")\n    print(\"3. 文档分块器可能没有正确分块\")\n    print(\"4. chunk_index 赋值逻辑错误\")\n    print(f\"\\n🎯 正确的 chunk_index 应该是:\")", "detail": "check_chunk_index", "documentation": {}}, {"label": "check_generate_logic", "kind": 2, "importPath": "check_chunk_index", "description": "check_chunk_index", "peekOfCode": "def check_generate_logic():\n    \"\"\"检查 generate.py 中的逻辑\"\"\"\n    print(f\"\\n🔍 检查 generate.py 中的分块逻辑\")\n    print(\"=\" * 60)\n    try:\n        with open('generate.py', 'r', encoding='utf-8') as f:\n            content = f.read()\n        # 查找 chunk_index 相关的代码\n        lines = content.split('\\n')\n        chunk_index_lines = []", "detail": "check_chunk_index", "documentation": {}}, {"label": "suggest_fixes", "kind": 2, "importPath": "check_chunk_index", "description": "check_chunk_index", "peekOfCode": "def suggest_fixes():\n    \"\"\"建议修复方案\"\"\"\n    print(f\"\\n💡 修复建议\")\n    print(\"=\" * 60)\n    print(\"🔧 方案1: 修复 generate.py 中的 chunk_index 逻辑\")\n    print(\"- 确保为同一文件的不同块分配递增的 chunk_index\")\n    print(\"- 修改循环逻辑，正确计算块索引\")\n    print(f\"\\n🔧 方案2: 重新生成索引\")\n    print(\"- 先重置数据库\")\n    print(\"- 修复代码后重新运行 generate\")", "detail": "check_chunk_index", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "check_chunk_index", "description": "check_chunk_index", "peekOfCode": "def main():\n    \"\"\"主检查流程\"\"\"\n    check_chunk_index_issue()\n    analyze_chunking_logic()\n    check_generate_logic()\n    suggest_fixes()\nif __name__ == \"__main__\":\n    main()", "detail": "check_chunk_index", "documentation": {}}, {"label": "check_database_status", "kind": 2, "importPath": "check_db_duplicates", "description": "check_db_duplicates", "peekOfCode": "def check_database_status():\n    \"\"\"检查数据库状态和重复数据\"\"\"\n    db_path = \"storage/docstore.db\"\n    if not os.path.exists(db_path):\n        print(f\"❌ 数据库文件不存在: {db_path}\")\n        return\n    print(f\"📊 检查数据库: {db_path}\")\n    print(\"=\" * 50)\n    with sqlite3.connect(db_path) as conn:\n        cursor = conn.cursor()", "detail": "check_db_duplicates", "documentation": {}}, {"label": "suggest_cleanup_actions", "kind": 2, "importPath": "check_db_duplicates", "description": "check_db_duplicates", "peekOfCode": "def suggest_cleanup_actions():\n    \"\"\"建议清理操作\"\"\"\n    print(\"🛠️  数据库清理建议:\")\n    print(\"=\" * 50)\n    print(\"1. 🔄 完全重置数据库 (推荐)\")\n    print(\"   - 删除现有数据库文件\")\n    print(\"   - 重新初始化干净的数据库\")\n    print(\"   - 重新索引所有文档\")\n    print()\n    print(\"2. 🧹 清理重复数据\")", "detail": "check_db_duplicates", "documentation": {}}, {"label": "check_sqlite_data", "kind": 2, "importPath": "check_duplicates", "description": "check_duplicates", "peekOfCode": "def check_sqlite_data():\n    \"\"\"检查 SQLite 数据库中的数据\"\"\"\n    print(\"🔍 检查 SQLite 数据库...\")\n    # 检查 documents 表\n    with sqlite3.connect('storage/docstore.db') as conn:\n        cursor = conn.execute('SELECT COUNT(*) FROM documents')\n        doc_count = cursor.fetchone()[0]\n        print(f'📊 Documents 表中有 {doc_count} 条记录')\n        # 按文件分组统计\n        cursor = conn.execute('''", "detail": "check_duplicates", "documentation": {}}, {"label": "check_chromadb_data", "kind": 2, "importPath": "check_duplicates", "description": "check_duplicates", "peekOfCode": "def check_chromadb_data():\n    \"\"\"检查 ChromaDB 中的向量数据\"\"\"\n    print(\"\\n🔍 检查 ChromaDB 向量数据...\")\n    try:\n        chroma_client = chromadb.PersistentClient(\n            path='storage/chroma_db_new',\n            settings=ChromaSettings(\n                anonymized_telemetry=False,\n                allow_reset=True\n            )", "detail": "check_duplicates", "documentation": {}}, {"label": "analyze_duplication_issue", "kind": 2, "importPath": "check_duplicates", "description": "check_duplicates", "peekOfCode": "def analyze_duplication_issue():\n    \"\"\"分析重复数据问题\"\"\"\n    print(\"\\n🔍 分析重复数据问题...\")\n    # 检查 generate.py 的逻辑\n    print(\"📋 Generate 命令的行为:\")\n    print(\"1. 每次运行都会重新读取 data 目录中的所有文件\")\n    print(\"2. 为每个文档生成新的 node_id (UUID)\")\n    print(\"3. 使用 INSERT OR REPLACE 更新 SQLite 数据\")\n    print(\"4. 向 ChromaDB 添加新的向量（可能重复）\")\n    # 检查当前的重复情况", "detail": "check_duplicates", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "check_duplicates", "description": "check_duplicates", "peekOfCode": "def main():\n    \"\"\"主检查流程\"\"\"\n    print(\"🔍 开始检查数据库重复数据\")\n    print(\"=\" * 60)\n    check_sqlite_data()\n    check_chromadb_data()\n    analyze_duplication_issue()\n    print(\"\\n\" + \"=\" * 60)\n    print(\"🎯 结论:\")\n    print(\"重复运行 'uv run generate' 会导致:\")", "detail": "check_duplicates", "documentation": {}}, {"label": "analyze_storage_files", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def analyze_storage_files():\n    \"\"\"分析存储目录中的文件\"\"\"\n    storage_dir = \"storage\"\n    print(\"📁 存储目录文件分析\")\n    print(\"=\" * 60)\n    if not os.path.exists(storage_dir):\n        print(\"❌ 存储目录不存在\")\n        return\n    # 当前正在使用的文件（根据 storage_config.py）\n    current_files = {", "detail": "cleanup_storage", "documentation": {}}, {"label": "get_size_info", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def get_size_info(path):\n    \"\"\"获取文件或目录大小信息\"\"\"\n    try:\n        if os.path.isfile(path):\n            size = os.path.getsize(path)\n            return format_size(size)\n        elif os.path.isdir(path):\n            total_size = 0\n            for dirpath, dirnames, filenames in os.walk(path):\n                for filename in filenames:", "detail": "cleanup_storage", "documentation": {}}, {"label": "format_size", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def format_size(size_bytes):\n    \"\"\"格式化文件大小\"\"\"\n    if size_bytes == 0:\n        return \"0 B\"\n    for unit in ['B', 'KB', 'MB', 'GB']:\n        if size_bytes < 1024.0:\n            return f\"{size_bytes:.1f} {unit}\"\n        size_bytes /= 1024.0\n    return f\"{size_bytes:.1f} TB\"\ndef cleanup_storage():", "detail": "cleanup_storage", "documentation": {}}, {"label": "cleanup_storage", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def cleanup_storage():\n    \"\"\"清理存储目录\"\"\"\n    storage_dir = \"storage\"\n    print(\"\\n🧹 开始清理存储目录\")\n    print(\"=\" * 60)\n    # 要删除的文件和目录\n    items_to_delete = []\n    for item in os.listdir(storage_dir):\n        item_path = os.path.join(storage_dir, item)\n        # 检查是否应该删除", "detail": "cleanup_storage", "documentation": {}}, {"label": "verify_current_setup", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def verify_current_setup():\n    \"\"\"验证当前设置是否正常\"\"\"\n    print(\"\\n🔍 验证当前存储设置\")\n    print(\"=\" * 60)\n    storage_dir = \"storage\"\n    required_files = {\n        \"chroma_db_new\": \"ChromaDB 向量数据库目录\",\n        \"docstore.db\": \"SQLite 文档存储\",\n        \"index_store.db\": \"SQLite 索引存储\"\n    }", "detail": "cleanup_storage", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "def main():\n    \"\"\"主清理流程\"\"\"\n    print(\"🧹 存储目录清理工具\")\n    print(\"=\" * 60)\n    # 1. 分析文件\n    analyze_storage_files()\n    # 2. 清理文件\n    cleanup_storage()\n    # 3. 验证设置\n    verify_current_setup()", "detail": "cleanup_storage", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "cleanup_storage", "description": "cleanup_storage", "peekOfCode": "logger = logging.getLogger(__name__)\ndef analyze_storage_files():\n    \"\"\"分析存储目录中的文件\"\"\"\n    storage_dir = \"storage\"\n    print(\"📁 存储目录文件分析\")\n    print(\"=\" * 60)\n    if not os.path.exists(storage_dir):\n        print(\"❌ 存储目录不存在\")\n        return\n    # 当前正在使用的文件（根据 storage_config.py）", "detail": "cleanup_storage", "documentation": {}}, {"label": "clear_all_data", "kind": 2, "importPath": "clear_and_test", "description": "clear_and_test", "peekOfCode": "def clear_all_data():\n    \"\"\"清理所有数据\"\"\"\n    print(\"🧹 清理所有数据\")\n    print(\"=\" * 60)\n    print(\"⚠️  请先停止服务器，然后手动删除以下目录和文件:\")\n    print(f\"1. 删除存储目录: {STORAGE_DIR}\")\n    print(\"2. 删除 data 目录中的所有文件\")\n    print(\"3. 然后重新运行此脚本\")\n    # 检查是否已清理\n    if os.path.exists(STORAGE_DIR):", "detail": "clear_and_test", "documentation": {}}, {"label": "create_test_files", "kind": 2, "importPath": "clear_and_test", "description": "clear_and_test", "peekOfCode": "def create_test_files():\n    \"\"\"创建测试文件\"\"\"\n    print(\"\\n📝 创建测试文件\")\n    print(\"=\" * 60)\n    data_dir = \"data\"\n    os.makedirs(data_dir, exist_ok=True)\n    # 创建测试文件1\n    test_file1 = os.path.join(data_dir, \"test1.txt\")\n    with open(test_file1, \"w\", encoding=\"utf-8\") as f:\n        f.write(\"\"\"第一个测试文档", "detail": "clear_and_test", "documentation": {}}, {"label": "test_upload_and_check", "kind": 2, "importPath": "clear_and_test", "description": "clear_and_test", "peekOfCode": "def test_upload_and_check():\n    \"\"\"测试上传并检查结果\"\"\"\n    print(\"\\n🧪 测试上传并检查结果\")\n    print(\"=\" * 60)\n    # 这里我们需要手动上传文件，因为我们在脚本中\n    print(\"请通过以下步骤测试:\")\n    print(\"1. 启动服务器: uv fastapi run dev\")\n    print(\"2. 上传 data/test1.txt 文件\")\n    print(\"3. 上传 data/test2.txt 文件\")\n    print(\"4. 运行 python test_storage_fixes.py 检查结果\")", "detail": "clear_and_test", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "clear_and_test", "description": "clear_and_test", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🧪 清理数据库并测试修复\")\n    print(\"=\" * 80)\n    if clear_all_data():\n        create_test_files()\n        test_upload_and_check()\n    else:\n        print(\"\\n请先清理数据，然后重新运行脚本\")\nif __name__ == \"__main__\":", "detail": "clear_and_test", "documentation": {}}, {"label": "debug_chunking_process", "kind": 2, "importPath": "debug_chunking", "description": "debug_chunking", "peekOfCode": "def debug_chunking_process():\n    \"\"\"调试文档分块过程\"\"\"\n    print(\"🔍 调试文档分块过程\")\n    print(\"=\" * 60)\n    # 1. 读取原始文档\n    data_dir = os.environ.get(\"DATA_DIR\", \"data\")\n    reader = SimpleDirectoryReader(data_dir, recursive=True)\n    documents = reader.load_data()\n    print(f\"📄 读取到 {len(documents)} 个文档:\")\n    for i, doc in enumerate(documents):", "detail": "debug_chunking", "documentation": {}}, {"label": "check_database_vs_actual", "kind": 2, "importPath": "debug_chunking", "description": "debug_chunking", "peekOfCode": "def check_database_vs_actual():\n    \"\"\"对比数据库中的数据和实际分块结果\"\"\"\n    print(\"🔍 对比数据库数据和实际分块\")\n    print(\"=\" * 60)\n    # 检查数据库中的数据\n    with sqlite3.connect('storage/docstore.db') as conn:\n        cursor = conn.execute('''\n            SELECT file_name, COUNT(*) as db_chunks,\n                   COUNT(DISTINCT LENGTH(data)) as unique_lengths,\n                   MIN(LENGTH(data)) as min_length,", "detail": "debug_chunking", "documentation": {}}, {"label": "analyze_chunk_index_logic", "kind": 2, "importPath": "debug_chunking", "description": "debug_chunking", "peekOfCode": "def analyze_chunk_index_logic():\n    \"\"\"分析 chunk_index 逻辑\"\"\"\n    print(f\"\\n🔍 分析 chunk_index 逻辑问题\")\n    print(\"=\" * 60)\n    print(\"📋 generate.py 中的逻辑:\")\n    print(\"1. 读取所有文档\")\n    print(\"2. 使用 SentenceSplitter 分块\")\n    print(\"3. 为每个文档找到对应的节点: doc_nodes = [node for node in nodes if node.ref_doc_id == document.doc_id]\")\n    print(\"4. 为每个节点分配 chunk_index: 'chunk_index': i\")\n    print(f\"\\n🤔 可能的问题:\")", "detail": "debug_chunking", "documentation": {}}, {"label": "suggest_solution", "kind": 2, "importPath": "debug_chunking", "description": "debug_chunking", "peekOfCode": "def suggest_solution():\n    \"\"\"建议解决方案\"\"\"\n    print(f\"\\n💡 解决方案\")\n    print(\"=\" * 60)\n    print(\"🔧 立即解决:\")\n    print(\"1. 重置数据库清除重复数据\")\n    print(\"2. 检查文档分块设置\")\n    print(\"3. 确保 generate 只运行一次\")\n    print(f\"\\n🔧 长期改进:\")\n    print(\"1. 添加去重逻辑，避免重复处理相同文档\")", "detail": "debug_chunking", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "debug_chunking", "description": "debug_chunking", "peekOfCode": "def main():\n    \"\"\"主调试流程\"\"\"\n    debug_chunking_process()\n    check_database_vs_actual()\n    analyze_chunk_index_logic()\n    suggest_solution()\nif __name__ == \"__main__\":\n    main()", "detail": "debug_chunking", "documentation": {}}, {"label": "debug_chunk_index", "kind": 2, "importPath": "debug_chunk_index", "description": "debug_chunk_index", "peekOfCode": "def debug_chunk_index():\n    \"\"\"调试 chunk_index 问题\"\"\"\n    print(\"🔍 调试 chunk_index 问题\")\n    print(\"=\" * 60)\n    conn = sqlite3.connect('storage/docstore.db')\n    # 获取一个文档的详细信息\n    cursor = conn.execute(\"\"\"\n        SELECT doc_id, data, chunk_index, file_name \n        FROM documents \n        LIMIT 3", "detail": "debug_chunk_index", "documentation": {}}, {"label": "check_node_creation", "kind": 2, "importPath": "debug_chunk_index", "description": "debug_chunk_index", "peekOfCode": "def check_node_creation():\n    \"\"\"检查节点创建过程\"\"\"\n    print(\"\\n🔧 模拟节点创建过程\")\n    print(\"=\" * 60)\n    # 模拟 main.py 中的逻辑\n    print(\"模拟代码:\")\n    print(\"\"\"\n    for chunk_index, node in enumerate(nodes):\n        node.metadata.update({\n            'file_id': file_id,", "detail": "debug_chunk_index", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "debug_chunk_index", "description": "debug_chunk_index", "peekOfCode": "def main():\n    debug_chunk_index()\n    check_node_creation()\nif __name__ == \"__main__\":\n    main()", "detail": "debug_chunk_index", "documentation": {}}, {"label": "backup_chroma_db", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def backup_chroma_db(chroma_db_path):\n    \"\"\"备份 ChromaDB 目录\"\"\"\n    if not os.path.exists(chroma_db_path):\n        logger.info(\"ChromaDB 目录不存在，无需备份\")\n        return None\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    backup_path = f\"{chroma_db_path}_backup_{timestamp}\"\n    try:\n        shutil.copytree(chroma_db_path, backup_path)\n        logger.info(f\"✅ 已备份 ChromaDB 到: {backup_path}\")", "detail": "fix_chromadb", "documentation": {}}, {"label": "completely_remove_chromadb", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def completely_remove_chromadb(chroma_db_path):\n    \"\"\"完全删除 ChromaDB 目录\"\"\"\n    if not os.path.exists(chroma_db_path):\n        logger.info(\"ChromaDB 目录不存在\")\n        return True\n    try:\n        shutil.rmtree(chroma_db_path)\n        logger.info(f\"✅ 已完全删除 ChromaDB 目录: {chroma_db_path}\")\n        return True\n    except Exception as e:", "detail": "fix_chromadb", "documentation": {}}, {"label": "create_fresh_chromadb", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def create_fresh_chromadb(chroma_db_path):\n    \"\"\"创建全新的 ChromaDB\"\"\"\n    try:\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        logger.info(\"🔧 创建全新的 ChromaDB...\")\n        # 确保目录存在\n        os.makedirs(chroma_db_path, exist_ok=True)\n        # 创建新的 ChromaDB 客户端\n        chroma_client = chromadb.PersistentClient(", "detail": "fix_chromadb", "documentation": {}}, {"label": "verify_chromadb", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def verify_chromadb(chroma_db_path):\n    \"\"\"验证 ChromaDB 是否正常工作\"\"\"\n    try:\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        logger.info(\"🔍 验证 ChromaDB...\")\n        # 连接到 ChromaDB\n        chroma_client = chromadb.PersistentClient(\n            path=chroma_db_path,\n            settings=ChromaSettings(", "detail": "fix_chromadb", "documentation": {}}, {"label": "test_storage_context", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def test_storage_context():\n    \"\"\"测试存储上下文创建\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 测试存储上下文创建...\")\n        storage_context = get_storage_context(\"storage\")\n        logger.info(\"✅ 存储上下文创建成功\")\n        return True\n    except Exception as e:\n        logger.error(f\"❌ 存储上下文创建失败: {e}\")", "detail": "fix_chromadb", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "def main():\n    \"\"\"主修复流程\"\"\"\n    storage_dir = \"storage\"\n    chroma_db_path = os.path.join(storage_dir, \"chroma_db\")\n    print(\"🔧 开始修复 ChromaDB\")\n    print(\"=\" * 50)\n    # 1. 备份现有的 ChromaDB\n    backup_path = backup_chroma_db(chroma_db_path)\n    # 2. 完全删除现有的 ChromaDB\n    if not completely_remove_chromadb(chroma_db_path):", "detail": "fix_chromadb", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "fix_chromadb", "description": "fix_chromadb", "peekOfCode": "logger = logging.getLogger(__name__)\ndef backup_chroma_db(chroma_db_path):\n    \"\"\"备份 ChromaDB 目录\"\"\"\n    if not os.path.exists(chroma_db_path):\n        logger.info(\"ChromaDB 目录不存在，无需备份\")\n        return None\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    backup_path = f\"{chroma_db_path}_backup_{timestamp}\"\n    try:\n        shutil.copytree(chroma_db_path, backup_path)", "detail": "fix_chromadb", "documentation": {}}, {"label": "fix_chromadb_migrations", "kind": 2, "importPath": "fix_chromadb_migrations", "description": "fix_chromadb_migrations", "peekOfCode": "def fix_chromadb_migrations():\n    \"\"\"修复 ChromaDB 迁移状态\"\"\"\n    chroma_sqlite_path = os.path.join(\"storage\", \"chroma_db\", \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            logger.info(\"🔧 修复 ChromaDB 迁移状态...\")\n            # 检查 migrations 表", "detail": "fix_chromadb_migrations", "documentation": {}}, {"label": "test_chromadb_after_fix", "kind": 2, "importPath": "fix_chromadb_migrations", "description": "fix_chromadb_migrations", "peekOfCode": "def test_chromadb_after_fix():\n    \"\"\"修复后测试 ChromaDB\"\"\"\n    try:\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        logger.info(\"🔍 测试修复后的 ChromaDB...\")\n        chroma_db_path = os.path.join(\"storage\", \"chroma_db\")\n        # 创建客户端\n        chroma_client = chromadb.PersistentClient(\n            path=chroma_db_path,", "detail": "fix_chromadb_migrations", "documentation": {}}, {"label": "test_full_workflow", "kind": 2, "importPath": "fix_chromadb_migrations", "description": "fix_chromadb_migrations", "peekOfCode": "def test_full_workflow():\n    \"\"\"测试完整工作流\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 测试完整工作流...\")\n        # 测试存储上下文\n        storage_context = get_storage_context(\"storage\")\n        logger.info(\"✅ 存储上下文创建成功\")\n        return True\n    except Exception as e:", "detail": "fix_chromadb_migrations", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "fix_chromadb_migrations", "description": "fix_chromadb_migrations", "peekOfCode": "def main():\n    \"\"\"主修复流程\"\"\"\n    print(\"🔧 开始修复 ChromaDB 迁移状态\")\n    print(\"=\" * 50)\n    # 1. 修复 ChromaDB 迁移状态\n    if not fix_chromadb_migrations():\n        print(\"❌ 修复失败：无法修复 ChromaDB 迁移状态\")\n        return\n    # 2. 测试修复后的 ChromaDB\n    if not test_chromadb_after_fix():", "detail": "fix_chromadb_migrations", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "fix_chromadb_migrations", "description": "fix_chromadb_migrations", "peekOfCode": "logger = logging.getLogger(__name__)\ndef fix_chromadb_migrations():\n    \"\"\"修复 ChromaDB 迁移状态\"\"\"\n    chroma_sqlite_path = os.path.join(\"storage\", \"chroma_db\", \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            logger.info(\"🔧 修复 ChromaDB 迁移状态...\")", "detail": "fix_chromadb_migrations", "documentation": {}}, {"label": "force_cleanup_old_chroma", "kind": 2, "importPath": "force_cleanup", "description": "force_cleanup", "peekOfCode": "def force_cleanup_old_chroma():\n    \"\"\"强制清理旧的 chroma_db 目录\"\"\"\n    storage_dir = \"storage\"\n    old_chroma_path = os.path.join(storage_dir, \"chroma_db\")\n    if not os.path.exists(old_chroma_path):\n        print(\"✅ 旧的 chroma_db 目录已经不存在\")\n        return True\n    print(\"🔧 尝试强制清理旧的 chroma_db 目录...\")\n    # 方法1: 尝试重命名目录\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")", "detail": "force_cleanup", "documentation": {}}, {"label": "check_final_state", "kind": 2, "importPath": "force_cleanup", "description": "force_cleanup", "peekOfCode": "def check_final_state():\n    \"\"\"检查最终的存储状态\"\"\"\n    print(\"\\n🔍 检查最终存储状态\")\n    print(\"=\" * 50)\n    storage_dir = \"storage\"\n    if not os.path.exists(storage_dir):\n        print(\"❌ 存储目录不存在\")\n        return\n    print(\"📁 当前存储目录内容:\")\n    for item in sorted(os.listdir(storage_dir)):", "detail": "force_cleanup", "documentation": {}}, {"label": "format_size", "kind": 2, "importPath": "force_cleanup", "description": "force_cleanup", "peekOfCode": "def format_size(size_bytes):\n    \"\"\"格式化文件大小\"\"\"\n    if size_bytes == 0:\n        return \"0 B\"\n    for unit in ['B', 'KB', 'MB', 'GB']:\n        if size_bytes < 1024.0:\n            return f\"{size_bytes:.1f} {unit}\"\n        size_bytes /= 1024.0\n    return f\"{size_bytes:.1f} TB\"\ndef main():", "detail": "force_cleanup", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "force_cleanup", "description": "force_cleanup", "peekOfCode": "def main():\n    \"\"\"主清理流程\"\"\"\n    print(\"🧹 强制清理工具\")\n    print(\"=\" * 50)\n    # 强制清理旧的 ChromaDB\n    success = force_cleanup_old_chroma()\n    if success:\n        print(\"✅ 清理完成\")\n    else:\n        print(\"⚠️  部分清理完成，可能有文件仍被占用\")", "detail": "force_cleanup", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "force_cleanup", "description": "force_cleanup", "peekOfCode": "logger = logging.getLogger(__name__)\ndef force_cleanup_old_chroma():\n    \"\"\"强制清理旧的 chroma_db 目录\"\"\"\n    storage_dir = \"storage\"\n    old_chroma_path = os.path.join(storage_dir, \"chroma_db\")\n    if not os.path.exists(old_chroma_path):\n        print(\"✅ 旧的 chroma_db 目录已经不存在\")\n        return True\n    print(\"🔧 尝试强制清理旧的 chroma_db 目录...\")\n    # 方法1: 尝试重命名目录", "detail": "force_cleanup", "documentation": {}}, {"label": "force_move_chromadb", "kind": 2, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "def force_move_chromadb(chroma_db_path):\n    \"\"\"强制移动 ChromaDB 目录（绕过文件占用）\"\"\"\n    if not os.path.exists(chroma_db_path):\n        logger.info(\"ChromaDB 目录不存在\")\n        return True\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    temp_path = f\"{chroma_db_path}_old_{timestamp}\"\n    try:\n        # 尝试重命名目录\n        os.rename(chroma_db_path, temp_path)", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "create_fresh_chromadb_simple", "kind": 2, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "def create_fresh_chromadb_simple(chroma_db_path):\n    \"\"\"创建全新的 ChromaDB（简化版）\"\"\"\n    try:\n        logger.info(\"🔧 创建全新的 ChromaDB...\")\n        # 确保目录存在\n        os.makedirs(chroma_db_path, exist_ok=True)\n        # 创建一个简单的 SQLite 数据库文件\n        chroma_sqlite_path = os.path.join(chroma_db_path, \"chroma.sqlite3\")\n        # 创建基本的数据库结构\n        with sqlite3.connect(chroma_sqlite_path) as conn:", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "test_chromadb_with_retry", "kind": 2, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "def test_chromadb_with_retry(chroma_db_path):\n    \"\"\"测试 ChromaDB 连接（带重试）\"\"\"\n    try:\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        logger.info(\"🔍 测试 ChromaDB 连接...\")\n        # 创建 ChromaDB 客户端\n        chroma_client = chromadb.PersistentClient(\n            path=chroma_db_path,\n            settings=ChromaSettings(", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "test_full_storage_context", "kind": 2, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "def test_full_storage_context():\n    \"\"\"测试完整的存储上下文\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 测试完整存储上下文...\")\n        storage_context = get_storage_context(\"storage\")\n        logger.info(\"✅ 完整存储上下文创建成功\")\n        return True\n    except Exception as e:\n        logger.error(f\"❌ 完整存储上下文测试失败: {e}\")", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "def main():\n    \"\"\"主修复流程\"\"\"\n    storage_dir = \"storage\"\n    chroma_db_path = os.path.join(storage_dir, \"chroma_db\")\n    print(\"🔧 开始强制修复 ChromaDB\")\n    print(\"=\" * 50)\n    # 1. 强制移动现有的 ChromaDB 目录\n    if not force_move_chromadb(chroma_db_path):\n        print(\"❌ 修复失败：无法移动现有 ChromaDB\")\n        return", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "force_fix_chromadb", "description": "force_fix_chromadb", "peekOfCode": "logger = logging.getLogger(__name__)\ndef force_move_chromadb(chroma_db_path):\n    \"\"\"强制移动 ChromaDB 目录（绕过文件占用）\"\"\"\n    if not os.path.exists(chroma_db_path):\n        logger.info(\"ChromaDB 目录不存在\")\n        return True\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    temp_path = f\"{chroma_db_path}_old_{timestamp}\"\n    try:\n        # 尝试重命名目录", "detail": "force_fix_chromadb", "documentation": {}}, {"label": "force_remove_file", "kind": 2, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "def force_remove_file(file_path, max_attempts=5):\n    \"\"\"强制删除文件，处理被占用的情况\"\"\"\n    for attempt in range(max_attempts):\n        try:\n            if os.path.isfile(file_path):\n                os.remove(file_path)\n            elif os.path.isdir(file_path):\n                shutil.rmtree(file_path)\n            logger.info(f\"✅ 已删除: {file_path}\")\n            return True", "detail": "force_reset_database", "documentation": {}}, {"label": "force_reset_storage", "kind": 2, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "def force_reset_storage(storage_dir=\"storage\"):\n    \"\"\"强制重置存储目录\"\"\"\n    logger.info(\"🔄 开始强制重置存储...\")\n    if not os.path.exists(storage_dir):\n        logger.info(f\"存储目录 {storage_dir} 不存在\")\n        return True\n    # 逐个删除文件\n    files_to_remove = []\n    for root, dirs, files in os.walk(storage_dir):\n        for file in files:", "detail": "force_reset_database", "documentation": {}}, {"label": "create_clean_storage_context", "kind": 2, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "def create_clean_storage_context():\n    \"\"\"创建干净的存储上下文\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 创建干净的存储上下文...\")\n        storage_context = get_storage_context(\"storage\")\n        # 验证创建结果\n        storage_files = [\n            \"storage/docstore.db\",\n            \"storage/index_store.db\",", "detail": "force_reset_database", "documentation": {}}, {"label": "verify_clean_state", "kind": 2, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "def verify_clean_state():\n    \"\"\"验证清理状态\"\"\"\n    logger.info(\"🔍 验证清理状态...\")\n    # 检查不应该存在的文件\n    unwanted_files = [\n        \"storage/graph_store.json\",\n        \"storage/image__vector_store.json\"\n    ]\n    clean = True\n    for file_path in unwanted_files:", "detail": "force_reset_database", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "def main():\n    \"\"\"主重置流程\"\"\"\n    print(\"🔄 强制数据库重置\")\n    print(\"=\" * 40)\n    # 1. 强制删除存储目录\n    if not force_reset_storage(\"storage\"):\n        print(\"❌ 强制重置失败\")\n        return\n    # 2. 创建干净的存储\n    if not create_clean_storage_context():", "detail": "force_reset_database", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "force_reset_database", "description": "force_reset_database", "peekOfCode": "logger = logging.getLogger(__name__)\ndef force_remove_file(file_path, max_attempts=5):\n    \"\"\"强制删除文件，处理被占用的情况\"\"\"\n    for attempt in range(max_attempts):\n        try:\n            if os.path.isfile(file_path):\n                os.remove(file_path)\n            elif os.path.isdir(file_path):\n                shutil.rmtree(file_path)\n            logger.info(f\"✅ 已删除: {file_path}\")", "detail": "force_reset_database", "documentation": {}}, {"label": "generate_index", "kind": 2, "importPath": "generate", "description": "generate", "peekOfCode": "def generate_index():\n    \"\"\"\n    Index the documents in the data directory using SQLite and ChromaDB.\n    \"\"\"\n    from app.index import STORAGE_DIR\n    from app.settings import init_settings\n    from app.storage_config import get_storage_context\n    from llama_index.core.indices import (\n        VectorStoreIndex,\n    )", "detail": "generate", "documentation": {}}, {"label": "generate_ui_for_workflow", "kind": 2, "importPath": "generate", "description": "generate", "peekOfCode": "def generate_ui_for_workflow():\n    \"\"\"\n    Generate UI for UIEventData event in app/workflow.py\n    \"\"\"\n    import asyncio\n    from main import COMPONENT_DIR\n    # To generate UI components for additional event types,\n    # import the corresponding data model (e.g., MyCustomEventData)\n    # and run the generate_ui_for_workflow function with the imported model.\n    # Make sure the output filename of the generated UI component matches the event type (here `ui_event`)", "detail": "generate", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "generate", "description": "generate", "peekOfCode": "logger = logging.getLogger()\ndef generate_index():\n    \"\"\"\n    Index the documents in the data directory using SQLite and ChromaDB.\n    \"\"\"\n    from app.index import STORAGE_DIR\n    from app.settings import init_settings\n    from app.storage_config import get_storage_context\n    from llama_index.core.indices import (\n        VectorStoreIndex,", "detail": "generate", "documentation": {}}, {"label": "generate_index_improved", "kind": 2, "importPath": "generate_improved", "description": "generate_improved", "peekOfCode": "def generate_index_improved():\n    \"\"\"\n    改进的索引生成函数，解决 chunk_index 和重复数据问题\n    \"\"\"\n    from app.index import STORAGE_DIR\n    from app.settings import init_settings\n    from app.storage_config import get_storage_context\n    from llama_index.core.indices import VectorStoreIndex\n    from llama_index.core.readers import SimpleDirectoryReader\n    from llama_index.core.node_parser import SentenceSplitter", "detail": "generate_improved", "documentation": {}}, {"label": "verify_chunk_index_in_db", "kind": 2, "importPath": "generate_improved", "description": "generate_improved", "peekOfCode": "def verify_chunk_index_in_db():\n    \"\"\"验证数据库中的 chunk_index\"\"\"\n    import sqlite3\n    try:\n        with sqlite3.connect('storage/docstore.db') as conn:\n            cursor = conn.execute('''\n                SELECT file_name, chunk_index, COUNT(*) as count\n                FROM documents \n                GROUP BY file_name, chunk_index \n                ORDER BY file_name, chunk_index", "detail": "generate_improved", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "generate_improved", "description": "generate_improved", "peekOfCode": "logger = logging.getLogger()\ndef generate_index_improved():\n    \"\"\"\n    改进的索引生成函数，解决 chunk_index 和重复数据问题\n    \"\"\"\n    from app.index import STORAGE_DIR\n    from app.settings import init_settings\n    from app.storage_config import get_storage_context\n    from llama_index.core.indices import VectorStoreIndex\n    from llama_index.core.readers import SimpleDirectoryReader", "detail": "generate_improved", "documentation": {}}, {"label": "create_app", "kind": 2, "importPath": "main", "description": "main", "peekOfCode": "def create_app():\n    app = LlamaIndexServer(\n        workflow_factory=create_workflow,  # A factory function that creates a new workflow for each request\n        ui_config=UIConfig(\n            enabled=False,  # 禁用默认UI\n        ),\n        logger=logger,\n        env=\"dev\",\n    )\n    # 添加自定义中间件来处理sources注解", "detail": "main", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "logger = logging.getLogger(\"uvicorn\")\n# A path to a directory where the customized UI code is stored\nCOMPONENT_DIR = \"components\"\ndef create_app():\n    app = LlamaIndexServer(\n        workflow_factory=create_workflow,  # A factory function that creates a new workflow for each request\n        ui_config=UIConfig(\n            enabled=False,  # 禁用默认UI\n        ),\n        logger=logger,", "detail": "main", "documentation": {}}, {"label": "COMPONENT_DIR", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "COMPONENT_DIR = \"components\"\ndef create_app():\n    app = LlamaIndexServer(\n        workflow_factory=create_workflow,  # A factory function that creates a new workflow for each request\n        ui_config=UIConfig(\n            enabled=False,  # 禁用默认UI\n        ),\n        logger=logger,\n        env=\"dev\",\n    )", "detail": "main", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "app = create_app()", "detail": "main", "documentation": {}}, {"label": "calculate_file_hash", "kind": 2, "importPath": "migrate_to_file_management", "description": "migrate_to_file_management", "peekOfCode": "def calculate_file_hash(file_path: str) -> str:\n    \"\"\"计算文件的MD5哈希值\"\"\"\n    hash_md5 = hashlib.md5()\n    try:\n        with open(file_path, \"rb\") as f:\n            for chunk in iter(lambda: f.read(4096), b\"\"):\n                hash_md5.update(chunk)\n        return hash_md5.hexdigest()\n    except Exception as e:\n        logger.warning(f\"Failed to calculate hash for {file_path}: {e}\")", "detail": "migrate_to_file_management", "documentation": {}}, {"label": "migrate_database", "kind": 2, "importPath": "migrate_to_file_management", "description": "migrate_to_file_management", "peekOfCode": "def migrate_database():\n    \"\"\"执行数据库迁移\"\"\"\n    db_path = \"storage/docstore.db\"\n    data_dir = \"data\"\n    if not os.path.exists(db_path):\n        logger.error(f\"Database file not found: {db_path}\")\n        return False\n    if not os.path.exists(data_dir):\n        logger.error(f\"Data directory not found: {data_dir}\")\n        return False", "detail": "migrate_to_file_management", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "migrate_to_file_management", "description": "migrate_to_file_management", "peekOfCode": "logger = logging.getLogger(__name__)\ndef calculate_file_hash(file_path: str) -> str:\n    \"\"\"计算文件的MD5哈希值\"\"\"\n    hash_md5 = hashlib.md5()\n    try:\n        with open(file_path, \"rb\") as f:\n            for chunk in iter(lambda: f.read(4096), b\"\"):\n                hash_md5.update(chunk)\n        return hash_md5.hexdigest()\n    except Exception as e:", "detail": "migrate_to_file_management", "documentation": {}}, {"label": "reset_chromadb_database", "kind": 2, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "def reset_chromadb_database():\n    \"\"\"重置 ChromaDB 数据库内容\"\"\"\n    chroma_sqlite_path = os.path.join(\"storage\", \"chroma_db\", \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            logger.info(\"🔧 重置 ChromaDB 数据库...\")\n            # 删除所有数据但保留表结构", "detail": "reinit_chromadb", "documentation": {}}, {"label": "test_chromadb_connection", "kind": 2, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "def test_chromadb_connection():\n    \"\"\"测试 ChromaDB 连接\"\"\"\n    try:\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        logger.info(\"🔍 测试 ChromaDB 连接...\")\n        chroma_db_path = os.path.join(\"storage\", \"chroma_db\")\n        # 创建客户端\n        chroma_client = chromadb.PersistentClient(\n            path=chroma_db_path,", "detail": "reinit_chromadb", "documentation": {}}, {"label": "test_storage_context", "kind": 2, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "def test_storage_context():\n    \"\"\"测试存储上下文\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 测试存储上下文...\")\n        storage_context = get_storage_context(\"storage\")\n        logger.info(\"✅ 存储上下文创建成功\")\n        return True\n    except Exception as e:\n        logger.error(f\"❌ 存储上下文测试失败: {e}\")", "detail": "reinit_chromadb", "documentation": {}}, {"label": "test_generate_command", "kind": 2, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "def test_generate_command():\n    \"\"\"测试 generate 命令的核心逻辑\"\"\"\n    try:\n        from app.index import STORAGE_DIR\n        from app.settings import init_settings\n        from app.storage_config import get_storage_context\n        from llama_index.core.readers import SimpleDirectoryReader\n        from dotenv import load_dotenv\n        logger.info(\"🔧 测试 generate 命令核心逻辑...\")\n        load_dotenv()", "detail": "reinit_chromadb", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "def main():\n    \"\"\"主修复流程\"\"\"\n    print(\"🔧 开始重新初始化 ChromaDB\")\n    print(\"=\" * 50)\n    # 1. 重置 ChromaDB 数据库\n    if not reset_chromadb_database():\n        print(\"❌ 修复失败：无法重置 ChromaDB 数据库\")\n        return\n    # 2. 测试 ChromaDB 连接\n    if not test_chromadb_connection():", "detail": "reinit_chromadb", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "reinit_chromadb", "description": "reinit_chromadb", "peekOfCode": "logger = logging.getLogger(__name__)\ndef reset_chromadb_database():\n    \"\"\"重置 ChromaDB 数据库内容\"\"\"\n    chroma_sqlite_path = os.path.join(\"storage\", \"chroma_db\", \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            logger.info(\"🔧 重置 ChromaDB 数据库...\")", "detail": "reinit_chromadb", "documentation": {}}, {"label": "backup_storage", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def backup_storage(storage_dir=\"storage\"):\n    \"\"\"备份当前存储目录\"\"\"\n    if not os.path.exists(storage_dir):\n        logger.info(f\"存储目录 {storage_dir} 不存在，无需备份\")\n        return None\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    backup_dir = f\"{storage_dir}_backup_{timestamp}\"\n    try:\n        shutil.copytree(storage_dir, backup_dir)\n        logger.info(f\"✅ 已备份存储目录到: {backup_dir}\")", "detail": "reset_database", "documentation": {}}, {"label": "analyze_current_storage", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def analyze_current_storage(storage_dir=\"storage\"):\n    \"\"\"分析当前存储状态\"\"\"\n    logger.info(\"📊 分析当前存储状态...\")\n    if not os.path.exists(storage_dir):\n        logger.info(\"存储目录不存在\")\n        return\n    # 统计文件大小\n    total_size = 0\n    file_count = 0\n    for root, dirs, files in os.walk(storage_dir):", "detail": "reset_database", "documentation": {}}, {"label": "remove_storage_directory", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def remove_storage_directory(storage_dir=\"storage\"):\n    \"\"\"删除存储目录\"\"\"\n    if not os.path.exists(storage_dir):\n        logger.info(f\"存储目录 {storage_dir} 不存在\")\n        return True\n    try:\n        shutil.rmtree(storage_dir)\n        logger.info(f\"🗑️  已删除存储目录: {storage_dir}\")\n        return True\n    except Exception as e:", "detail": "reset_database", "documentation": {}}, {"label": "create_clean_storage", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def create_clean_storage(storage_dir=\"storage\"):\n    \"\"\"创建干净的存储目录结构\"\"\"\n    try:\n        os.makedirs(storage_dir, exist_ok=True)\n        logger.info(f\"📁 已创建干净的存储目录: {storage_dir}\")\n        return True\n    except Exception as e:\n        logger.error(f\"❌ 创建存储目录失败: {e}\")\n        return False\ndef initialize_clean_databases(storage_dir=\"storage\"):", "detail": "reset_database", "documentation": {}}, {"label": "initialize_clean_databases", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def initialize_clean_databases(storage_dir=\"storage\"):\n    \"\"\"初始化干净的数据库\"\"\"\n    try:\n        from app.storage_config import get_storage_context\n        logger.info(\"🔧 初始化干净的存储上下文...\")\n        storage_context = get_storage_context(storage_dir)\n        # 验证数据库是否正确创建\n        docstore_path = os.path.join(storage_dir, \"docstore.db\")\n        index_store_path = os.path.join(storage_dir, \"index_store.db\")\n        chroma_path = os.path.join(storage_dir, \"chroma_db\")", "detail": "reset_database", "documentation": {}}, {"label": "verify_reset", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def verify_reset(storage_dir=\"storage\"):\n    \"\"\"验证重置结果\"\"\"\n    logger.info(\"🔍 验证重置结果...\")\n    if not os.path.exists(storage_dir):\n        logger.error(\"❌ 存储目录不存在\")\n        return False\n    # 检查必需的文件\n    required_files = [\n        \"docstore.db\",\n        \"index_store.db\",", "detail": "reset_database", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "def main():\n    \"\"\"主重置流程\"\"\"\n    storage_dir = \"storage\"\n    print(\"🔄 开始数据库重置流程\")\n    print(\"=\" * 50)\n    # 1. 分析当前状态\n    analyze_current_storage(storage_dir)\n    print()\n    # 2. 确认重置\n    response = input(\"⚠️  确定要重置数据库吗？这将删除所有现有数据！(y/N): \")", "detail": "reset_database", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "reset_database", "description": "reset_database", "peekOfCode": "logger = logging.getLogger(__name__)\ndef backup_storage(storage_dir=\"storage\"):\n    \"\"\"备份当前存储目录\"\"\"\n    if not os.path.exists(storage_dir):\n        logger.info(f\"存储目录 {storage_dir} 不存在，无需备份\")\n        return None\n    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n    backup_dir = f\"{storage_dir}_backup_{timestamp}\"\n    try:\n        shutil.copytree(storage_dir, backup_dir)", "detail": "reset_database", "documentation": {}}, {"label": "clear_chroma_sqlite", "kind": 2, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "def clear_chroma_sqlite(chroma_db_path):\n    \"\"\"直接清理 chroma.sqlite3 数据库\"\"\"\n    chroma_sqlite_path = os.path.join(chroma_db_path, \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在，跳过清理\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            # 获取所有表名\n            cursor = conn.execute(\"SELECT name FROM sqlite_master WHERE type='table'\")", "detail": "reset_data_complete", "documentation": {}}, {"label": "clear_chroma_vector_files", "kind": 2, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "def clear_chroma_vector_files(chroma_db_path):\n    \"\"\"清理 ChromaDB 向量文件\"\"\"\n    try:\n        deleted_dirs = []\n        for item in os.listdir(chroma_db_path):\n            item_path = os.path.join(chroma_db_path, item)\n            if os.path.isdir(item_path) and item != \"__pycache__\":\n                try:\n                    shutil.rmtree(item_path)\n                    deleted_dirs.append(item)", "detail": "reset_data_complete", "documentation": {}}, {"label": "clear_document_data_complete", "kind": 2, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "def clear_document_data_complete(storage_dir=\"storage\"):\n    \"\"\"完整清空文档数据，包括直接操作 chroma.sqlite3\"\"\"\n    logger.info(\"🔄 开始完整清空文档数据...\")\n    # 1. 清空SQLite数据库中的文档数据\n    docstore_path = os.path.join(storage_dir, \"docstore.db\")\n    if os.path.exists(docstore_path):\n        try:\n            with sqlite3.connect(docstore_path) as conn:\n                cursor = conn.execute(\"DELETE FROM documents\")\n                docs_deleted = cursor.rowcount", "detail": "reset_data_complete", "documentation": {}}, {"label": "verify_complete_reset", "kind": 2, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "def verify_complete_reset(storage_dir=\"storage\"):\n    \"\"\"验证完整重置结果\"\"\"\n    logger.info(\"🔍 验证完整重置结果...\")\n    # 检查docstore.db\n    docstore_path = os.path.join(storage_dir, \"docstore.db\")\n    if os.path.exists(docstore_path):\n        with sqlite3.connect(docstore_path) as conn:\n            cursor = conn.execute(\"SELECT COUNT(*) FROM documents\")\n            doc_count = cursor.fetchone()[0]\n            cursor = conn.execute(\"SELECT COUNT(*) FROM files\")", "detail": "reset_data_complete", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "def main():\n    \"\"\"主重置流程\"\"\"\n    storage_dir = \"storage\"\n    print(\"🔄 开始完整数据重置（包括 chroma.sqlite3）\")\n    print(\"=\" * 60)\n    # 1. 完整清空文档数据\n    if not clear_document_data_complete(storage_dir):\n        print(\"❌ 完整数据重置失败\")\n        return\n    # 2. 验证重置结果", "detail": "reset_data_complete", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "reset_data_complete", "description": "reset_data_complete", "peekOfCode": "logger = logging.getLogger(__name__)\ndef clear_chroma_sqlite(chroma_db_path):\n    \"\"\"直接清理 chroma.sqlite3 数据库\"\"\"\n    chroma_sqlite_path = os.path.join(chroma_db_path, \"chroma.sqlite3\")\n    if not os.path.exists(chroma_sqlite_path):\n        logger.info(\"chroma.sqlite3 不存在，跳过清理\")\n        return True\n    try:\n        with sqlite3.connect(chroma_sqlite_path) as conn:\n            # 获取所有表名", "detail": "reset_data_complete", "documentation": {}}, {"label": "clear_document_data", "kind": 2, "importPath": "reset_data_only", "description": "reset_data_only", "peekOfCode": "def clear_document_data(storage_dir=\"storage\"):\n    \"\"\"清空文档数据，保留配置和表结构\"\"\"\n    logger.info(\"🔄 开始清空文档数据...\")\n    # 1. 清空SQLite数据库中的文档数据\n    docstore_path = os.path.join(storage_dir, \"docstore.db\")\n    if os.path.exists(docstore_path):\n        try:\n            with sqlite3.connect(docstore_path) as conn:\n                # 清空文档相关表的数据，但保留表结构\n                cursor = conn.execute(\"DELETE FROM documents\")", "detail": "reset_data_only", "documentation": {}}, {"label": "verify_data_reset", "kind": 2, "importPath": "reset_data_only", "description": "reset_data_only", "peekOfCode": "def verify_data_reset(storage_dir=\"storage\"):\n    \"\"\"验证数据重置结果\"\"\"\n    logger.info(\"🔍 验证数据重置结果...\")\n    # 检查docstore.db\n    docstore_path = os.path.join(storage_dir, \"docstore.db\")\n    if os.path.exists(docstore_path):\n        with sqlite3.connect(docstore_path) as conn:\n            cursor = conn.execute(\"SELECT COUNT(*) FROM documents\")\n            doc_count = cursor.fetchone()[0]\n            cursor = conn.execute(\"SELECT COUNT(*) FROM files\")", "detail": "reset_data_only", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "reset_data_only", "description": "reset_data_only", "peekOfCode": "def main():\n    \"\"\"主重置流程\"\"\"\n    storage_dir = \"storage\"\n    print(\"🔄 开始数据重置（保留配置）\")\n    print(\"=\" * 50)\n    # 1. 清空文档数据\n    if not clear_document_data(storage_dir):\n        print(\"❌ 数据重置失败\")\n        return\n    # 2. 验证重置结果", "detail": "reset_data_only", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "reset_data_only", "description": "reset_data_only", "peekOfCode": "logger = logging.getLogger(__name__)\ndef clear_document_data(storage_dir=\"storage\"):\n    \"\"\"清空文档数据，保留配置和表结构\"\"\"\n    logger.info(\"🔄 开始清空文档数据...\")\n    # 1. 清空SQLite数据库中的文档数据\n    docstore_path = os.path.join(storage_dir, \"docstore.db\")\n    if os.path.exists(docstore_path):\n        try:\n            with sqlite3.connect(docstore_path) as conn:\n                # 清空文档相关表的数据，但保留表结构", "detail": "reset_data_only", "documentation": {}}, {"label": "simple_reset", "kind": 2, "importPath": "simple_reset", "description": "simple_reset", "peekOfCode": "def simple_reset():\n    \"\"\"简单重置数据库\"\"\"\n    storage_dir = \"storage\"\n    print(\"🔄 开始简单数据库重置\")\n    print(\"=\" * 40)\n    # 1. 尝试删除存储目录\n    if os.path.exists(storage_dir):\n        try:\n            shutil.rmtree(storage_dir)\n            logger.info(f\"✅ 已删除存储目录: {storage_dir}\")", "detail": "simple_reset", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "simple_reset", "description": "simple_reset", "peekOfCode": "logger = logging.getLogger(__name__)\ndef simple_reset():\n    \"\"\"简单重置数据库\"\"\"\n    storage_dir = \"storage\"\n    print(\"🔄 开始简单数据库重置\")\n    print(\"=\" * 40)\n    # 1. 尝试删除存储目录\n    if os.path.exists(storage_dir):\n        try:\n            shutil.rmtree(storage_dir)", "detail": "simple_reset", "documentation": {}}, {"label": "test_chat_api", "kind": 2, "importPath": "test_api", "description": "test_api", "peekOfCode": "def test_chat_api():\n    \"\"\"测试chat API并检查响应格式\"\"\"\n    url = \"http://localhost:8000/api/chat\"\n    payload = {\n        \"id\": \"test-123\",\n        \"messages\": [\n            {\n                \"role\": \"user\",\n                \"content\": \"发票丢失了怎么办？\"\n            }", "detail": "test_api", "documentation": {}}, {"label": "test_health_api", "kind": 2, "importPath": "test_api", "description": "test_api", "peekOfCode": "def test_health_api():\n    \"\"\"测试健康检查API\"\"\"\n    url = \"http://localhost:8000/api/health\"\n    try:\n        response = requests.get(url, timeout=10)\n        print(f\"健康检查状态码: {response.status_code}\")\n        print(f\"健康检查响应: {response.json()}\")\n        return response.status_code == 200\n    except Exception as e:\n        print(f\"健康检查失败: {e}\")", "detail": "test_api", "documentation": {}}, {"label": "test_node_creation", "kind": 2, "importPath": "test_chunk_index_fix", "description": "test_chunk_index_fix", "peekOfCode": "def test_node_creation():\n    \"\"\"测试节点创建过程\"\"\"\n    print(\"🧪 测试节点创建过程\")\n    print(\"=\" * 60)\n    # 创建测试文件\n    test_content = \"\"\"测试文档\n这是第一段内容，用于测试文档分块功能。\n这是第二段内容，应该被分成不同的块。\n这是第三段内容，每个块都应该有正确的索引。\"\"\"\n    test_file = \"test_chunk_index.txt\"", "detail": "test_chunk_index_fix", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_chunk_index_fix", "description": "test_chunk_index_fix", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🧪 Chunk Index 修复测试\")\n    print(\"=\" * 80)\n    test_node_creation()\n    print(\"\\n💡 测试结论:\")\n    print(\"如果序列化后的 chunk_index 正确，但数据库中仍然是0，\")\n    print(\"那么问题可能在于:\")\n    print(\"1. 数据库中的数据是旧的\")\n    print(\"2. add_documents 方法中的逻辑有问题\")", "detail": "test_chunk_index_fix", "documentation": {}}, {"label": "create_test_files_with_duplicate_content", "kind": 2, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "def create_test_files_with_duplicate_content():\n    \"\"\"创建包含重复内容的测试文件\"\"\"\n    test_dir = \"test_duplicate_data\"\n    os.makedirs(test_dir, exist_ok=True)\n    # 创建两个文件，包含相同的文本块\n    duplicate_content = \"\"\"\n这是一段重复的文本内容。\n这段内容会出现在多个文件中。\n用于测试系统如何处理重复的文本块。\n\"\"\"", "detail": "test_duplicate_content", "documentation": {}}, {"label": "analyze_node_generation", "kind": 2, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "def analyze_node_generation():\n    \"\"\"分析节点生成过程\"\"\"\n    logger.info(\"🔍 分析节点生成过程...\")\n    test_dir = create_test_files_with_duplicate_content()\n    try:\n        # 读取文档\n        reader = SimpleDirectoryReader(test_dir)\n        documents = reader.load_data()\n        logger.info(f\"📄 读取了 {len(documents)} 个文档\")\n        # 解析为节点", "detail": "test_duplicate_content", "documentation": {}}, {"label": "test_storage_behavior", "kind": 2, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "def test_storage_behavior():\n    \"\"\"测试存储行为\"\"\"\n    logger.info(\"🔍 测试存储行为...\")\n    # 创建测试存储\n    test_storage_dir = \"test_storage\"\n    storage_context = get_storage_context(test_storage_dir)\n    try:\n        # 创建两个内容相同但ID不同的节点\n        node1 = TextNode(\n            text=\"这是重复的测试内容\",", "detail": "test_duplicate_content", "documentation": {}}, {"label": "analyze_vector_store_behavior", "kind": 2, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "def analyze_vector_store_behavior():\n    \"\"\"分析向量存储行为\"\"\"\n    logger.info(\"🔍 分析向量存储行为...\")\n    # 对于相同内容的文本块：\n    # 1. LlamaIndex会为每个节点生成唯一的node_id\n    # 2. 即使内容相同，也会被视为不同的节点\n    # 3. 会生成相同或非常相似的向量表示\n    # 4. 在检索时可能会返回多个相似的结果\n    logger.info(\"📊 向量存储行为分析:\")\n    logger.info(\"1. ✅ 每个节点都有唯一的node_id，即使内容相同\")", "detail": "test_duplicate_content", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "def main():\n    \"\"\"主测试流程\"\"\"\n    print(\"🧪 重复文本块处理机制测试\")\n    print(\"=\" * 50)\n    # 1. 分析节点生成\n    nodes, content_analysis = analyze_node_generation()\n    print()\n    # 2. 测试存储行为\n    test_storage_behavior()\n    print()", "detail": "test_duplicate_content", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_duplicate_content", "description": "test_duplicate_content", "peekOfCode": "logger = logging.getLogger(__name__)\ndef create_test_files_with_duplicate_content():\n    \"\"\"创建包含重复内容的测试文件\"\"\"\n    test_dir = \"test_duplicate_data\"\n    os.makedirs(test_dir, exist_ok=True)\n    # 创建两个文件，包含相同的文本块\n    duplicate_content = \"\"\"\n这是一段重复的文本内容。\n这段内容会出现在多个文件中。\n用于测试系统如何处理重复的文本块。", "detail": "test_duplicate_content", "documentation": {}}, {"label": "test_storage_creation", "kind": 2, "importPath": "test_storage", "description": "test_storage", "peekOfCode": "def test_storage_creation():\n    \"\"\"测试存储上下文创建\"\"\"\n    try:\n        logger.info(\"开始测试存储上下文创建...\")\n        # 导入必要的模块\n        from app.storage_config import get_storage_context\n        from app.index import STORAGE_DIR\n        logger.info(f\"存储目录: {STORAGE_DIR}\")\n        # 尝试创建存储上下文\n        storage_context = get_storage_context(STORAGE_DIR)", "detail": "test_storage", "documentation": {}}, {"label": "test_chromadb_directly", "kind": 2, "importPath": "test_storage", "description": "test_storage", "peekOfCode": "def test_chromadb_directly():\n    \"\"\"直接测试 ChromaDB\"\"\"\n    try:\n        logger.info(\"开始直接测试 ChromaDB...\")\n        import chromadb\n        from chromadb.config import Settings as ChromaSettings\n        chroma_db_path = os.path.join(\"storage\", \"chroma_db\")\n        logger.info(f\"ChromaDB 路径: {chroma_db_path}\")\n        # 创建 ChromaDB 客户端\n        chroma_client = chromadb.PersistentClient(", "detail": "test_storage", "documentation": {}}, {"label": "test_sqlite_stores", "kind": 2, "importPath": "test_storage", "description": "test_storage", "peekOfCode": "def test_sqlite_stores():\n    \"\"\"测试 SQLite 存储\"\"\"\n    try:\n        logger.info(\"开始测试 SQLite 存储...\")\n        from app.sqlite_stores import SQLiteDocumentStore, SQLiteIndexStore\n        # 测试文档存储\n        docstore_path = os.path.join(\"storage\", \"docstore.db\")\n        docstore = SQLiteDocumentStore(docstore_path)\n        logger.info(\"✅ SQLite 文档存储创建成功！\")\n        # 测试索引存储", "detail": "test_storage", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_storage", "description": "test_storage", "peekOfCode": "def main():\n    \"\"\"主测试流程\"\"\"\n    print(\"🔍 开始存储系统诊断\")\n    print(\"=\" * 50)\n    # 1. 测试 SQLite 存储\n    if test_sqlite_stores():\n        print(\"✅ SQLite 存储测试通过\")\n    else:\n        print(\"❌ SQLite 存储测试失败\")\n    print()", "detail": "test_storage", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_storage", "description": "test_storage", "peekOfCode": "logger = logging.getLogger(__name__)\ndef test_storage_creation():\n    \"\"\"测试存储上下文创建\"\"\"\n    try:\n        logger.info(\"开始测试存储上下文创建...\")\n        # 导入必要的模块\n        from app.storage_config import get_storage_context\n        from app.index import STORAGE_DIR\n        logger.info(f\"存储目录: {STORAGE_DIR}\")\n        # 尝试创建存储上下文", "detail": "test_storage", "documentation": {}}, {"label": "test_storage_creation", "kind": 2, "importPath": "test_storage_config", "description": "test_storage_config", "peekOfCode": "def test_storage_creation():\n    \"\"\"测试存储上下文创建\"\"\"\n    logger.info(\"🧪 测试存储上下文创建...\")\n    try:\n        storage_context = get_storage_context(\"storage\")\n        # 检查组件\n        logger.info(f\"✅ Vector Store: {type(storage_context.vector_store).__name__}\")\n        logger.info(f\"✅ Document Store: {type(storage_context.docstore).__name__}\")\n        logger.info(f\"✅ Index Store: {type(storage_context.index_store).__name__}\")\n        # 检查是否禁用了不需要的组件", "detail": "test_storage_config", "documentation": {}}, {"label": "test_storage_loading", "kind": 2, "importPath": "test_storage_config", "description": "test_storage_config", "peekOfCode": "def test_storage_loading():\n    \"\"\"测试存储上下文加载\"\"\"\n    logger.info(\"🧪 测试存储上下文加载...\")\n    try:\n        storage_context = load_storage_context(\"storage\")\n        if storage_context:\n            logger.info(\"✅ 存储上下文加载成功\")\n            return True\n        else:\n            logger.warning(\"⚠️  存储上下文加载返回None\")", "detail": "test_storage_config", "documentation": {}}, {"label": "check_storage_files", "kind": 2, "importPath": "test_storage_config", "description": "test_storage_config", "peekOfCode": "def check_storage_files():\n    \"\"\"检查存储文件状态\"\"\"\n    logger.info(\"🧪 检查存储文件...\")\n    storage_dir = \"storage\"\n    required_files = [\n        \"docstore.db\",\n        \"index_store.db\",\n        \"chroma_db\"\n    ]\n    unwanted_files = [", "detail": "test_storage_config", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_storage_config", "description": "test_storage_config", "peekOfCode": "def main():\n    \"\"\"主测试流程\"\"\"\n    print(\"🧪 存储配置测试\")\n    print(\"=\" * 40)\n    tests = [\n        (\"存储上下文创建\", test_storage_creation),\n        (\"存储上下文加载\", test_storage_loading),\n        (\"存储文件检查\", check_storage_files)\n    ]\n    passed = 0", "detail": "test_storage_config", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_storage_config", "description": "test_storage_config", "peekOfCode": "logger = logging.getLogger(__name__)\ndef test_storage_creation():\n    \"\"\"测试存储上下文创建\"\"\"\n    logger.info(\"🧪 测试存储上下文创建...\")\n    try:\n        storage_context = get_storage_context(\"storage\")\n        # 检查组件\n        logger.info(f\"✅ Vector Store: {type(storage_context.vector_store).__name__}\")\n        logger.info(f\"✅ Document Store: {type(storage_context.docstore).__name__}\")\n        logger.info(f\"✅ Index Store: {type(storage_context.index_store).__name__}\")", "detail": "test_storage_config", "documentation": {}}, {"label": "test_chunk_index_fix", "kind": 2, "importPath": "test_storage_fixes", "description": "test_storage_fixes", "peekOfCode": "def test_chunk_index_fix():\n    \"\"\"测试 chunk_index 修复\"\"\"\n    print(\"🔧 测试 chunk_index 修复\")\n    print(\"=\" * 60)\n    if not os.path.exists('storage/docstore.db'):\n        print(\"❌ docstore.db 不存在，请先上传一些文档\")\n        return\n    conn = sqlite3.connect('storage/docstore.db')\n    # 检查 chunk_index 分布\n    cursor = conn.execute(\"\"\"", "detail": "test_storage_fixes", "documentation": {}}, {"label": "test_same_file_handling", "kind": 2, "importPath": "test_storage_fixes", "description": "test_storage_fixes", "peekOfCode": "def test_same_file_handling():\n    \"\"\"测试同名文件处理\"\"\"\n    print(\"\\n🔄 测试同名文件处理\")\n    print(\"=\" * 60)\n    if not os.path.exists('storage/docstore.db'):\n        print(\"❌ docstore.db 不存在\")\n        return\n    conn = sqlite3.connect('storage/docstore.db')\n    # 检查文件重复情况\n    cursor = conn.execute(\"\"\"", "detail": "test_storage_fixes", "documentation": {}}, {"label": "test_chroma_consistency", "kind": 2, "importPath": "test_storage_fixes", "description": "test_storage_fixes", "peekOfCode": "def test_chroma_consistency():\n    \"\"\"测试 ChromaDB 一致性\"\"\"\n    print(\"\\n🧠 测试 ChromaDB 一致性\")\n    print(\"=\" * 60)\n    # 获取 docstore 中的 doc_id\n    docstore_ids = set()\n    if os.path.exists('storage/docstore.db'):\n        conn = sqlite3.connect('storage/docstore.db')\n        cursor = conn.execute(\"SELECT doc_id FROM documents\")\n        docstore_ids = {row[0] for row in cursor.fetchall()}", "detail": "test_storage_fixes", "documentation": {}}, {"label": "test_data_field_structure", "kind": 2, "importPath": "test_storage_fixes", "description": "test_storage_fixes", "peekOfCode": "def test_data_field_structure():\n    \"\"\"测试 data 字段结构\"\"\"\n    print(\"\\n📋 测试 data 字段结构\")\n    print(\"=\" * 60)\n    if not os.path.exists('storage/docstore.db'):\n        print(\"❌ docstore.db 不存在\")\n        return\n    conn = sqlite3.connect('storage/docstore.db')\n    cursor = conn.execute('SELECT doc_id, data FROM documents LIMIT 1')\n    row = cursor.fetchone()", "detail": "test_storage_fixes", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_storage_fixes", "description": "test_storage_fixes", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"🧪 存储逻辑修复测试\")\n    print(\"=\" * 80)\n    test_chunk_index_fix()\n    test_same_file_handling()\n    test_chroma_consistency()\n    test_data_field_structure()\n    print(\"\\n💡 测试完成\")\n    print(\"如果发现问题，请检查修复是否正确应用\")", "detail": "test_storage_fixes", "documentation": {}}, {"label": "test_upload", "kind": 2, "importPath": "test_upload_api", "description": "test_upload_api", "peekOfCode": "def test_upload():\n    # 测试文件路径\n    file_path = \"test_upload.txt\"\n    if not os.path.exists(file_path):\n        print(f\"测试文件 {file_path} 不存在\")\n        return\n    # API端点\n    url = \"http://127.0.0.1:8000/api/documents/upload\"\n    # 准备文件\n    with open(file_path, 'rb') as f:", "detail": "test_upload_api", "documentation": {}}, {"label": "check_documents", "kind": 2, "importPath": "test_upload_api", "description": "test_upload_api", "peekOfCode": "def check_documents():\n    \"\"\"检查当前文档列表\"\"\"\n    url = \"http://127.0.0.1:8000/api/documents\"\n    try:\n        response = requests.get(url)\n        if response.status_code == 200:\n            result = response.json()\n            print(\"\\n📋 当前文档列表:\")\n            if 'documents' in result:\n                for doc in result['documents']:", "detail": "test_upload_api", "documentation": {}}, {"label": "update_file_metadata", "kind": 2, "importPath": "update_file_metadata", "description": "update_file_metadata", "peekOfCode": "def update_file_metadata():\n    \"\"\"Update existing documents with file metadata.\"\"\"\n    db_path = \"storage/docstore.db\"\n    data_dir = \"data\"\n    if not os.path.exists(db_path):\n        logger.error(f\"Database file not found: {db_path}\")\n        return False\n    if not os.path.exists(data_dir):\n        logger.error(f\"Data directory not found: {data_dir}\")\n        return False", "detail": "update_file_metadata", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "update_file_metadata", "description": "update_file_metadata", "peekOfCode": "logger = logging.getLogger(__name__)\ndef update_file_metadata():\n    \"\"\"Update existing documents with file metadata.\"\"\"\n    db_path = \"storage/docstore.db\"\n    data_dir = \"data\"\n    if not os.path.exists(db_path):\n        logger.error(f\"Database file not found: {db_path}\")\n        return False\n    if not os.path.exists(data_dir):\n        logger.error(f\"Data directory not found: {data_dir}\")", "detail": "update_file_metadata", "documentation": {}}]