# @ai-sdk/solid

## 0.0.27

### Patch Changes

- Updated dependencies [5be25124]
  - @ai-sdk/ui-utils@0.0.24

## 0.0.26

### Patch Changes

- Updated dependencies [fea7b604]
  - @ai-sdk/ui-utils@0.0.23

## 0.0.25

### Patch Changes

- Updated dependencies [1d93d716]
  - @ai-sdk/ui-utils@0.0.22

## 0.0.24

### Patch Changes

- c450fcf7: feat (ui): invoke useChat onFinish with finishReason and tokens
- e4a1719f: chore (ai/ui): rename streamMode to streamProtocol
- Updated dependencies [c450fcf7]
- Updated dependencies [e4a1719f]
  - @ai-sdk/ui-utils@0.0.21

## 0.0.23

### Patch Changes

- b2bee4c5: fix (ai/ui): send data, body, headers in useChat().reload

## 0.0.22

### Patch Changes

- @ai-sdk/ui-utils@0.0.20

## 0.0.21

### Patch Changes

- @ai-sdk/ui-utils@0.0.19

## 0.0.20

### Patch Changes

- @ai-sdk/ui-utils@0.0.18

## 0.0.19

### Patch Changes

- f63829fe: feat (ai/ui): add allowEmptySubmit flag to handleSubmit
- 4b2c09d9: feat (ai/ui): add mutator function support to useChat / setMessages
- Updated dependencies [f63829fe]
  - @ai-sdk/ui-utils@0.0.17

## 0.0.18

### Patch Changes

- Updated dependencies [5b7b3bbe]
  - @ai-sdk/ui-utils@0.0.16

## 0.0.17

### Patch Changes

- Updated dependencies [1f67fe49]
  - @ai-sdk/ui-utils@0.0.15

## 0.0.16

### Patch Changes

- Updated dependencies [99ddbb74]
  - @ai-sdk/ui-utils@0.0.14

## 0.0.15

### Patch Changes

- a6cb2c8b: feat (ai/ui): add keepLastMessageOnError option to useChat
- Updated dependencies [a6cb2c8b]
  - @ai-sdk/ui-utils@0.0.13

## 0.0.14

### Patch Changes

- 56bbc2a7: feat (ai/ui): set body and headers directly on options for handleSubmit and append
- Updated dependencies [56bbc2a7]
  - @ai-sdk/ui-utils@0.0.12

## 0.0.13

### Patch Changes

- @ai-sdk/ui-utils@0.0.11

## 0.0.12

### Patch Changes

- 3db90c3d: allow empty handleSubmit submissions for useChat
  - @ai-sdk/ui-utils@0.0.10

## 0.0.11

### Patch Changes

- Updated dependencies [1894f811]
  - @ai-sdk/ui-utils@0.0.9

## 0.0.10

### Patch Changes

- d3100b9c: feat (ai/ui): support custom fetch function in useChat, useCompletion, useAssistant, useObject
- Updated dependencies [d3100b9c]
  - @ai-sdk/ui-utils@0.0.8

## 0.0.9

### Patch Changes

- @ai-sdk/ui-utils@0.0.7

## 0.0.8

### Patch Changes

- c908f741: chore (ui/solid): update solidjs useChat and useCompletion to feature parity with React

## 0.0.7

### Patch Changes

- Updated dependencies [54bf4083]
  - @ai-sdk/ui-utils@0.0.6

## 0.0.6

### Patch Changes

- d42b8907: feat (ui): make event in handleSubmit optional

## 0.0.5

### Patch Changes

- @ai-sdk/ui-utils@0.0.5

## 0.0.4

### Patch Changes

- Updated dependencies [008725ec]
  - @ai-sdk/ui-utils@0.0.4

## 0.0.3

### Patch Changes

- @ai-sdk/ui-utils@0.0.3

## 0.0.2

### Patch Changes

- @ai-sdk/ui-utils@0.0.2

## 0.0.1

### Patch Changes

- 85f209a4: chore: extracted ui library support into separate modules
- Updated dependencies [85f209a4]
  - @ai-sdk/ui-utils@0.0.1
