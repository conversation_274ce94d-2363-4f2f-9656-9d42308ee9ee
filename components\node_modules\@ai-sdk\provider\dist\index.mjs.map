{"version": 3, "sources": ["../src/errors/api-call-error.ts", "../src/errors/download-error.ts", "../src/errors/empty-response-body-error.ts", "../src/errors/invalid-argument-error.ts", "../src/errors/invalid-data-content-error.ts", "../src/errors/invalid-prompt-error.ts", "../src/errors/invalid-response-data-error.ts", "../src/errors/get-error-message.ts", "../src/errors/invalid-tool-arguments-error.ts", "../src/errors/json-parse-error.ts", "../src/errors/load-api-key-error.ts", "../src/errors/load-setting-error.ts", "../src/errors/no-content-generated-error.ts", "../src/errors/no-object-generated-error.ts", "../src/errors/no-such-tool-error.ts", "../src/errors/retry-error.ts", "../src/errors/too-many-embedding-values-for-call-error.ts", "../src/errors/tool-call-parse-error.ts", "../src/errors/type-validation-error.ts", "../src/errors/unsupported-functionality-error.ts", "../src/errors/unsupported-json-schema-error.ts"], "sourcesContent": ["export class APICallError extends Error {\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly cause?: unknown;\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_APICallError';\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.cause = cause;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isAPICallError(error: unknown): error is APICallError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_APICallError' &&\n      typeof (error as APICallError).url === 'string' &&\n      typeof (error as APICallError).requestBodyValues === 'object' &&\n      ((error as APICallError).statusCode == null ||\n        typeof (error as APICallError).statusCode === 'number') &&\n      ((error as APICallError).responseHeaders == null ||\n        typeof (error as APICallError).responseHeaders === 'object') &&\n      ((error as APICallError).responseBody == null ||\n        typeof (error as APICallError).responseBody === 'string') &&\n      ((error as APICallError).cause == null ||\n        typeof (error as APICallError).cause === 'object') &&\n      typeof (error as APICallError).isRetryable === 'boolean' &&\n      ((error as APICallError).data == null ||\n        typeof (error as APICallError).data === 'object')\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      requestBodyValues: this.requestBodyValues,\n      statusCode: this.statusCode,\n      responseHeaders: this.responseHeaders,\n      responseBody: this.responseBody,\n      cause: this.cause,\n      isRetryable: this.isRetryable,\n      data: this.data,\n    };\n  }\n}\n", "export class DownloadError extends Error {\n  readonly url: string;\n  readonly statusCode?: number;\n  readonly statusText?: string;\n  readonly cause?: unknown;\n\n  constructor({\n    url,\n    statusCode,\n    statusText,\n    cause,\n    message = cause == null\n      ? `Failed to download ${url}: ${statusCode} ${statusText}`\n      : `Failed to download ${url}: ${cause}`,\n  }: {\n    url: string;\n    statusCode?: number;\n    statusText?: string;\n    message?: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_DownloadError';\n\n    this.url = url;\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n    this.cause = cause;\n  }\n\n  static isDownloadError(error: unknown): error is DownloadError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_DownloadError' &&\n      typeof (error as DownloadError).url === 'string' &&\n      ((error as DownloadError).statusCode == null ||\n        typeof (error as DownloadError).statusCode === 'number') &&\n      ((error as DownloadError).statusText == null ||\n        typeof (error as DownloadError).statusText === 'string')\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      url: this.url,\n      statusCode: this.statusCode,\n      statusText: this.statusText,\n      cause: this.cause,\n    };\n  }\n}\n", "export class EmptyResponseBodyError extends Error {\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_EmptyResponseBodyError';\n  }\n\n  static isEmptyResponseBodyError(\n    error: unknown,\n  ): error is EmptyResponseBodyError {\n    return error instanceof Error && error.name === 'AI_EmptyResponseBodyError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "export class InvalidArgumentError extends Error {\n  readonly parameter: string;\n  readonly value: unknown;\n\n  constructor({\n    parameter,\n    value,\n    message,\n  }: {\n    parameter: string;\n    value: unknown;\n    message: string;\n  }) {\n    super(`Invalid argument for parameter ${parameter}: ${message}`);\n\n    this.name = 'AI_InvalidArgumentError';\n\n    this.parameter = parameter;\n    this.value = value;\n  }\n\n  static isInvalidArgumentError(error: unknown): error is InvalidArgumentError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidArgumentError' &&\n      typeof (error as InvalidArgumentError).parameter === 'string' &&\n      typeof (error as InvalidArgumentError).value === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      parameter: this.parameter,\n      value: this.value,\n    };\n  }\n}\n", "export class InvalidDataContentError extends Error {\n  readonly content: unknown;\n  readonly cause?: unknown;\n\n  constructor({\n    content,\n    cause,\n    message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.`,\n  }: {\n    content: unknown;\n    cause?: unknown;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidDataContentError';\n\n    this.cause = cause;\n    this.content = content;\n  }\n\n  static isInvalidDataContentError(\n    error: unknown,\n  ): error is InvalidDataContentError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidDataContentError' &&\n      (error as InvalidDataContentError).content != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n      cause: this.cause,\n      content: this.content,\n    };\n  }\n}\n", "export class InvalidPromptError extends Error {\n  readonly prompt: unknown;\n\n  constructor({ prompt, message }: { prompt: unknown; message: string }) {\n    super(`Invalid prompt: ${message}`);\n\n    this.name = 'AI_InvalidPromptError';\n\n    this.prompt = prompt;\n  }\n\n  static isInvalidPromptError(error: unknown): error is InvalidPromptError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidPromptError' &&\n      prompt != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      prompt: this.prompt,\n    };\n  }\n}\n", "/**\nServer returned a response with invalid data content. This should be thrown by providers when they\ncannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends Error {\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidResponseDataError';\n\n    this.data = data;\n  }\n\n  static isInvalidResponseDataError(\n    error: unknown,\n  ): error is InvalidResponseDataError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidResponseDataError' &&\n      (error as InvalidResponseDataError).data != null\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      data: this.data,\n    };\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class InvalidToolArgumentsError extends Error {\n  readonly toolName: string;\n  readonly toolArgs: string;\n  readonly cause: unknown;\n\n  constructor({\n    toolArgs,\n    toolName,\n    cause,\n    message = `Invalid arguments for tool ${toolName}: ${getErrorMessage(\n      cause,\n    )}`,\n  }: {\n    message?: string;\n    toolArgs: string;\n    toolName: string;\n    cause: unknown;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidToolArgumentsError';\n\n    this.toolArgs = toolArgs;\n    this.toolName = toolName;\n    this.cause = cause;\n  }\n\n  static isInvalidToolArgumentsError(\n    error: unknown,\n  ): error is InvalidToolArgumentsError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidToolArgumentsError' &&\n      typeof (error as InvalidToolArgumentsError).toolName === 'string' &&\n      typeof (error as InvalidToolArgumentsError).toolArgs === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      toolName: this.toolName,\n      toolArgs: this.toolArgs,\n    };\n  }\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class JSONParseError extends Error {\n  // note: property order determines debugging output\n  readonly text: string;\n  readonly cause: unknown;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super(\n      `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n    );\n\n    this.name = 'AI_JSONParseError';\n\n    this.cause = cause;\n    this.text = text;\n  }\n\n  static isJSONParseError(error: unknown): error is JSONParseError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_JSONParseError' &&\n      typeof (error as JSONParseError).text === 'string' &&\n      typeof (error as JSONParseError).cause === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      valueText: this.text,\n    };\n  }\n}\n", "export class LoadAPIKeyError extends Error {\n  constructor({ message }: { message: string }) {\n    super(message);\n\n    this.name = 'AI_LoadAPIKeyError';\n  }\n\n  static isLoadAPIKeyError(error: unknown): error is LoadAPIKeyError {\n    return error instanceof Error && error.name === 'AI_LoadAPIKeyError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n    };\n  }\n}\n", "export class LoadSettingError extends Error {\n  constructor({ message }: { message: string }) {\n    super(message);\n\n    this.name = 'AI_LoadSettingError';\n  }\n\n  static isLoadSettingError(error: unknown): error is LoadSettingError {\n    return error instanceof Error && error.name === 'AI_LoadSettingError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n    };\n  }\n}\n", "/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends Error {\n  readonly cause: unknown;\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_NoContentGeneratedError';\n  }\n\n  static isNoContentGeneratedError(\n    error: unknown,\n  ): error is NoContentGeneratedError {\n    return (\n      error instanceof Error && error.name === 'AI_NoContentGeneratedError'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "/**\nThrown when the AI provider fails to generate a parsable object.\n */\nexport class NoObjectGeneratedError extends Error {\n  readonly cause: unknown;\n\n  constructor({ message = 'No object generated.' }: { message?: string } = {}) {\n    super(message);\n\n    this.name = 'AI_NoObjectGeneratedError';\n  }\n\n  static isNoObjectGeneratedError(\n    error: unknown,\n  ): error is NoObjectGeneratedError {\n    return error instanceof Error && error.name === 'AI_NoObjectGeneratedError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      cause: this.cause,\n      message: this.message,\n      stack: this.stack,\n    };\n  }\n}\n", "export class NoSuchToolError extends Error {\n  readonly toolName: string;\n  readonly availableTools: string[] | undefined;\n\n  constructor({\n    toolName,\n    availableTools = undefined,\n    message = `Model tried to call unavailable tool '${toolName}'. ${\n      availableTools === undefined\n        ? 'No tools are available.'\n        : `Available tools: ${availableTools.join(', ')}.`\n    }`,\n  }: {\n    toolName: string;\n    availableTools?: string[] | undefined;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_NoSuchToolError';\n\n    this.toolName = toolName;\n    this.availableTools = availableTools;\n  }\n\n  static isNoSuchToolError(error: unknown): error is NoSuchToolError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_NoSuchToolError' &&\n      'toolName' in error &&\n      error.toolName != undefined &&\n      typeof error.name === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      toolName: this.toolName,\n      availableTools: this.availableTools,\n    };\n  }\n}\n", "export type RetryErrorReason =\n  | 'maxRetriesExceeded'\n  | 'errorNotRetryable'\n  | 'abort';\n\nexport class RetryError extends Error {\n  // note: property order determines debugging output\n  readonly reason: RetryErrorReason;\n  readonly lastError: unknown;\n  readonly errors: Array<unknown>;\n\n  constructor({\n    message,\n    reason,\n    errors,\n  }: {\n    message: string;\n    reason: RetryErrorReason;\n    errors: Array<unknown>;\n  }) {\n    super(message);\n\n    this.name = 'AI_RetryError';\n    this.reason = reason;\n    this.errors = errors;\n\n    // separate our last error to make debugging via log easier:\n    this.lastError = errors[errors.length - 1];\n  }\n\n  static isRetryError(error: unknown): error is RetryError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_RetryError' &&\n      typeof (error as RetryError).reason === 'string' &&\n      Array.isArray((error as RetryError).errors)\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      reason: this.reason,\n      lastError: this.lastError,\n      errors: this.errors,\n    };\n  }\n}\n", "export class TooManyEmbeddingValuesForCallError extends Error {\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super(\n      `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    );\n\n    this.name = 'AI_TooManyEmbeddingValuesForCallError';\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInvalidPromptError(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_TooManyEmbeddingValuesForCallError' &&\n      'provider' in error &&\n      typeof error.provider === 'string' &&\n      'modelId' in error &&\n      typeof error.modelId === 'string' &&\n      'maxEmbeddingsPerCall' in error &&\n      typeof error.maxEmbeddingsPerCall === 'number' &&\n      'values' in error &&\n      Array.isArray(error.values)\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      provider: this.provider,\n      modelId: this.modelId,\n      maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n      values: this.values,\n    };\n  }\n}\n", "import { LanguageModelV1FunctionTool } from '../language-model/v1/language-model-v1-function-tool';\nimport { getErrorMessage } from './get-error-message';\n\nexport class ToolCallParseError extends Error {\n  readonly cause: unknown;\n  readonly text: string;\n  readonly tools: LanguageModelV1FunctionTool[];\n\n  constructor({\n    cause,\n    text,\n    tools,\n    message = `Failed to parse tool calls: ${getErrorMessage(cause)}`,\n  }: {\n    cause: unknown;\n    text: string;\n    tools: LanguageModelV1FunctionTool[];\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_ToolCallParseError';\n\n    this.cause = cause;\n    this.text = text;\n    this.tools = tools;\n  }\n\n  static isToolCallParseError(error: unknown): error is ToolCallParseError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_ToolCallParseError' &&\n      'cause' in error &&\n      error.cause != undefined &&\n      'text' in error &&\n      error.text != undefined &&\n      typeof error.text === 'string' &&\n      'tools' in error &&\n      error.tools != undefined\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      cause: this.cause,\n      text: this.text,\n      tools: this.tools,\n    };\n  }\n}\n", "import { getErrorMessage } from './get-error-message';\n\nexport class TypeValidationError extends Error {\n  readonly value: unknown;\n  readonly cause: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super(\n      `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n    );\n\n    this.name = 'AI_TypeValidationError';\n\n    this.cause = cause;\n    this.value = value;\n  }\n\n  static isTypeValidationError(error: unknown): error is TypeValidationError {\n    return error instanceof Error && error.name === 'AI_TypeValidationError';\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      cause: this.cause,\n      stack: this.stack,\n\n      value: this.value,\n    };\n  }\n}\n", "export class UnsupportedFunctionalityError extends Error {\n  readonly functionality: string;\n\n  constructor({ functionality }: { functionality: string }) {\n    super(`'${functionality}' functionality not supported.`);\n\n    this.name = 'AI_UnsupportedFunctionalityError';\n\n    this.functionality = functionality;\n  }\n\n  static isUnsupportedFunctionalityError(\n    error: unknown,\n  ): error is UnsupportedFunctionalityError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_UnsupportedFunctionalityError' &&\n      typeof (error as UnsupportedFunctionalityError).functionality === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      functionality: this.functionality,\n    };\n  }\n}\n", "export class UnsupportedJSONSchemaError extends Error {\n  readonly reason: string;\n  readonly schema: unknown;\n\n  constructor({\n    schema,\n    reason,\n    message = `Unsupported JSON schema: ${reason}`,\n  }: {\n    schema: unknown;\n    reason: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_UnsupportedJSONSchemaError';\n\n    this.reason = reason;\n    this.schema = schema;\n  }\n\n  static isUnsupportedJSONSchemaError(\n    error: unknown,\n  ): error is UnsupportedJSONSchemaError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_UnsupportedJSONSchemaError' &&\n      'reason' in error &&\n      error.reason != undefined &&\n      'schema' in error &&\n      error.schema !== undefined\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      reason: this.reason,\n      schema: this.schema,\n    };\n  }\n}\n"], "mappings": ";AAAO,IAAM,eAAN,cAA2B,MAAM;AAAA,EAYtC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,cAAc,SACzB,eAAe;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,cAAc;AAAA;AAAA,IAClB;AAAA,EACF,GAUG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,eAAe,OAAuC;AAC3D,WACE,iBAAiB,SACjB,MAAM,SAAS,qBACf,OAAQ,MAAuB,QAAQ,YACvC,OAAQ,MAAuB,sBAAsB,aACnD,MAAuB,cAAc,QACrC,OAAQ,MAAuB,eAAe,cAC9C,MAAuB,mBAAmB,QAC1C,OAAQ,MAAuB,oBAAoB,cACnD,MAAuB,gBAAgB,QACvC,OAAQ,MAAuB,iBAAiB,cAChD,MAAuB,SAAS,QAChC,OAAQ,MAAuB,UAAU,aAC3C,OAAQ,MAAuB,gBAAgB,cAC7C,MAAuB,QAAQ,QAC/B,OAAQ,MAAuB,SAAS;AAAA,EAE9C;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,KAAK,KAAK;AAAA,MACV,mBAAmB,KAAK;AAAA,MACxB,YAAY,KAAK;AAAA,MACjB,iBAAiB,KAAK;AAAA,MACtB,cAAc,KAAK;AAAA,MACnB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;ACrFO,IAAM,gBAAN,cAA4B,MAAM;AAAA,EAMvC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,SAAS,OACf,sBAAsB,GAAG,KAAK,UAAU,IAAI,UAAU,KACtD,sBAAsB,GAAG,KAAK,KAAK;AAAA,EACzC,GAMG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,gBAAgB,OAAwC;AAC7D,WACE,iBAAiB,SACjB,MAAM,SAAS,sBACf,OAAQ,MAAwB,QAAQ,aACtC,MAAwB,cAAc,QACtC,OAAQ,MAAwB,eAAe,cAC/C,MAAwB,cAAc,QACtC,OAAQ,MAAwB,eAAe;AAAA,EAErD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,KAAK,KAAK;AAAA,MACV,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACrDO,IAAM,yBAAN,cAAqC,MAAM;AAAA,EAChD,YAAY,EAAE,UAAU,sBAAsB,IAA0B,CAAC,GAAG;AAC1E,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,yBACL,OACiC;AACjC,WAAO,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACpBO,IAAM,uBAAN,cAAmC,MAAM;AAAA,EAI9C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,kCAAkC,SAAS,KAAK,OAAO,EAAE;AAE/D,SAAK,OAAO;AAEZ,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,uBAAuB,OAA+C;AAC3E,WACE,iBAAiB,SACjB,MAAM,SAAS,6BACf,OAAQ,MAA+B,cAAc,YACrD,OAAQ,MAA+B,UAAU;AAAA,EAErD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACxCO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAIjD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU,+FAA+F,OAAO,OAAO;AAAA,EACzH,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,OAAO,0BACL,OACkC;AAClC,WACE,iBAAiB,SACjB,MAAM,SAAS,gCACd,MAAkC,WAAW;AAAA,EAElD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;;;ACxCO,IAAM,qBAAN,cAAiC,MAAM;AAAA,EAG5C,YAAY,EAAE,QAAAA,SAAQ,QAAQ,GAAyC;AACrE,UAAM,mBAAmB,OAAO,EAAE;AAElC,SAAK,OAAO;AAEZ,SAAK,SAASA;AAAA,EAChB;AAAA,EAEA,OAAO,qBAAqB,OAA6C;AACvE,WACE,iBAAiB,SACjB,MAAM,SAAS,2BACf,UAAU;AAAA,EAEd;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;;;ACxBO,IAAM,2BAAN,cAAuC,MAAM;AAAA,EAGlD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,KAAK,UAAU,IAAI,CAAC;AAAA,EAC1D,GAGG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,2BACL,OACmC;AACnC,WACE,iBAAiB,SACjB,MAAM,SAAS,iCACd,MAAmC,QAAQ;AAAA,EAEhD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;ACxCO,SAAS,gBAAgB,OAA4B;AAC1D,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,KAAK,UAAU,KAAK;AAC7B;;;ACZO,IAAM,4BAAN,cAAwC,MAAM;AAAA,EAKnD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,8BAA8B,QAAQ,KAAK;AAAA,MACnD;AAAA,IACF,CAAC;AAAA,EACH,GAKG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,4BACL,OACoC;AACpC,WACE,iBAAiB,SACjB,MAAM,SAAS,kCACf,OAAQ,MAAoC,aAAa,YACzD,OAAQ,MAAoC,aAAa;AAAA,EAE7D;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MAEZ,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IACjB;AAAA,EACF;AACF;;;ACjDO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EAKxC,YAAY,EAAE,MAAM,MAAM,GAAqC;AAC7D;AAAA,MACE,8BACW,IAAI;AAAA,iBACK,gBAAgB,KAAK,CAAC;AAAA,IAC5C;AAEA,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,iBAAiB,OAAyC;AAC/D,WACE,iBAAiB,SACjB,MAAM,SAAS,uBACf,OAAQ,MAAyB,SAAS,YAC1C,OAAQ,MAAyB,UAAU;AAAA,EAE/C;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MAEZ,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACF;;;ACvCO,IAAM,kBAAN,cAA8B,MAAM;AAAA,EACzC,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,kBAAkB,OAA0C;AACjE,WAAO,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;;;ACjBO,IAAM,mBAAN,cAA+B,MAAM;AAAA,EAC1C,YAAY,EAAE,QAAQ,GAAwB;AAC5C,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,mBAAmB,OAA2C;AACnE,WAAO,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;;;ACdO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAGjD,YAAY;AAAA,IACV,UAAU;AAAA,EACZ,IAA0B,CAAC,GAAG;AAC5B,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,0BACL,OACkC;AAClC,WACE,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAE7C;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;AC3BO,IAAM,yBAAN,cAAqC,MAAM;AAAA,EAGhD,YAAY,EAAE,UAAU,uBAAuB,IAA0B,CAAC,GAAG;AAC3E,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,yBACL,OACiC;AACjC,WAAO,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;AC1BO,IAAM,kBAAN,cAA8B,MAAM;AAAA,EAIzC,YAAY;AAAA,IACV;AAAA,IACA,iBAAiB;AAAA,IACjB,UAAU,yCAAyC,QAAQ,MACzD,mBAAmB,SACf,4BACA,oBAAoB,eAAe,KAAK,IAAI,CAAC,GACnD;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEA,OAAO,kBAAkB,OAA0C;AACjE,WACE,iBAAiB,SACjB,MAAM,SAAS,wBACf,cAAc,SACd,MAAM,YAAY,UAClB,OAAO,MAAM,SAAS;AAAA,EAE1B;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,UAAU,KAAK;AAAA,MACf,gBAAgB,KAAK;AAAA,IACvB;AAAA,EACF;AACF;;;ACxCO,IAAM,aAAN,cAAyB,MAAM;AAAA,EAMpC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AAGd,SAAK,YAAY,OAAO,OAAO,SAAS,CAAC;AAAA,EAC3C;AAAA,EAEA,OAAO,aAAa,OAAqC;AACvD,WACE,iBAAiB,SACjB,MAAM,SAAS,mBACf,OAAQ,MAAqB,WAAW,YACxC,MAAM,QAAS,MAAqB,MAAM;AAAA,EAE9C;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;;;AChDO,IAAM,qCAAN,cAAiD,MAAM;AAAA,EAM5D,YAAY,SAKT;AACD;AAAA,MACE,oDACS,QAAQ,QAAQ,WAAW,QAAQ,OAAO,0BAC9C,QAAQ,oBAAoB,yBAAyB,QAAQ,OAAO,MAAM;AAAA,IACjF;AAEA,SAAK,OAAO;AAEZ,SAAK,WAAW,QAAQ;AACxB,SAAK,UAAU,QAAQ;AACvB,SAAK,uBAAuB,QAAQ;AACpC,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EAEA,OAAO,qBACL,OAC6C;AAC7C,WACE,iBAAiB,SACjB,MAAM,SAAS,2CACf,cAAc,SACd,OAAO,MAAM,aAAa,YAC1B,aAAa,SACb,OAAO,MAAM,YAAY,YACzB,0BAA0B,SAC1B,OAAO,MAAM,yBAAyB,YACtC,YAAY,SACZ,MAAM,QAAQ,MAAM,MAAM;AAAA,EAE9B;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,MACd,sBAAsB,KAAK;AAAA,MAC3B,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;;;ACpDO,IAAM,qBAAN,cAAiC,MAAM;AAAA,EAK5C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,+BAA+B,gBAAgB,KAAK,CAAC;AAAA,EACjE,GAKG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,qBAAqB,OAA6C;AACvE,WACE,iBAAiB,SACjB,MAAM,SAAS,2BACf,WAAW,SACX,MAAM,SAAS,UACf,UAAU,SACV,MAAM,QAAQ,UACd,OAAO,MAAM,SAAS,YACtB,WAAW,SACX,MAAM,SAAS;AAAA,EAEnB;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACnDO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAI7C,YAAY,EAAE,OAAO,MAAM,GAAuC;AAChE;AAAA,MACE,kCACY,KAAK,UAAU,KAAK,CAAC;AAAA,iBACb,gBAAgB,KAAK,CAAC;AAAA,IAC5C;AAEA,SAAK,OAAO;AAEZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,sBAAsB,OAA8C;AACzE,WAAO,iBAAiB,SAAS,MAAM,SAAS;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MAEZ,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACjCO,IAAM,gCAAN,cAA4C,MAAM;AAAA,EAGvD,YAAY,EAAE,cAAc,GAA8B;AACxD,UAAM,IAAI,aAAa,gCAAgC;AAEvD,SAAK,OAAO;AAEZ,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEA,OAAO,gCACL,OACwC;AACxC,WACE,iBAAiB,SACjB,MAAM,SAAS,sCACf,OAAQ,MAAwC,kBAAkB;AAAA,EAEtE;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,eAAe,KAAK;AAAA,IACtB;AAAA,EACF;AACF;;;AC9BO,IAAM,6BAAN,cAAyC,MAAM;AAAA,EAIpD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU,4BAA4B,MAAM;AAAA,EAC9C,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,OAAO,6BACL,OACqC;AACrC,WACE,iBAAiB,SACjB,MAAM,SAAS,mCACf,YAAY,SACZ,MAAM,UAAU,UAChB,YAAY,SACZ,MAAM,WAAW;AAAA,EAErB;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;", "names": ["prompt"]}