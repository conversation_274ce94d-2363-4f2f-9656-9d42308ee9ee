{"name": "sswr", "version": "2.1.0", "description": "Svelte stale while revalidate (SWR) data fetching strategy", "repository": "github.com/ConsoleTVs/sswr", "author": "<PERSON><PERSON>", "license": "MIT", "type": "module", "types": "./dist/sswr.d.ts", "main": "./dist/sswr.umd.cjs", "module": "./dist/sswr.js", "exports": {".": {"types": "./dist/sswr.d.ts", "import": "./dist/sswr.js", "require": "./dist/sswr.umd.cjs"}, "./package.json": "./package.json"}, "files": ["dist", "src", "package.json"], "scripts": {"dev": "vite", "build": "vite build && tsc", "prepack": "npm run build"}, "dependencies": {"swrev": "^4.0.0"}, "devDependencies": {"svelte": "^4.2.8", "typescript": "^5.3.3", "vite": "^5.0.10"}, "peerDependencies": {"svelte": "^4.0.0 || ^5.0.0-next.0"}}