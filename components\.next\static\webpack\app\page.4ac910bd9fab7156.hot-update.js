"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatInput,ChatMessages,ChatSection!=!@llamaindex/chat-ui */ \"(app-pages-browser)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(app-pages-browser)/./app/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 修复422错误：根据README.md，LlamaIndex Server只需要messages字段\nconst CHAT_CONFIG = {\n    api: \"http://localhost:8000/api/chat\",\n    // 不使用body，因为LlamaIndex Server期望的是 { messages: [...] } 格式\n    initialMessages: [\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n        }\n    ]\n};\nfunction ChatPage() {\n    _s();\n    const handler = (0,ai_react__WEBPACK_IMPORTED_MODULE_2__.useChat)(CHAT_CONFIG);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatSection, {\n                handler: handler,\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatMessages, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatInput, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"1ZiBm6gpQFpcty49fMCYY+Iqv+w=\", false, function() {\n    return [\n        ai_react__WEBPACK_IMPORTED_MODULE_2__.useChat\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});