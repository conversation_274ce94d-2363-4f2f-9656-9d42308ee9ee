{"version": 3, "sources": ["../src/index.ts", "../src/parse-complex-response.ts", "../src/parse-partial-json.ts", "../src/fix-json.ts", "../src/stream-parts.ts", "../src/read-data-stream.ts", "../src/call-chat-api.ts", "../src/call-completion-api.ts", "../src/create-chunk-decoder.ts", "../src/is-deep-equal-data.ts", "../src/process-chat-stream.ts", "../src/data-url.ts"], "sourcesContent": ["export * from './types';\n\nexport { generateId } from '@ai-sdk/provider-utils';\n\n// Export stream data utilities for custom stream implementations,\n// both on the client and server side.\nexport { callChatApi } from './call-chat-api';\nexport { callCompletionApi } from './call-completion-api';\nexport { createChunkDecoder } from './create-chunk-decoder';\nexport type { DeepPartial } from './deep-partial';\nexport { isDeepEqualData } from './is-deep-equal-data';\nexport { parseComplexResponse } from './parse-complex-response';\nexport { parsePartialJson } from './parse-partial-json';\nexport { processChatStream } from './process-chat-stream';\nexport { readDataStream } from './read-data-stream';\nexport { formatStreamPart, parseStreamPart } from './stream-parts';\nexport type { StreamPart, StreamString } from './stream-parts';\nexport { getTextFromDataUrl } from './data-url';\n", "import { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport { parsePartialJson } from './parse-partial-json';\nimport { readDataStream } from './read-data-stream';\nimport type {\n  FunctionCall,\n  JSONValue,\n  Message,\n  ToolCall,\n  UseChatOptions,\n} from './types';\nimport { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\ntype PrefixMap = {\n  text?: Message;\n  // @deprecated\n  function_call?: Message & {\n    role: 'assistant';\n    function_call: FunctionCall;\n  };\n  // @deprecated\n  tool_calls?: Message & {\n    role: 'assistant';\n    tool_calls: ToolCall[];\n  };\n  data: JSONValue[];\n};\n\nfunction assignAnnotationsToMessage<T extends Message | null | undefined>(\n  message: T,\n  annotations: JSONValue[] | undefined,\n): T {\n  if (!message || !annotations || !annotations.length) return message;\n  return { ...message, annotations: [...annotations] } as T;\n}\n\nexport async function parseComplexResponse({\n  reader,\n  abortControllerRef,\n  update,\n  onToolCall,\n  onFinish,\n  generateId = generateIdFunction,\n  getCurrentDate = () => new Date(),\n}: {\n  reader: ReadableStreamDefaultReader<Uint8Array>;\n  abortControllerRef?: {\n    current: AbortController | null;\n  };\n  update: (merged: Message[], data: JSONValue[] | undefined) => void;\n  onToolCall?: UseChatOptions['onToolCall'];\n  onFinish?: (options: {\n    prefixMap: PrefixMap;\n    finishReason: LanguageModelV1FinishReason;\n    usage: {\n      completionTokens: number;\n      promptTokens: number;\n      totalTokens: number;\n    };\n  }) => void;\n  generateId?: () => string;\n  getCurrentDate?: () => Date;\n}) {\n  const createdAt = getCurrentDate();\n  const prefixMap: PrefixMap = {\n    data: [],\n  };\n\n  // keep list of current message annotations for message\n  let message_annotations: JSONValue[] | undefined = undefined;\n\n  // keep track of partial tool calls\n  const partialToolCalls: Record<\n    string,\n    { text: string; prefixMapIndex: number; toolName: string }\n  > = {};\n\n  let usage: {\n    completionTokens: number;\n    promptTokens: number;\n    totalTokens: number;\n  } = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN,\n  };\n  let finishReason: LanguageModelV1FinishReason = 'unknown';\n\n  // we create a map of each prefix, and for each prefixed message we push to the map\n  for await (const { type, value } of readDataStream(reader, {\n    isAborted: () => abortControllerRef?.current === null,\n  })) {\n    if (type === 'text') {\n      if (prefixMap['text']) {\n        prefixMap['text'] = {\n          ...prefixMap['text'],\n          content: (prefixMap['text'].content || '') + value,\n        };\n      } else {\n        prefixMap['text'] = {\n          id: generateId(),\n          role: 'assistant',\n          content: value,\n          createdAt,\n        };\n      }\n    }\n\n    if (type === 'finish_message') {\n      const { completionTokens, promptTokens } = value.usage;\n\n      finishReason = value.finishReason;\n      usage = {\n        completionTokens,\n        promptTokens,\n        totalTokens: completionTokens + promptTokens,\n      };\n    }\n\n    // Tool invocations are part of an assistant message\n    if (type === 'tool_call_streaming_start') {\n      // create message if it doesn't exist\n      if (prefixMap.text == null) {\n        prefixMap.text = {\n          id: generateId(),\n          role: 'assistant',\n          content: '',\n          createdAt,\n        };\n      }\n\n      if (prefixMap.text.toolInvocations == null) {\n        prefixMap.text.toolInvocations = [];\n      }\n\n      // add the partial tool call to the map\n      partialToolCalls[value.toolCallId] = {\n        text: '',\n        toolName: value.toolName,\n        prefixMapIndex: prefixMap.text.toolInvocations.length,\n      };\n\n      prefixMap.text.toolInvocations.push({\n        state: 'partial-call',\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: undefined,\n      });\n    } else if (type === 'tool_call_delta') {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n\n      partialToolCall.text += value.argsTextDelta;\n\n      prefixMap.text!.toolInvocations![partialToolCall.prefixMapIndex] = {\n        state: 'partial-call',\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: parsePartialJson(partialToolCall.text),\n      };\n\n      // trigger update for streaming by copying adding a update id that changes\n      // (without it, the changes get stuck in SWR and are not forwarded to rendering):\n      (prefixMap.text! as any).internalUpdateId = generateId();\n    } else if (type === 'tool_call') {\n      if (partialToolCalls[value.toolCallId] != null) {\n        // change the partial tool call to a full tool call\n        prefixMap.text!.toolInvocations![\n          partialToolCalls[value.toolCallId].prefixMapIndex\n        ] = { state: 'call', ...value };\n      } else {\n        // create message if it doesn't exist\n        if (prefixMap.text == null) {\n          prefixMap.text = {\n            id: generateId(),\n            role: 'assistant',\n            content: '',\n            createdAt,\n          };\n        }\n\n        if (prefixMap.text.toolInvocations == null) {\n          prefixMap.text.toolInvocations = [];\n        }\n\n        prefixMap.text.toolInvocations.push({\n          state: 'call',\n          ...value,\n        });\n      }\n\n      // trigger update for streaming by copying adding a update id that changes\n      // (without it, the changes get stuck in SWR and are not forwarded to rendering):\n      (prefixMap.text! as any).internalUpdateId = generateId();\n\n      // invoke the onToolCall callback if it exists. This is blocking.\n      // In the future we should make this non-blocking, which\n      // requires additional state management for error handling etc.\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          // store the result in the tool invocation\n          prefixMap.text!.toolInvocations![\n            prefixMap.text!.toolInvocations!.length - 1\n          ] = { state: 'result', ...value, result };\n        }\n      }\n    } else if (type === 'tool_result') {\n      const toolInvocations = prefixMap.text?.toolInvocations;\n\n      if (toolInvocations == null) {\n        throw new Error('tool_result must be preceded by a tool_call');\n      }\n\n      // find if there is any tool invocation with the same toolCallId\n      // and replace it with the result\n      const toolInvocationIndex = toolInvocations.findIndex(\n        invocation => invocation.toolCallId === value.toolCallId,\n      );\n\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          'tool_result must be preceded by a tool_call with the same toolCallId',\n        );\n      }\n\n      toolInvocations[toolInvocationIndex] = {\n        ...toolInvocations[toolInvocationIndex],\n        state: 'result' as const,\n        ...value,\n      };\n    }\n\n    let functionCallMessage: Message | null | undefined = null;\n\n    if (type === 'function_call') {\n      prefixMap['function_call'] = {\n        id: generateId(),\n        role: 'assistant',\n        content: '',\n        function_call: value.function_call,\n        name: value.function_call.name,\n        createdAt,\n      };\n\n      functionCallMessage = prefixMap['function_call'];\n    }\n\n    let toolCallMessage: Message | null | undefined = null;\n\n    if (type === 'tool_calls') {\n      prefixMap['tool_calls'] = {\n        id: generateId(),\n        role: 'assistant',\n        content: '',\n        tool_calls: value.tool_calls,\n        createdAt,\n      };\n\n      toolCallMessage = prefixMap['tool_calls'];\n    }\n\n    if (type === 'data') {\n      prefixMap['data'].push(...value);\n    }\n\n    let responseMessage = prefixMap['text'];\n\n    if (type === 'message_annotations') {\n      if (!message_annotations) {\n        message_annotations = [...value];\n      } else {\n        message_annotations.push(...value);\n      }\n\n      // Update any existing message with the latest annotations\n      functionCallMessage = assignAnnotationsToMessage(\n        prefixMap['function_call'],\n        message_annotations,\n      );\n      toolCallMessage = assignAnnotationsToMessage(\n        prefixMap['tool_calls'],\n        message_annotations,\n      );\n      responseMessage = assignAnnotationsToMessage(\n        prefixMap['text'],\n        message_annotations,\n      );\n    }\n\n    // keeps the prefixMap up to date with the latest annotations, even if annotations preceded the message\n    if (message_annotations?.length) {\n      const messagePrefixKeys: (keyof PrefixMap)[] = [\n        'text',\n        'function_call',\n        'tool_calls',\n      ];\n      messagePrefixKeys.forEach(key => {\n        if (prefixMap[key]) {\n          (prefixMap[key] as Message).annotations = [...message_annotations!];\n        }\n      });\n    }\n\n    // We add function & tool calls and response messages to the messages[], but data is its own thing\n    const merged = [functionCallMessage, toolCallMessage, responseMessage]\n      .filter(Boolean)\n      .map(message => ({\n        ...assignAnnotationsToMessage(message, message_annotations),\n      })) as Message[];\n\n    update(merged, [...prefixMap['data']]); // make a copy of the data array\n  }\n\n  onFinish?.({ prefixMap, finishReason, usage });\n\n  return {\n    messages: [\n      prefixMap.text,\n      prefixMap.function_call,\n      prefixMap.tool_calls,\n    ].filter(Boolean) as Message[],\n    data: prefixMap.data,\n  };\n}\n", "import SecureJSON from 'secure-json-parse';\nimport { fixJson } from './fix-json';\n\nexport function parsePartialJson(\n  jsonText: string | undefined,\n): unknown | undefined {\n  if (jsonText == null) {\n    return undefined;\n  }\n\n  try {\n    // first attempt a regular JSON parse:\n    return SecureJSON.parse(jsonText);\n  } catch (ignored) {\n    try {\n      // then try to fix the partial JSON and parse it:\n      const fixedJsonText = fixJson(jsonText);\n      return SecureJSON.parse(fixedJsonText);\n    } catch (ignored) {\n      // ignored\n    }\n  }\n\n  return undefined;\n}\n", "type State =\n  | 'ROOT'\n  | 'FINISH'\n  | 'INSIDE_STRING'\n  | 'INSIDE_STRING_ESCAPE'\n  | 'INSIDE_LITERAL'\n  | 'INSIDE_NUMBER'\n  | 'INSIDE_OBJECT_START'\n  | 'INSIDE_OBJECT_KEY'\n  | 'INSIDE_OBJECT_AFTER_KEY'\n  | 'INSIDE_OBJECT_BEFORE_VALUE'\n  | 'INSIDE_OBJECT_AFTER_VALUE'\n  | 'INSIDE_OBJECT_AFTER_COMMA'\n  | 'INSIDE_ARRAY_START'\n  | 'INSIDE_ARRAY_AFTER_VALUE'\n  | 'INSIDE_ARRAY_AFTER_COMMA';\n\n// Implemented as a scanner with additional fixing\n// that performs a single linear time scan pass over the partial JSON.\n//\n// The states should ideally match relevant states from the JSON spec:\n// https://www.json.org/json-en.html\n//\n// Please note that invalid JSON is not considered/covered, because it\n// is assumed that the resulting JSON will be processed by a standard\n// JSON parser that will detect any invalid JSON.\nexport function fixJson(input: string): string {\n  const stack: State[] = ['ROOT'];\n  let lastValidIndex = -1;\n  let literalStart: number | null = null;\n\n  function processValueStart(char: string, i: number, swapState: State) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_STRING');\n          break;\n        }\n\n        case 'f':\n        case 't':\n        case 'n': {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_LITERAL');\n          break;\n        }\n\n        case '-': {\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n        case '0':\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n\n        case '{': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_OBJECT_START');\n          break;\n        }\n\n        case '[': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_ARRAY_START');\n          break;\n        }\n      }\n    }\n  }\n\n  function processAfterObjectValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_OBJECT_AFTER_COMMA');\n        break;\n      }\n      case '}': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  function processAfterArrayValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_ARRAY_AFTER_COMMA');\n        break;\n      }\n      case ']': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n\n    switch (currentState) {\n      case 'ROOT':\n        processValueStart(char, i, 'FINISH');\n        break;\n\n      case 'INSIDE_OBJECT_START': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n          case '}': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_COMMA': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_AFTER_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_KEY': {\n        switch (char) {\n          case ':': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_BEFORE_VALUE');\n\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_BEFORE_VALUE': {\n        processValueStart(char, i, 'INSIDE_OBJECT_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        processAfterObjectValue(char, i);\n        break;\n      }\n\n      case 'INSIDE_STRING': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n\n          case '\\\\': {\n            stack.push('INSIDE_STRING_ESCAPE');\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START': {\n        switch (char) {\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        switch (char) {\n          case ',': {\n            stack.pop();\n            stack.push('INSIDE_ARRAY_AFTER_COMMA');\n            break;\n          }\n\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_COMMA': {\n        processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_STRING_ESCAPE': {\n        stack.pop();\n        lastValidIndex = i;\n\n        break;\n      }\n\n      case 'INSIDE_NUMBER': {\n        switch (char) {\n          case '0':\n          case '1':\n          case '2':\n          case '3':\n          case '4':\n          case '5':\n          case '6':\n          case '7':\n          case '8':\n          case '9': {\n            lastValidIndex = i;\n            break;\n          }\n\n          case 'e':\n          case 'E':\n          case '-':\n          case '.': {\n            break;\n          }\n\n          case ',': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case '}': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case ']': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            break;\n          }\n\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, i + 1);\n\n        if (\n          !'false'.startsWith(partialLiteral) &&\n          !'true'.startsWith(partialLiteral) &&\n          !'null'.startsWith(partialLiteral)\n        ) {\n          stack.pop();\n\n          if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n\n        break;\n      }\n    }\n  }\n\n  let result = input.slice(0, lastValidIndex + 1);\n\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n\n    switch (state) {\n      case 'INSIDE_STRING': {\n        result += '\"';\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY':\n      case 'INSIDE_OBJECT_AFTER_KEY':\n      case 'INSIDE_OBJECT_AFTER_COMMA':\n      case 'INSIDE_OBJECT_START':\n      case 'INSIDE_OBJECT_BEFORE_VALUE':\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        result += '}';\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START':\n      case 'INSIDE_ARRAY_AFTER_COMMA':\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        result += ']';\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, input.length);\n\n        if ('true'.startsWith(partialLiteral)) {\n          result += 'true'.slice(partialLiteral.length);\n        } else if ('false'.startsWith(partialLiteral)) {\n          result += 'false'.slice(partialLiteral.length);\n        } else if ('null'.startsWith(partialLiteral)) {\n          result += 'null'.slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n\n  return result;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { ToolCall as CoreToolCall } from './duplicated/tool-call';\nimport { ToolResult as CoreToolResult } from './duplicated/tool-result';\nimport {\n  AssistantMessage,\n  DataMessage,\n  FunctionCall,\n  JSONValue,\n  ToolCall,\n} from './types';\n\nexport type StreamString =\n  `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\\n`;\n\nexport interface StreamPart<CODE extends string, NAME extends string, TYPE> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: StreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst functionCallStreamPart: StreamPart<\n  '1',\n  'function_call',\n  { function_call: FunctionCall }\n> = {\n  code: '1',\n  name: 'function_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('function_call' in value) ||\n      typeof value.function_call !== 'object' ||\n      value.function_call == null ||\n      !('name' in value.function_call) ||\n      !('arguments' in value.function_call) ||\n      typeof value.function_call.name !== 'string' ||\n      typeof value.function_call.arguments !== 'string'\n    ) {\n      throw new Error(\n        '\"function_call\" parts expect an object with a \"function_call\" property.',\n      );\n    }\n\n    return {\n      type: 'function_call',\n      value: value as unknown as { function_call: FunctionCall },\n    };\n  },\n};\n\nconst dataStreamPart: StreamPart<'2', 'data', Array<JSONValue>> = {\n  code: '2',\n  name: 'data',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n\n    return { type: 'data', value };\n  },\n};\n\nconst errorStreamPart: StreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst assistantMessageStreamPart: StreamPart<\n  '4',\n  'assistant_message',\n  AssistantMessage\n> = {\n  code: '4',\n  name: 'assistant_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('id' in value) ||\n      !('role' in value) ||\n      !('content' in value) ||\n      typeof value.id !== 'string' ||\n      typeof value.role !== 'string' ||\n      value.role !== 'assistant' ||\n      !Array.isArray(value.content) ||\n      !value.content.every(\n        item =>\n          item != null &&\n          typeof item === 'object' &&\n          'type' in item &&\n          item.type === 'text' &&\n          'text' in item &&\n          item.text != null &&\n          typeof item.text === 'object' &&\n          'value' in item.text &&\n          typeof item.text.value === 'string',\n      )\n    ) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_message',\n      value: value as AssistantMessage,\n    };\n  },\n};\n\nconst assistantControlDataStreamPart: StreamPart<\n  '5',\n  'assistant_control_data',\n  {\n    threadId: string;\n    messageId: string;\n  }\n> = {\n  code: '5',\n  name: 'assistant_control_data',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('threadId' in value) ||\n      !('messageId' in value) ||\n      typeof value.threadId !== 'string' ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_control_data',\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst dataMessageStreamPart: StreamPart<'6', 'data_message', DataMessage> = {\n  code: '6',\n  name: 'data_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('role' in value) ||\n      !('data' in value) ||\n      typeof value.role !== 'string' ||\n      value.role !== 'data'\n    ) {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.',\n      );\n    }\n\n    return {\n      type: 'data_message',\n      value: value as DataMessage,\n    };\n  },\n};\n\nconst toolCallsStreamPart: StreamPart<\n  '7',\n  'tool_calls',\n  { tool_calls: ToolCall[] }\n> = {\n  code: '7',\n  name: 'tool_calls',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('tool_calls' in value) ||\n      typeof value.tool_calls !== 'object' ||\n      value.tool_calls == null ||\n      !Array.isArray(value.tool_calls) ||\n      value.tool_calls.some(\n        tc =>\n          tc == null ||\n          typeof tc !== 'object' ||\n          !('id' in tc) ||\n          typeof tc.id !== 'string' ||\n          !('type' in tc) ||\n          typeof tc.type !== 'string' ||\n          !('function' in tc) ||\n          tc.function == null ||\n          typeof tc.function !== 'object' ||\n          !('arguments' in tc.function) ||\n          typeof tc.function.name !== 'string' ||\n          typeof tc.function.arguments !== 'string',\n      )\n    ) {\n      throw new Error(\n        '\"tool_calls\" parts expect an object with a ToolCallPayload.',\n      );\n    }\n\n    return {\n      type: 'tool_calls',\n      value: value as unknown as { tool_calls: ToolCall[] },\n    };\n  },\n};\n\nconst messageAnnotationsStreamPart: StreamPart<\n  '8',\n  'message_annotations',\n  Array<JSONValue>\n> = {\n  code: '8',\n  name: 'message_annotations',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n\n    return { type: 'message_annotations', value };\n  },\n};\n\nconst toolCallStreamPart: StreamPart<\n  '9',\n  'tool_call',\n  CoreToolCall<string, any>\n> = {\n  code: '9',\n  name: 'tool_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string' ||\n      !('args' in value) ||\n      typeof value.args !== 'object'\n    ) {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call',\n      value: value as unknown as CoreToolCall<string, any>,\n    };\n  },\n};\n\nconst toolResultStreamPart: StreamPart<\n  'a',\n  'tool_result',\n  Omit<CoreToolResult<string, any, any>, 'args' | 'toolName'>\n> = {\n  code: 'a',\n  name: 'tool_result',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('result' in value)\n    ) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_result',\n      value: value as unknown as Omit<\n        CoreToolResult<string, any, any>,\n        'args' | 'toolName'\n      >,\n    };\n  },\n};\n\nconst toolCallStreamingStartStreamPart: StreamPart<\n  'b',\n  'tool_call_streaming_start',\n  { toolCallId: string; toolName: string }\n> = {\n  code: 'b',\n  name: 'tool_call_streaming_start',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_streaming_start',\n      value: value as unknown as { toolCallId: string; toolName: string },\n    };\n  },\n};\n\nconst toolCallDeltaStreamPart: StreamPart<\n  'c',\n  'tool_call_delta',\n  { toolCallId: string; argsTextDelta: string }\n> = {\n  code: 'c',\n  name: 'tool_call_delta',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('argsTextDelta' in value) ||\n      typeof value.argsTextDelta !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_delta',\n      value: value as unknown as {\n        toolCallId: string;\n        argsTextDelta: string;\n      },\n    };\n  },\n};\n\nconst finishMessageStreamPart: StreamPart<\n  'd',\n  'finish_message',\n  {\n    finishReason: LanguageModelV1FinishReason;\n    usage: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'd',\n  name: 'finish_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string' ||\n      !('usage' in value) ||\n      value.usage == null ||\n      typeof value.usage !== 'object' ||\n      !('promptTokens' in value.usage) ||\n      !('completionTokens' in value.usage)\n    ) {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" and \"usage\" property.',\n      );\n    }\n    // NaN is JSON parsed as null.\n    if (typeof value.usage.promptTokens !== 'number') {\n      value.usage.promptTokens = Number.NaN;\n    }\n    if (typeof value.usage.completionTokens !== 'number') {\n      value.usage.completionTokens = Number.NaN;\n    }\n    return {\n      type: 'finish_message',\n      value: value as unknown as {\n        finishReason: LanguageModelV1FinishReason;\n        usage: {\n          promptTokens: number;\n          completionTokens: number;\n          totalTokens: number;\n        };\n      },\n    };\n  },\n};\n\nconst streamParts = [\n  textStreamPart,\n  functionCallStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n  toolCallsStreamPart,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n] as const;\n\n// union type of all stream parts\ntype StreamParts =\n  | typeof textStreamPart\n  | typeof functionCallStreamPart\n  | typeof dataStreamPart\n  | typeof errorStreamPart\n  | typeof assistantMessageStreamPart\n  | typeof assistantControlDataStreamPart\n  | typeof dataMessageStreamPart\n  | typeof toolCallsStreamPart\n  | typeof messageAnnotationsStreamPart\n  | typeof toolCallStreamPart\n  | typeof toolResultStreamPart\n  | typeof toolCallStreamingStartStreamPart\n  | typeof toolCallDeltaStreamPart\n  | typeof finishMessageStreamPart;\n\n/**\n * Maps the type of a stream part to its value type.\n */\ntype StreamPartValueType = {\n  [P in StreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type StreamPartType =\n  | ReturnType<typeof textStreamPart.parse>\n  | ReturnType<typeof functionCallStreamPart.parse>\n  | ReturnType<typeof dataStreamPart.parse>\n  | ReturnType<typeof errorStreamPart.parse>\n  | ReturnType<typeof assistantMessageStreamPart.parse>\n  | ReturnType<typeof assistantControlDataStreamPart.parse>\n  | ReturnType<typeof dataMessageStreamPart.parse>\n  | ReturnType<typeof toolCallsStreamPart.parse>\n  | ReturnType<typeof messageAnnotationsStreamPart.parse>\n  | ReturnType<typeof toolCallStreamPart.parse>\n  | ReturnType<typeof toolResultStreamPart.parse>\n  | ReturnType<typeof toolCallStreamingStartStreamPart.parse>\n  | ReturnType<typeof toolCallDeltaStreamPart.parse>\n  | ReturnType<typeof finishMessageStreamPart.parse>;\n\nexport const streamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [functionCallStreamPart.code]: functionCallStreamPart,\n  [dataStreamPart.code]: dataStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n  [toolCallsStreamPart.code]: toolCallsStreamPart,\n  [messageAnnotationsStreamPart.code]: messageAnnotationsStreamPart,\n  [toolCallStreamPart.code]: toolCallStreamPart,\n  [toolResultStreamPart.code]: toolResultStreamPart,\n  [toolCallStreamingStartStreamPart.code]: toolCallStreamingStartStreamPart,\n  [toolCallDeltaStreamPart.code]: toolCallDeltaStreamPart,\n  [finishMessageStreamPart.code]: finishMessageStreamPart,\n} as const;\n\n/**\n * The map of prefixes for data in the stream\n *\n * - 0: Text from the LLM response\n * - 1: (OpenAI) function_call responses\n * - 2: custom JSON added by the user using `Data`\n * - 6: (OpenAI) tool_call responses\n *\n * Example:\n * ```\n * 0:Vercel\n * 0:'s\n * 0: AI\n * 0: AI\n * 0: SDK\n * 0: is great\n * 0:!\n * 2: { \"someJson\": \"value\" }\n * 1: {\"function_call\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}\n * 6: {\"tool_call\": {\"id\": \"tool_0\", \"type\": \"function\", \"function\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}}\n *```\n */\nexport const StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [functionCallStreamPart.name]: functionCallStreamPart.code,\n  [dataStreamPart.name]: dataStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n  [toolCallsStreamPart.name]: toolCallsStreamPart.code,\n  [messageAnnotationsStreamPart.name]: messageAnnotationsStreamPart.code,\n  [toolCallStreamPart.name]: toolCallStreamPart.code,\n  [toolResultStreamPart.name]: toolResultStreamPart.code,\n  [toolCallStreamingStartStreamPart.name]:\n    toolCallStreamingStartStreamPart.code,\n  [toolCallDeltaStreamPart.name]: toolCallDeltaStreamPart.code,\n  [finishMessageStreamPart.name]: finishMessageStreamPart.code,\n} as const;\n\nexport const validCodes = streamParts.map(part => part.code);\n\n/**\nParses a stream part from a string.\n\n@param line The string to parse.\n@returns The parsed stream part.\n@throws An error if the string cannot be parsed.\n */\nexport const parseStreamPart = (line: string): StreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof streamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof streamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return streamPartsByCode[code].parse(jsonValue);\n};\n\n/**\nPrepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,\nand appends a new line.\n\nIt ensures type-safety for the part type and value.\n */\nexport function formatStreamPart<T extends keyof StreamPartValueType>(\n  type: T,\n  value: StreamPartValueType[T],\n): StreamString {\n  const streamPart = streamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { StreamPartType, parseStreamPart } from './stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\n/**\nConverts a ReadableStreamDefaultReader into an async generator that yields\nStreamPart objects.\n\n@param reader \n       Reader for the stream to read from.\n@param isAborted\n       Optional function that returns true if the request has been aborted.\n       If the function returns true, the generator will stop reading the stream.\n       If the function is not provided, the generator will not stop reading the stream.\n */\nexport async function* readDataStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  {\n    isAborted,\n  }: {\n    isAborted?: () => boolean;\n  } = {},\n): AsyncGenerator<StreamPartType> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '') // splitting leaves an empty string at the end\n      .map(parseStreamPart);\n\n    for (const streamPart of streamParts) {\n      yield streamPart;\n    }\n\n    // The request has been aborted, stop reading the stream.\n    if (isAborted?.()) {\n      reader.cancel();\n      break;\n    }\n  }\n}\n", "import { createChunkDecoder } from './index';\nimport { parseComplexResponse } from './parse-complex-response';\nimport { IdGenerator, JSONValue, Message, UseChatOptions } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callChatApi({\n  api,\n  body,\n  streamProtocol = 'data',\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId,\n  fetch = getOriginalFetch(),\n}: {\n  api: string;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  abortController: (() => AbortController | null) | undefined;\n  restoreMessagesOnFailure: () => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onUpdate: (merged: Message[], data: JSONValue[] | undefined) => void;\n  onFinish: UseChatOptions['onFinish'];\n  onToolCall: UseChatOptions['onToolCall'];\n  generateId: IdGenerator;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n}) {\n  const response = await fetch(api, {\n    method: 'POST',\n    body: JSON.stringify(body),\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    signal: abortController?.()?.signal,\n    credentials,\n  }).catch(err => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (await response.text()) ?? 'Failed to fetch the chat response.',\n    );\n  }\n\n  if (!response.body) {\n    throw new Error('The response body is empty.');\n  }\n\n  const reader = response.body.getReader();\n\n  switch (streamProtocol) {\n    case 'text': {\n      const decoder = createChunkDecoder();\n\n      const resultMessage = {\n        id: generateId(),\n        createdAt: new Date(),\n        role: 'assistant' as const,\n        content: '',\n      };\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n          break;\n        }\n\n        resultMessage.content += decoder(value);\n\n        // note: creating a new message object is required for Solid.js streaming\n        onUpdate([{ ...resultMessage }], []);\n\n        // The request has been aborted, stop reading the stream.\n        if (abortController?.() === null) {\n          reader.cancel();\n          break;\n        }\n      }\n\n      // in text mode, we don't have usage information or finish reason:\n      onFinish?.(resultMessage, {\n        usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n        finishReason: 'unknown',\n      });\n\n      return {\n        messages: [resultMessage],\n        data: [],\n      };\n    }\n\n    case 'data': {\n      return await parseComplexResponse({\n        reader,\n        abortControllerRef:\n          abortController != null ? { current: abortController() } : undefined,\n        update: onUpdate,\n        onToolCall,\n        onFinish({ prefixMap, finishReason, usage }) {\n          if (onFinish && prefixMap.text != null) {\n            onFinish(prefixMap.text, { usage, finishReason });\n          }\n        },\n        generateId,\n      });\n    }\n\n    default: {\n      const exhaustiveCheck: never = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n", "import { readDataStream } from './read-data-stream';\nimport { JSONValue } from './types';\nimport { createChunkDecoder } from './index';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = 'data',\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch = getOriginalFetch(),\n}: {\n  api: string;\n  prompt: string;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  setCompletion: (completion: string) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: Error | undefined) => void;\n  setAbortController: (abortController: AbortController | null) => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onFinish: ((prompt: string, completion: string) => void) | undefined;\n  onError: ((error: Error) => void) | undefined;\n  onData: ((data: JSONValue[]) => void) | undefined;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n}) {\n  try {\n    setLoading(true);\n    setError(undefined);\n\n    const abortController = new AbortController();\n    setAbortController(abortController);\n\n    // Empty the completion immediately.\n    setCompletion('');\n\n    const res = await fetch(api, {\n      method: 'POST',\n      body: JSON.stringify({\n        prompt,\n        ...body,\n      }),\n      credentials,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      signal: abortController.signal,\n    }).catch(err => {\n      throw err;\n    });\n\n    if (onResponse) {\n      try {\n        await onResponse(res);\n      } catch (err) {\n        throw err;\n      }\n    }\n\n    if (!res.ok) {\n      throw new Error(\n        (await res.text()) || 'Failed to fetch the chat response.',\n      );\n    }\n\n    if (!res.body) {\n      throw new Error('The response body is empty.');\n    }\n\n    let result = '';\n    const reader = res.body.getReader();\n\n    switch (streamProtocol) {\n      case 'text': {\n        const decoder = createChunkDecoder();\n\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) {\n            break;\n          }\n\n          // Update the completion state with the new message tokens.\n          result += decoder(value);\n          setCompletion(result);\n\n          // The request has been aborted, stop reading the stream.\n          if (abortController === null) {\n            reader.cancel();\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'data': {\n        for await (const { type, value } of readDataStream(reader, {\n          isAborted: () => abortController === null,\n        })) {\n          switch (type) {\n            case 'text': {\n              result += value;\n              setCompletion(result);\n              break;\n            }\n            case 'data': {\n              onData?.(value);\n              break;\n            }\n          }\n        }\n        break;\n      }\n\n      default: {\n        const exhaustiveCheck: never = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    // Ignore abort errors as they are expected.\n    if ((err as any).name === 'AbortError') {\n      setAbortController(null);\n      return null;\n    }\n\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n\n    setError(err as Error);\n  } finally {\n    setLoading(false);\n  }\n}\n", "import { StreamPartType, parseStreamPart } from './stream-parts';\n\n// simple decoder signatures:\nfunction createChunkDecoder(): (chunk: Uint8Array | undefined) => string;\nfunction createChunkDecoder(\n  complex: false,\n): (chunk: Uint8Array | undefined) => string;\n// complex decoder signature:\nfunction createChunkDecoder(\n  complex: true,\n): (chunk: Uint8Array | undefined) => StreamPartType[];\n// combined signature for when the client calls this function with a boolean:\nfunction createChunkDecoder(\n  complex?: boolean,\n): (chunk: Uint8Array | undefined) => StreamPartType[] | string;\nfunction createChunkDecoder(complex?: boolean) {\n  const decoder = new TextDecoder();\n\n  if (!complex) {\n    return function (chunk: Uint8Array | undefined): string {\n      if (!chunk) return '';\n      return decoder.decode(chunk, { stream: true });\n    };\n  }\n\n  return function (chunk: Uint8Array | undefined) {\n    const decoded = decoder\n      .decode(chunk, { stream: true })\n      .split('\\n')\n      .filter(line => line !== ''); // splitting leaves an empty string at the end\n\n    return decoded.map(parseStreamPart).filter(Boolean);\n  };\n}\n\nexport { createChunkDecoder };\n", "/**\n * Performs a deep-equal comparison of two parsed JSON objects.\n *\n * @param {any} obj1 - The first object to compare.\n * @param {any} obj2 - The second object to compare.\n * @returns {boolean} - Returns true if the two objects are deeply equal, false otherwise.\n */\nexport function isDeepEqualData(obj1: any, obj2: any): boolean {\n  // Check for strict equality first\n  if (obj1 === obj2) return true;\n\n  // Check if either is null or undefined\n  if (obj1 == null || obj2 == null) return false;\n\n  // Check if both are objects\n  if (typeof obj1 !== 'object' && typeof obj2 !== 'object')\n    return obj1 === obj2;\n\n  // If they are not strictly equal, they both need to be Objects\n  if (obj1.constructor !== obj2.constructor) return false;\n\n  // Special handling for Date objects\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n\n  // Handle arrays: compare length and then perform a recursive deep comparison on each item\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length) return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i])) return false;\n    }\n    return true; // All array elements matched\n  }\n\n  // Compare the set of keys in each object\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length) return false;\n\n  // Check each key-value pair recursively\n  for (const key of keys1) {\n    if (!keys2.includes(key)) return false;\n    if (!isDeepEqualData(obj1[key], obj2[key])) return false;\n  }\n\n  return true; // All keys and values matched\n}\n", "import {\n  ChatRe<PERSON>,\n  Function<PERSON>all,\n  JSONV<PERSON>ue,\n  Message,\n  ToolCall,\n} from './types';\n\nexport async function processChatStream({\n  getStreamedResponse,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  updateChatRequest,\n  getCurrentMessages,\n}: {\n  getStreamedResponse: () => Promise<\n    Message | { messages: Message[]; data: JSONValue[] }\n  >;\n  experimental_onFunctionCall?: (\n    chatMessages: Message[],\n    functionCall: FunctionCall,\n  ) => Promise<void | ChatRequest>;\n  experimental_onToolCall?: (\n    chatMessages: Message[],\n    toolCalls: ToolCall[],\n  ) => Promise<void | ChatRequest>;\n  updateChatRequest: (chatRequest: ChatRequest) => void;\n  getCurrentMessages: () => Message[];\n}) {\n  while (true) {\n    // TODO-STREAMDATA: This should be {  const { messages: streamedResponseMessages, data } =\n    // await getStreamedResponse(} once Stream Data is not experimental\n    const messagesAndDataOrJustMessage = await getStreamedResponse();\n\n    // Using experimental stream data\n    if ('messages' in messagesAndDataOrJustMessage) {\n      let hasFollowingResponse = false;\n\n      for (const message of messagesAndDataOrJustMessage.messages) {\n        // See if the message has a complete function call or tool call\n        if (\n          (message.function_call === undefined ||\n            typeof message.function_call === 'string') &&\n          (message.tool_calls === undefined ||\n            typeof message.tool_calls === 'string')\n        ) {\n          continue;\n        }\n\n        hasFollowingResponse = true;\n        // Try to handle function call\n        if (experimental_onFunctionCall) {\n          const functionCall = message.function_call;\n          // Make sure functionCall is an object\n          // If not, we got tool calls instead of function calls\n          if (typeof functionCall !== 'object') {\n            console.warn(\n              'experimental_onFunctionCall should not be defined when using tools',\n            );\n            continue;\n          }\n\n          // User handles the function call in their own functionCallHandler.\n          // The \"arguments\" key of the function call object will still be a string which will have to be parsed in the function handler.\n          // If the \"arguments\" JSON is malformed due to model error the user will have to handle that themselves.\n\n          const functionCallResponse: ChatRequest | void =\n            await experimental_onFunctionCall(\n              getCurrentMessages(),\n              functionCall,\n            );\n\n          // If the user does not return anything as a result of the function call, the loop will break.\n          if (functionCallResponse === undefined) {\n            hasFollowingResponse = false;\n            break;\n          }\n\n          // A function call response was returned.\n          // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n          updateChatRequest(functionCallResponse);\n        }\n        // Try to handle tool call\n        if (experimental_onToolCall) {\n          const toolCalls = message.tool_calls;\n          // Make sure toolCalls is an array of objects\n          // If not, we got function calls instead of tool calls\n          if (\n            !Array.isArray(toolCalls) ||\n            toolCalls.some(toolCall => typeof toolCall !== 'object')\n          ) {\n            console.warn(\n              'experimental_onToolCall should not be defined when using tools',\n            );\n            continue;\n          }\n\n          // User handles the function call in their own functionCallHandler.\n          // The \"arguments\" key of the function call object will still be a string which will have to be parsed in the function handler.\n          // If the \"arguments\" JSON is malformed due to model error the user will have to handle that themselves.\n          const toolCallResponse: ChatRequest | void =\n            await experimental_onToolCall(getCurrentMessages(), toolCalls);\n\n          // If the user does not return anything as a result of the function call, the loop will break.\n          if (toolCallResponse === undefined) {\n            hasFollowingResponse = false;\n            break;\n          }\n\n          // A function call response was returned.\n          // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n          updateChatRequest(toolCallResponse);\n        }\n      }\n      if (!hasFollowingResponse) {\n        break;\n      }\n    } else {\n      const streamedResponseMessage = messagesAndDataOrJustMessage;\n\n      // TODO-STREAMDATA: Remove this once Stream Data is not experimental\n      if (\n        (streamedResponseMessage.function_call === undefined ||\n          typeof streamedResponseMessage.function_call === 'string') &&\n        (streamedResponseMessage.tool_calls === undefined ||\n          typeof streamedResponseMessage.tool_calls === 'string')\n      ) {\n        break;\n      }\n\n      // If we get here and are expecting a function call, the message should have one, if not warn and continue\n      if (experimental_onFunctionCall) {\n        const functionCall = streamedResponseMessage.function_call;\n        if (!(typeof functionCall === 'object')) {\n          console.warn(\n            'experimental_onFunctionCall should not be defined when using tools',\n          );\n          continue;\n        }\n        const functionCallResponse: ChatRequest | void =\n          await experimental_onFunctionCall(getCurrentMessages(), functionCall);\n\n        // If the user does not return anything as a result of the function call, the loop will break.\n        if (functionCallResponse === undefined) break;\n        // A function call response was returned.\n        // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n        fixFunctionCallArguments(functionCallResponse);\n        updateChatRequest(functionCallResponse);\n      }\n      // If we get here and are expecting a tool call, the message should have one, if not warn and continue\n      if (experimental_onToolCall) {\n        const toolCalls = streamedResponseMessage.tool_calls;\n        if (!(typeof toolCalls === 'object')) {\n          console.warn(\n            'experimental_onToolCall should not be defined when using functions',\n          );\n          continue;\n        }\n        const toolCallResponse: ChatRequest | void =\n          await experimental_onToolCall(getCurrentMessages(), toolCalls);\n\n        // If the user does not return anything as a result of the function call, the loop will break.\n        if (toolCallResponse === undefined) break;\n        // A function call response was returned.\n        // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n        fixFunctionCallArguments(toolCallResponse);\n        updateChatRequest(toolCallResponse);\n      }\n\n      // Make sure function call arguments are sent back to the API as a string\n      function fixFunctionCallArguments(response: ChatRequest) {\n        for (const message of response.messages) {\n          if (message.tool_calls !== undefined) {\n            for (const toolCall of message.tool_calls) {\n              if (typeof toolCall === 'object') {\n                if (\n                  toolCall.function.arguments &&\n                  typeof toolCall.function.arguments !== 'string'\n                ) {\n                  toolCall.function.arguments = JSON.stringify(\n                    toolCall.function.arguments,\n                  );\n                }\n              }\n            }\n          }\n          if (message.function_call !== undefined) {\n            if (typeof message.function_call === 'object') {\n              if (\n                message.function_call.arguments &&\n                typeof message.function_call.arguments !== 'string'\n              ) {\n                message.function_call.arguments = JSON.stringify(\n                  message.function_call.arguments,\n                );\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "/**\n * Converts a data URL of type text/* to a text string.\n */\nexport function getTextFromDataUrl(dataUrl: string): string {\n  const [header, base64Content] = dataUrl.split(',');\n  const mimeType = header.split(';')[0].split(':')[1];\n\n  if (mimeType == null || base64Content == null) {\n    throw new Error('Invalid data URL format');\n  }\n\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n"], "mappings": ";AAEA,SAAS,kBAAkB;;;ACF3B,SAAS,cAAc,0BAA0B;;;ACAjD,OAAO,gBAAgB;;;AC0BhB,SAAS,QAAQ,OAAuB;AAC7C,QAAM,QAAiB,CAAC,MAAM;AAC9B,MAAI,iBAAiB;AACrB,MAAI,eAA8B;AAElC,WAAS,kBAAkB,MAAc,GAAW,WAAkB;AACpE;AACE,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AACR,2BAAiB;AACjB,yBAAe;AACf,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,gBAAgB;AAC3B;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,qBAAqB;AAChC;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,oBAAoB;AAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,wBAAwB,MAAc,GAAW;AACxD,YAAQ,MAAM;AAAA,MACZ,KAAK,KAAK;AACR,cAAM,IAAI;AACV,cAAM,KAAK,2BAA2B;AACtC;AAAA,MACF;AAAA,MACA,KAAK,KAAK;AACR,yBAAiB;AACjB,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,uBAAuB,MAAc,GAAW;AACvD,YAAQ,MAAM;AAAA,MACZ,KAAK,KAAK;AACR,cAAM,IAAI;AACV,cAAM,KAAK,0BAA0B;AACrC;AAAA,MACF;AAAA,MACA,KAAK,KAAK;AACR,yBAAiB;AACjB,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAE3C,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,0BAAkB,MAAM,GAAG,QAAQ;AACnC;AAAA,MAEF,KAAK,uBAAuB;AAC1B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,mBAAmB;AAC9B;AAAA,UACF;AAAA,UACA,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,6BAA6B;AAChC,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,mBAAmB;AAC9B;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,qBAAqB;AACxB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,yBAAyB;AACpC;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,2BAA2B;AAC9B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,4BAA4B;AAEvC;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,8BAA8B;AACjC,0BAAkB,MAAM,GAAG,2BAA2B;AACtD;AAAA,MACF;AAAA,MAEA,KAAK,6BAA6B;AAChC,gCAAwB,MAAM,CAAC;AAC/B;AAAA,MACF;AAAA,MAEA,KAAK,iBAAiB;AACpB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,6BAAiB;AACjB;AAAA,UACF;AAAA,UAEA,KAAK,MAAM;AACT,kBAAM,KAAK,sBAAsB;AACjC;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AAAA,UACnB;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,sBAAsB;AACzB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AACjB,8BAAkB,MAAM,GAAG,0BAA0B;AACrD;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,4BAA4B;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,0BAA0B;AACrC;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,4BAA4B;AAC/B,0BAAkB,MAAM,GAAG,0BAA0B;AACrD;AAAA,MACF;AAAA,MAEA,KAAK,wBAAwB;AAC3B,cAAM,IAAI;AACV,yBAAiB;AAEjB;AAAA,MACF;AAAA,MAEA,KAAK,iBAAiB;AACpB,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,KAAK;AACR,6BAAiB;AACjB;AAAA,UACF;AAAA,UAEA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,KAAK;AACR;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AAC1D,qCAAuB,MAAM,CAAC;AAAA,YAChC;AAEA,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,sCAAwB,MAAM,CAAC;AAAA,YACjC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,sCAAwB,MAAM,CAAC;AAAA,YACjC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AAC1D,qCAAuB,MAAM,CAAC;AAAA,YAChC;AAEA;AAAA,UACF;AAAA,UAEA,SAAS;AACP,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,kBAAkB;AACrB,cAAM,iBAAiB,MAAM,UAAU,cAAe,IAAI,CAAC;AAE3D,YACE,CAAC,QAAQ,WAAW,cAAc,KAClC,CAAC,OAAO,WAAW,cAAc,KACjC,CAAC,OAAO,WAAW,cAAc,GACjC;AACA,gBAAM,IAAI;AAEV,cAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,oCAAwB,MAAM,CAAC;AAAA,UACjC,WAAW,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AACjE,mCAAuB,MAAM,CAAC;AAAA,UAChC;AAAA,QACF,OAAO;AACL,2BAAiB;AAAA,QACnB;AAEA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,MAAM,MAAM,GAAG,iBAAiB,CAAC;AAE9C,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAM,QAAQ,MAAM,CAAC;AAErB,YAAQ,OAAO;AAAA,MACb,KAAK,iBAAiB;AACpB,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,6BAA6B;AAChC,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,4BAA4B;AAC/B,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK,kBAAkB;AACrB,cAAM,iBAAiB,MAAM,UAAU,cAAe,MAAM,MAAM;AAElE,YAAI,OAAO,WAAW,cAAc,GAAG;AACrC,oBAAU,OAAO,MAAM,eAAe,MAAM;AAAA,QAC9C,WAAW,QAAQ,WAAW,cAAc,GAAG;AAC7C,oBAAU,QAAQ,MAAM,eAAe,MAAM;AAAA,QAC/C,WAAW,OAAO,WAAW,cAAc,GAAG;AAC5C,oBAAU,OAAO,MAAM,eAAe,MAAM;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AD7YO,SAAS,iBACd,UACqB;AACrB,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,MAAI;AAEF,WAAO,WAAW,MAAM,QAAQ;AAAA,EAClC,SAAS,SAAS;AAChB,QAAI;AAEF,YAAM,gBAAgB,QAAQ,QAAQ;AACtC,aAAO,WAAW,MAAM,aAAa;AAAA,IACvC,SAASA,UAAS;AAAA,IAElB;AAAA,EACF;AAEA,SAAO;AACT;;;AEJA,IAAM,iBAAkD;AAAA,EACtD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AACA,WAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACF;AAEA,IAAM,yBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,mBAAmB,UACrB,OAAO,MAAM,kBAAkB,YAC/B,MAAM,iBAAiB,QACvB,EAAE,UAAU,MAAM,kBAClB,EAAE,eAAe,MAAM,kBACvB,OAAO,MAAM,cAAc,SAAS,YACpC,OAAO,MAAM,cAAc,cAAc,UACzC;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,iBAA4D;AAAA,EAChE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,WAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACF;AAEA,IAAM,kBAAoD;AAAA,EACxD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AACA,WAAO,EAAE,MAAM,SAAS,MAAM;AAAA,EAChC;AACF;AAEA,IAAM,6BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,QAAQ,UACV,EAAE,UAAU,UACZ,EAAE,aAAa,UACf,OAAO,MAAM,OAAO,YACpB,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,eACf,CAAC,MAAM,QAAQ,MAAM,OAAO,KAC5B,CAAC,MAAM,QAAQ;AAAA,MACb,UACE,QAAQ,QACR,OAAO,SAAS,YAChB,UAAU,QACV,KAAK,SAAS,UACd,UAAU,QACV,KAAK,QAAQ,QACb,OAAO,KAAK,SAAS,YACrB,WAAW,KAAK,QAChB,OAAO,KAAK,KAAK,UAAU;AAAA,IAC/B,GACA;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,iCAOF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,cAAc,UAChB,EAAE,eAAe,UACjB,OAAO,MAAM,aAAa,YAC1B,OAAO,MAAM,cAAc,UAC3B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,UAAU,MAAM;AAAA,QAChB,WAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,wBAAsE;AAAA,EAC1E,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,UAAU,UACZ,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,QACf;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,sBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,MAAM,cAAc,QACpB,CAAC,MAAM,QAAQ,MAAM,UAAU,KAC/B,MAAM,WAAW;AAAA,MACf,QACE,MAAM,QACN,OAAO,OAAO,YACd,EAAE,QAAQ,OACV,OAAO,GAAG,OAAO,YACjB,EAAE,UAAU,OACZ,OAAO,GAAG,SAAS,YACnB,EAAE,cAAc,OAChB,GAAG,YAAY,QACf,OAAO,GAAG,aAAa,YACvB,EAAE,eAAe,GAAG,aACpB,OAAO,GAAG,SAAS,SAAS,YAC5B,OAAO,GAAG,SAAS,cAAc;AAAA,IACrC,GACA;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,+BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AAEA,WAAO,EAAE,MAAM,uBAAuB,MAAM;AAAA,EAC9C;AACF;AAEA,IAAM,qBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,cAAc,UAChB,OAAO,MAAM,aAAa,YAC1B,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,UACtB;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,YAAY,QACd;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IAIF;AAAA,EACF;AACF;AAEA,IAAM,mCAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,cAAc,UAChB,OAAO,MAAM,aAAa,UAC1B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,0BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,mBAAmB,UACrB,OAAO,MAAM,kBAAkB,UAC/B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IAIF;AAAA,EACF;AACF;AAEA,IAAM,0BAUF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,kBAAkB,UACpB,OAAO,MAAM,iBAAiB,YAC9B,EAAE,WAAW,UACb,MAAM,SAAS,QACf,OAAO,MAAM,UAAU,YACvB,EAAE,kBAAkB,MAAM,UAC1B,EAAE,sBAAsB,MAAM,QAC9B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO,MAAM,MAAM,iBAAiB,UAAU;AAChD,YAAM,MAAM,eAAe,OAAO;AAAA,IACpC;AACA,QAAI,OAAO,MAAM,MAAM,qBAAqB,UAAU;AACpD,YAAM,MAAM,mBAAmB,OAAO;AAAA,IACxC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IAQF;AAAA,EACF;AACF;AAEA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AA0CO,IAAM,oBAAoB;AAAA,EAC/B,CAAC,eAAe,IAAI,GAAG;AAAA,EACvB,CAAC,uBAAuB,IAAI,GAAG;AAAA,EAC/B,CAAC,eAAe,IAAI,GAAG;AAAA,EACvB,CAAC,gBAAgB,IAAI,GAAG;AAAA,EACxB,CAAC,2BAA2B,IAAI,GAAG;AAAA,EACnC,CAAC,+BAA+B,IAAI,GAAG;AAAA,EACvC,CAAC,sBAAsB,IAAI,GAAG;AAAA,EAC9B,CAAC,oBAAoB,IAAI,GAAG;AAAA,EAC5B,CAAC,6BAA6B,IAAI,GAAG;AAAA,EACrC,CAAC,mBAAmB,IAAI,GAAG;AAAA,EAC3B,CAAC,qBAAqB,IAAI,GAAG;AAAA,EAC7B,CAAC,iCAAiC,IAAI,GAAG;AAAA,EACzC,CAAC,wBAAwB,IAAI,GAAG;AAAA,EAChC,CAAC,wBAAwB,IAAI,GAAG;AAClC;AAwBO,IAAM,uBAAuB;AAAA,EAClC,CAAC,eAAe,IAAI,GAAG,eAAe;AAAA,EACtC,CAAC,uBAAuB,IAAI,GAAG,uBAAuB;AAAA,EACtD,CAAC,eAAe,IAAI,GAAG,eAAe;AAAA,EACtC,CAAC,gBAAgB,IAAI,GAAG,gBAAgB;AAAA,EACxC,CAAC,2BAA2B,IAAI,GAAG,2BAA2B;AAAA,EAC9D,CAAC,+BAA+B,IAAI,GAAG,+BAA+B;AAAA,EACtE,CAAC,sBAAsB,IAAI,GAAG,sBAAsB;AAAA,EACpD,CAAC,oBAAoB,IAAI,GAAG,oBAAoB;AAAA,EAChD,CAAC,6BAA6B,IAAI,GAAG,6BAA6B;AAAA,EAClE,CAAC,mBAAmB,IAAI,GAAG,mBAAmB;AAAA,EAC9C,CAAC,qBAAqB,IAAI,GAAG,qBAAqB;AAAA,EAClD,CAAC,iCAAiC,IAAI,GACpC,iCAAiC;AAAA,EACnC,CAAC,wBAAwB,IAAI,GAAG,wBAAwB;AAAA,EACxD,CAAC,wBAAwB,IAAI,GAAG,wBAAwB;AAC1D;AAEO,IAAM,aAAa,YAAY,IAAI,UAAQ,KAAK,IAAI;AASpD,IAAM,kBAAkB,CAAC,SAAiC;AAC/D,QAAM,sBAAsB,KAAK,QAAQ,GAAG;AAE5C,MAAI,wBAAwB,IAAI;AAC9B,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AAEA,QAAM,SAAS,KAAK,MAAM,GAAG,mBAAmB;AAEhD,MAAI,CAAC,WAAW,SAAS,MAAwC,GAAG;AAClE,UAAM,IAAI,MAAM,+CAA+C,MAAM,GAAG;AAAA,EAC1E;AAEA,QAAM,OAAO;AAEb,QAAM,YAAY,KAAK,MAAM,sBAAsB,CAAC;AACpD,QAAM,YAAuB,KAAK,MAAM,SAAS;AAEjD,SAAO,kBAAkB,IAAI,EAAE,MAAM,SAAS;AAChD;AAQO,SAAS,iBACd,MACA,OACc;AACd,QAAM,aAAa,YAAY,KAAK,UAAQ,KAAK,SAAS,IAAI;AAE9D,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,6BAA6B,IAAI,EAAE;AAAA,EACrD;AAEA,SAAO,GAAG,WAAW,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA;AACpD;;;AC7jBA,IAAM,UAAU,KAAK,WAAW,CAAC;AAGjC,SAAS,aAAa,QAAsB,aAAqB;AAC/D,QAAM,qBAAqB,IAAI,WAAW,WAAW;AAErD,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AAC1B,uBAAmB,IAAI,OAAO,MAAM;AACpC,cAAU,MAAM;AAAA,EAClB;AACA,SAAO,SAAS;AAEhB,SAAO;AACT;AAaA,gBAAuB,eACrB,QACA;AAAA,EACE;AACF,IAEI,CAAC,GAC2B;AAIhC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,SAAuB,CAAC;AAC9B,MAAI,cAAc;AAElB,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAEpC,QAAI,OAAO;AACT,aAAO,KAAK,KAAK;AACjB,qBAAe,MAAM;AACrB,UAAI,MAAM,MAAM,SAAS,CAAC,MAAM,SAAS;AAEvC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,GAAG;AACvB;AAAA,IACF;AAEA,UAAM,qBAAqB,aAAa,QAAQ,WAAW;AAC3D,kBAAc;AAEd,UAAMC,eAAc,QACjB,OAAO,oBAAoB,EAAE,QAAQ,KAAK,CAAC,EAC3C,MAAM,IAAI,EACV,OAAO,UAAQ,SAAS,EAAE,EAC1B,IAAI,eAAe;AAEtB,eAAW,cAAcA,cAAa;AACpC,YAAM;AAAA,IACR;AAGA,QAAI,0CAAe;AACjB,aAAO,OAAO;AACd;AAAA,IACF;AAAA,EACF;AACF;;;AJpDA,SAAS,2BACP,SACA,aACG;AACH,MAAI,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY;AAAQ,WAAO;AAC5D,SAAO,EAAE,GAAG,SAAS,aAAa,CAAC,GAAG,WAAW,EAAE;AACrD;AAEA,eAAsB,qBAAqB;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC,cAAa;AAAA,EACb,iBAAiB,MAAM,oBAAI,KAAK;AAClC,GAkBG;AA7DH;AA8DE,QAAM,YAAY,eAAe;AACjC,QAAM,YAAuB;AAAA,IAC3B,MAAM,CAAC;AAAA,EACT;AAGA,MAAI,sBAA+C;AAGnD,QAAM,mBAGF,CAAC;AAEL,MAAI,QAIA;AAAA,IACF,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa;AAAA,EACf;AACA,MAAI,eAA4C;AAGhD,mBAAiB,EAAE,MAAM,MAAM,KAAK,eAAe,QAAQ;AAAA,IACzD,WAAW,OAAM,yDAAoB,aAAY;AAAA,EACnD,CAAC,GAAG;AACF,QAAI,SAAS,QAAQ;AACnB,UAAI,UAAU,MAAM,GAAG;AACrB,kBAAU,MAAM,IAAI;AAAA,UAClB,GAAG,UAAU,MAAM;AAAA,UACnB,UAAU,UAAU,MAAM,EAAE,WAAW,MAAM;AAAA,QAC/C;AAAA,MACF,OAAO;AACL,kBAAU,MAAM,IAAI;AAAA,UAClB,IAAIA,YAAW;AAAA,UACf,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,kBAAkB;AAC7B,YAAM,EAAE,kBAAkB,aAAa,IAAI,MAAM;AAEjD,qBAAe,MAAM;AACrB,cAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA,aAAa,mBAAmB;AAAA,MAClC;AAAA,IACF;AAGA,QAAI,SAAS,6BAA6B;AAExC,UAAI,UAAU,QAAQ,MAAM;AAC1B,kBAAU,OAAO;AAAA,UACf,IAAIA,YAAW;AAAA,UACf,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,UAAI,UAAU,KAAK,mBAAmB,MAAM;AAC1C,kBAAU,KAAK,kBAAkB,CAAC;AAAA,MACpC;AAGA,uBAAiB,MAAM,UAAU,IAAI;AAAA,QACnC,MAAM;AAAA,QACN,UAAU,MAAM;AAAA,QAChB,gBAAgB,UAAU,KAAK,gBAAgB;AAAA,MACjD;AAEA,gBAAU,KAAK,gBAAgB,KAAK;AAAA,QAClC,OAAO;AAAA,QACP,YAAY,MAAM;AAAA,QAClB,UAAU,MAAM;AAAA,QAChB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,WAAW,SAAS,mBAAmB;AACrC,YAAM,kBAAkB,iBAAiB,MAAM,UAAU;AAEzD,sBAAgB,QAAQ,MAAM;AAE9B,gBAAU,KAAM,gBAAiB,gBAAgB,cAAc,IAAI;AAAA,QACjE,OAAO;AAAA,QACP,YAAY,MAAM;AAAA,QAClB,UAAU,gBAAgB;AAAA,QAC1B,MAAM,iBAAiB,gBAAgB,IAAI;AAAA,MAC7C;AAIA,MAAC,UAAU,KAAc,mBAAmBA,YAAW;AAAA,IACzD,WAAW,SAAS,aAAa;AAC/B,UAAI,iBAAiB,MAAM,UAAU,KAAK,MAAM;AAE9C,kBAAU,KAAM,gBACd,iBAAiB,MAAM,UAAU,EAAE,cACrC,IAAI,EAAE,OAAO,QAAQ,GAAG,MAAM;AAAA,MAChC,OAAO;AAEL,YAAI,UAAU,QAAQ,MAAM;AAC1B,oBAAU,OAAO;AAAA,YACf,IAAIA,YAAW;AAAA,YACf,MAAM;AAAA,YACN,SAAS;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,YAAI,UAAU,KAAK,mBAAmB,MAAM;AAC1C,oBAAU,KAAK,kBAAkB,CAAC;AAAA,QACpC;AAEA,kBAAU,KAAK,gBAAgB,KAAK;AAAA,UAClC,OAAO;AAAA,UACP,GAAG;AAAA,QACL,CAAC;AAAA,MACH;AAIA,MAAC,UAAU,KAAc,mBAAmBA,YAAW;AAKvD,UAAI,YAAY;AACd,cAAM,SAAS,MAAM,WAAW,EAAE,UAAU,MAAM,CAAC;AACnD,YAAI,UAAU,MAAM;AAElB,oBAAU,KAAM,gBACd,UAAU,KAAM,gBAAiB,SAAS,CAC5C,IAAI,EAAE,OAAO,UAAU,GAAG,OAAO,OAAO;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,WAAW,SAAS,eAAe;AACjC,YAAM,mBAAkB,eAAU,SAAV,mBAAgB;AAExC,UAAI,mBAAmB,MAAM;AAC3B,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAIA,YAAM,sBAAsB,gBAAgB;AAAA,QAC1C,gBAAc,WAAW,eAAe,MAAM;AAAA,MAChD;AAEA,UAAI,wBAAwB,IAAI;AAC9B,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,sBAAgB,mBAAmB,IAAI;AAAA,QACrC,GAAG,gBAAgB,mBAAmB;AAAA,QACtC,OAAO;AAAA,QACP,GAAG;AAAA,MACL;AAAA,IACF;AAEA,QAAI,sBAAkD;AAEtD,QAAI,SAAS,iBAAiB;AAC5B,gBAAU,eAAe,IAAI;AAAA,QAC3B,IAAIA,YAAW;AAAA,QACf,MAAM;AAAA,QACN,SAAS;AAAA,QACT,eAAe,MAAM;AAAA,QACrB,MAAM,MAAM,cAAc;AAAA,QAC1B;AAAA,MACF;AAEA,4BAAsB,UAAU,eAAe;AAAA,IACjD;AAEA,QAAI,kBAA8C;AAElD,QAAI,SAAS,cAAc;AACzB,gBAAU,YAAY,IAAI;AAAA,QACxB,IAAIA,YAAW;AAAA,QACf,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,MAAM;AAAA,QAClB;AAAA,MACF;AAEA,wBAAkB,UAAU,YAAY;AAAA,IAC1C;AAEA,QAAI,SAAS,QAAQ;AACnB,gBAAU,MAAM,EAAE,KAAK,GAAG,KAAK;AAAA,IACjC;AAEA,QAAI,kBAAkB,UAAU,MAAM;AAEtC,QAAI,SAAS,uBAAuB;AAClC,UAAI,CAAC,qBAAqB;AACxB,8BAAsB,CAAC,GAAG,KAAK;AAAA,MACjC,OAAO;AACL,4BAAoB,KAAK,GAAG,KAAK;AAAA,MACnC;AAGA,4BAAsB;AAAA,QACpB,UAAU,eAAe;AAAA,QACzB;AAAA,MACF;AACA,wBAAkB;AAAA,QAChB,UAAU,YAAY;AAAA,QACtB;AAAA,MACF;AACA,wBAAkB;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAGA,QAAI,2DAAqB,QAAQ;AAC/B,YAAM,oBAAyC;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,wBAAkB,QAAQ,SAAO;AAC/B,YAAI,UAAU,GAAG,GAAG;AAClB,UAAC,UAAU,GAAG,EAAc,cAAc,CAAC,GAAG,mBAAoB;AAAA,QACpE;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,SAAS,CAAC,qBAAqB,iBAAiB,eAAe,EAClE,OAAO,OAAO,EACd,IAAI,cAAY;AAAA,MACf,GAAG,2BAA2B,SAAS,mBAAmB;AAAA,IAC5D,EAAE;AAEJ,WAAO,QAAQ,CAAC,GAAG,UAAU,MAAM,CAAC,CAAC;AAAA,EACvC;AAEA,uCAAW,EAAE,WAAW,cAAc,MAAM;AAE5C,SAAO;AAAA,IACL,UAAU;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,EAAE,OAAO,OAAO;AAAA,IAChB,MAAM,UAAU;AAAA,EAClB;AACF;;;AK7TA,IAAM,mBAAmB,MAAM;AAE/B,eAAsB,YAAY;AAAA,EAChC;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,EACA,OAAAC,SAAQ,iBAAiB;AAC3B,GAcG;AAnCH;AAoCE,QAAM,WAAW,MAAMA,OAAM,KAAK;AAAA,IAChC,QAAQ;AAAA,IACR,MAAM,KAAK,UAAU,IAAI;AAAA,IACzB,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,IACA,SAAQ,iFAAqB;AAAA,IAC7B;AAAA,EACF,CAAC,EAAE,MAAM,SAAO;AACd,6BAAyB;AACzB,UAAM;AAAA,EACR,CAAC;AAED,MAAI,YAAY;AACd,QAAI;AACF,YAAM,WAAW,QAAQ;AAAA,IAC3B,SAAS,KAAK;AACZ,YAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,6BAAyB;AACzB,UAAM,IAAI;AAAA,OACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAEA,QAAM,SAAS,SAAS,KAAK,UAAU;AAEvC,UAAQ,gBAAgB;AAAA,IACtB,KAAK,QAAQ;AACX,YAAM,UAAU,mBAAmB;AAEnC,YAAM,gBAAgB;AAAA,QACpB,IAAID,YAAW;AAAA,QACf,WAAW,oBAAI,KAAK;AAAA,QACpB,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAEA,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI,MAAM;AACR;AAAA,QACF;AAEA,sBAAc,WAAW,QAAQ,KAAK;AAGtC,iBAAS,CAAC,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAGnC,aAAI,0DAAwB,MAAM;AAChC,iBAAO,OAAO;AACd;AAAA,QACF;AAAA,MACF;AAGA,2CAAW,eAAe;AAAA,QACxB,OAAO,EAAE,kBAAkB,KAAK,cAAc,KAAK,aAAa,IAAI;AAAA,QACpE,cAAc;AAAA,MAChB;AAEA,aAAO;AAAA,QACL,UAAU,CAAC,aAAa;AAAA,QACxB,MAAM,CAAC;AAAA,MACT;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,aAAO,MAAM,qBAAqB;AAAA,QAChC;AAAA,QACA,oBACE,mBAAmB,OAAO,EAAE,SAAS,gBAAgB,EAAE,IAAI;AAAA,QAC7D,QAAQ;AAAA,QACR;AAAA,QACA,SAAS,EAAE,WAAW,cAAc,MAAM,GAAG;AAC3C,cAAI,YAAY,UAAU,QAAQ,MAAM;AACtC,qBAAS,UAAU,MAAM,EAAE,OAAO,aAAa,CAAC;AAAA,UAClD;AAAA,QACF;AAAA,QACA,YAAAA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,SAAS;AACP,YAAM,kBAAyB;AAC/B,YAAM,IAAI,MAAM,4BAA4B,eAAe,EAAE;AAAA,IAC/D;AAAA,EACF;AACF;;;AChIA,IAAME,oBAAmB,MAAM;AAE/B,eAAsB,kBAAkB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC,SAAQD,kBAAiB;AAC3B,GAgBG;AACD,MAAI;AACF,eAAW,IAAI;AACf,aAAS,MAAS;AAElB,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,uBAAmB,eAAe;AAGlC,kBAAc,EAAE;AAEhB,UAAM,MAAM,MAAMC,OAAM,KAAK;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU;AAAA,QACnB;AAAA,QACA,GAAG;AAAA,MACL,CAAC;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL;AAAA,MACA,QAAQ,gBAAgB;AAAA,IAC1B,CAAC,EAAE,MAAM,SAAO;AACd,YAAM;AAAA,IACR,CAAC;AAED,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,GAAG;AAAA,MACtB,SAAS,KAAK;AACZ,cAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,IAAI;AAAA,QACP,MAAM,IAAI,KAAK,KAAM;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,CAAC,IAAI,MAAM;AACb,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAEA,QAAI,SAAS;AACb,UAAM,SAAS,IAAI,KAAK,UAAU;AAElC,YAAQ,gBAAgB;AAAA,MACtB,KAAK,QAAQ;AACX,cAAM,UAAU,mBAAmB;AAEnC,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI,MAAM;AACR;AAAA,UACF;AAGA,oBAAU,QAAQ,KAAK;AACvB,wBAAc,MAAM;AAGpB,cAAI,oBAAoB,MAAM;AAC5B,mBAAO,OAAO;AACd;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,yBAAiB,EAAE,MAAM,MAAM,KAAK,eAAe,QAAQ;AAAA,UACzD,WAAW,MAAM,oBAAoB;AAAA,QACvC,CAAC,GAAG;AACF,kBAAQ,MAAM;AAAA,YACZ,KAAK,QAAQ;AACX,wBAAU;AACV,4BAAc,MAAM;AACpB;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,+CAAS;AACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,kBAAyB;AAC/B,cAAM,IAAI,MAAM,4BAA4B,eAAe,EAAE;AAAA,MAC/D;AAAA,IACF;AAEA,QAAI,UAAU;AACZ,eAAS,QAAQ,MAAM;AAAA,IACzB;AAEA,uBAAmB,IAAI;AACvB,WAAO;AAAA,EACT,SAAS,KAAK;AAEZ,QAAK,IAAY,SAAS,cAAc;AACtC,yBAAmB,IAAI;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,OAAO;AACxB,UAAI,SAAS;AACX,gBAAQ,GAAG;AAAA,MACb;AAAA,IACF;AAEA,aAAS,GAAY;AAAA,EACvB,UAAE;AACA,eAAW,KAAK;AAAA,EAClB;AACF;;;AChJA,SAAS,mBAAmB,SAAmB;AAC7C,QAAM,UAAU,IAAI,YAAY;AAEhC,MAAI,CAAC,SAAS;AACZ,WAAO,SAAU,OAAuC;AACtD,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,IAC/C;AAAA,EACF;AAEA,SAAO,SAAU,OAA+B;AAC9C,UAAM,UAAU,QACb,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,EAC9B,MAAM,IAAI,EACV,OAAO,UAAQ,SAAS,EAAE;AAE7B,WAAO,QAAQ,IAAI,eAAe,EAAE,OAAO,OAAO;AAAA,EACpD;AACF;;;AC1BO,SAAS,gBAAgB,MAAW,MAAoB;AAE7D,MAAI,SAAS;AAAM,WAAO;AAG1B,MAAI,QAAQ,QAAQ,QAAQ;AAAM,WAAO;AAGzC,MAAI,OAAO,SAAS,YAAY,OAAO,SAAS;AAC9C,WAAO,SAAS;AAGlB,MAAI,KAAK,gBAAgB,KAAK;AAAa,WAAO;AAGlD,MAAI,gBAAgB,QAAQ,gBAAgB,MAAM;AAChD,WAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAAA,EACzC;AAGA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,QAAI,KAAK,WAAW,KAAK;AAAQ,aAAO;AACxC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,CAAC,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAG,eAAO;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAGA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,MAAM;AAAQ,WAAO;AAG1C,aAAW,OAAO,OAAO;AACvB,QAAI,CAAC,MAAM,SAAS,GAAG;AAAG,aAAO;AACjC,QAAI,CAAC,gBAAgB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAAG,aAAO;AAAA,EACrD;AAEA,SAAO;AACT;;;ACvCA,eAAsB,kBAAkB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAcG;AACD,SAAO,MAAM;AAGX,UAAM,+BAA+B,MAAM,oBAAoB;AAG/D,QAAI,cAAc,8BAA8B;AAC9C,UAAI,uBAAuB;AAE3B,iBAAW,WAAW,6BAA6B,UAAU;AAE3D,aACG,QAAQ,kBAAkB,UACzB,OAAO,QAAQ,kBAAkB,cAClC,QAAQ,eAAe,UACtB,OAAO,QAAQ,eAAe,WAChC;AACA;AAAA,QACF;AAEA,+BAAuB;AAEvB,YAAI,6BAA6B;AAC/B,gBAAM,eAAe,QAAQ;AAG7B,cAAI,OAAO,iBAAiB,UAAU;AACpC,oBAAQ;AAAA,cACN;AAAA,YACF;AACA;AAAA,UACF;AAMA,gBAAM,uBACJ,MAAM;AAAA,YACJ,mBAAmB;AAAA,YACnB;AAAA,UACF;AAGF,cAAI,yBAAyB,QAAW;AACtC,mCAAuB;AACvB;AAAA,UACF;AAIA,4BAAkB,oBAAoB;AAAA,QACxC;AAEA,YAAI,yBAAyB;AAC3B,gBAAM,YAAY,QAAQ;AAG1B,cACE,CAAC,MAAM,QAAQ,SAAS,KACxB,UAAU,KAAK,cAAY,OAAO,aAAa,QAAQ,GACvD;AACA,oBAAQ;AAAA,cACN;AAAA,YACF;AACA;AAAA,UACF;AAKA,gBAAM,mBACJ,MAAM,wBAAwB,mBAAmB,GAAG,SAAS;AAG/D,cAAI,qBAAqB,QAAW;AAClC,mCAAuB;AACvB;AAAA,UACF;AAIA,4BAAkB,gBAAgB;AAAA,QACpC;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB;AACzB;AAAA,MACF;AAAA,IACF,OAAO;AAqDL,UAASC,4BAAT,SAAkC,UAAuB;AACvD,mBAAW,WAAW,SAAS,UAAU;AACvC,cAAI,QAAQ,eAAe,QAAW;AACpC,uBAAW,YAAY,QAAQ,YAAY;AACzC,kBAAI,OAAO,aAAa,UAAU;AAChC,oBACE,SAAS,SAAS,aAClB,OAAO,SAAS,SAAS,cAAc,UACvC;AACA,2BAAS,SAAS,YAAY,KAAK;AAAA,oBACjC,SAAS,SAAS;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,QAAQ,kBAAkB,QAAW;AACvC,gBAAI,OAAO,QAAQ,kBAAkB,UAAU;AAC7C,kBACE,QAAQ,cAAc,aACtB,OAAO,QAAQ,cAAc,cAAc,UAC3C;AACA,wBAAQ,cAAc,YAAY,KAAK;AAAA,kBACrC,QAAQ,cAAc;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AA7BS,qCAAAA;AApDT,YAAM,0BAA0B;AAGhC,WACG,wBAAwB,kBAAkB,UACzC,OAAO,wBAAwB,kBAAkB,cAClD,wBAAwB,eAAe,UACtC,OAAO,wBAAwB,eAAe,WAChD;AACA;AAAA,MACF;AAGA,UAAI,6BAA6B;AAC/B,cAAM,eAAe,wBAAwB;AAC7C,YAAI,EAAE,OAAO,iBAAiB,WAAW;AACvC,kBAAQ;AAAA,YACN;AAAA,UACF;AACA;AAAA,QACF;AACA,cAAM,uBACJ,MAAM,4BAA4B,mBAAmB,GAAG,YAAY;AAGtE,YAAI,yBAAyB;AAAW;AAGxC,QAAAA,0BAAyB,oBAAoB;AAC7C,0BAAkB,oBAAoB;AAAA,MACxC;AAEA,UAAI,yBAAyB;AAC3B,cAAM,YAAY,wBAAwB;AAC1C,YAAI,EAAE,OAAO,cAAc,WAAW;AACpC,kBAAQ;AAAA,YACN;AAAA,UACF;AACA;AAAA,QACF;AACA,cAAM,mBACJ,MAAM,wBAAwB,mBAAmB,GAAG,SAAS;AAG/D,YAAI,qBAAqB;AAAW;AAGpC,QAAAA,0BAAyB,gBAAgB;AACzC,0BAAkB,gBAAgB;AAAA,MACpC;AAAA,IAiCF;AAAA,EACF;AACF;;;ACvMO,SAAS,mBAAmB,SAAyB;AAC1D,QAAM,CAAC,QAAQ,aAAa,IAAI,QAAQ,MAAM,GAAG;AACjD,QAAM,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAElD,MAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAEA,MAAI;AACF,WAAO,OAAO,KAAK,aAAa;AAAA,EAClC,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AACF;", "names": ["ignored", "streamParts", "generateId", "generateId", "fetch", "getOriginalFetch", "fetch", "fixFunctionCallArguments"]}