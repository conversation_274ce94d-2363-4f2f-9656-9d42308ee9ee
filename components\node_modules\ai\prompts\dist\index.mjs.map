{"version": 3, "sources": ["../anthropic.ts", "../huggingface.ts", "../openai.tsx"], "sourcesContent": ["import { Message } from '@ai-sdk/ui-utils';\n\n/**\n * A prompt constructor for Anthropic models.\n * Does not support `function` messages.\n * @see https://docs.anthropic.com/claude/reference/getting-started-with-the-api\n */\nexport function experimental_buildAnthropicPrompt(\n  messages: Pick<Message, 'content' | 'role'>[],\n) {\n  return (\n    messages.map(({ content, role }) => {\n      if (role === 'user') {\n        return `\\n\\nHuman: ${content}`;\n      } else {\n        return `\\n\\nAssistant: ${content}`;\n      }\n    }) + '\\n\\nAssistant:'\n  );\n}\n\n/**\n * A prompt constructor for Anthropic V3 models which require Messages API.\n * Does not support message with image content\n * @see https://docs.anthropic.com/claude/reference/messages_post\n */\nexport function experimental_buildAnthropicMessages(\n  messages: Pick<Message, 'content' | 'role'>[],\n) {\n  return messages.map(({ content, role }) => {\n    if (!['assistant', 'user'].includes(role)) {\n      throw new Error(`Cannot use ${role} on Anthropic V3 Messages API`);\n    }\n    return {\n      role,\n      content: [{ type: 'text', text: content }],\n    };\n  });\n}\n", "import { Message } from '@ai-sdk/ui-utils';\n\n/**\n * A prompt constructor for the HuggingFace StarChat Beta model.\n * Does not support `function` messages.\n * @see https://huggingface.co/HuggingFaceH4/starchat-beta\n */\nexport function experimental_buildStarChatBetaPrompt(\n  messages: Pick<Message, 'content' | 'role'>[],\n) {\n  return (\n    messages\n      .map(({ content, role }) => {\n        if (role === 'user') {\n          return `<|user|>\\n${content}<|end|>\\n`;\n        } else if (role === 'assistant') {\n          return `<|assistant|>\\n${content}<|end|>\\n`;\n        } else if (role === 'system') {\n          return `<|system|>\\n${content}<|end|>\\n`;\n        } else if (role === 'function') {\n          throw new Error('StarChat Beta does not support function calls.');\n        }\n      })\n      .join('') + '<|assistant|>'\n  );\n}\n\n/**\n * A prompt constructor for HuggingFace OpenAssistant models.\n * Does not support `function` or `system` messages.\n * @see https://huggingface.co/OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5\n */\nexport function experimental_buildOpenAssistantPrompt(\n  messages: Pick<Message, 'content' | 'role'>[],\n) {\n  return (\n    messages\n      .map(({ content, role }) => {\n        if (role === 'user') {\n          return `<|prompter|>${content}<|endoftext|>`;\n        } else if (role === 'function') {\n          throw new Error('OpenAssistant does not support function calls.');\n        } else if (role === 'system') {\n          throw new Error('OpenAssistant does not support system messages.');\n        } else {\n          return `<|assistant|>${content}<|endoftext|>`;\n        }\n      })\n      .join('') + '<|assistant|>'\n  );\n}\n\n/**\n * A prompt constructor for HuggingFace LLama 2 chat models.\n * Does not support `function` messages.\n * @see https://huggingface.co/meta-llama/Llama-2-70b-chat-hf and https://huggingface.co/blog/llama2#how-to-prompt-llama-2\n */\nexport function experimental_buildLlama2Prompt(\n  messages: Pick<Message, 'content' | 'role'>[],\n) {\n  const startPrompt = `<s>[INST] `;\n  const endPrompt = ` [/INST]`;\n  const conversation = messages.map(({ content, role }, index) => {\n    if (role === 'user') {\n      return content.trim();\n    } else if (role === 'assistant') {\n      return ` [/INST] ${content}</s><s>[INST] `;\n    } else if (role === 'function') {\n      throw new Error('Llama 2 does not support function calls.');\n    } else if (role === 'system' && index === 0) {\n      return `<<SYS>>\\n${content}\\n<</SYS>>\\n\\n`;\n    } else {\n      throw new Error(`Invalid message role: ${role}`);\n    }\n  });\n\n  return startPrompt + conversation.join('') + endPrompt;\n}\n", "import { Message } from '@ai-sdk/ui-utils';\n\nexport function experimental_buildOpenAIMessages(\n  messages: Message[],\n): ChatCompletionMessageParam[] {\n  return messages.map(message => {\n    switch (message.role) {\n      case 'system':\n      case 'user':\n        return {\n          role: message.role,\n          content: message.content,\n        } satisfies ChatCompletionMessageParam;\n\n      case 'assistant': {\n        const function_call = message.function_call;\n\n        if (\n          function_call !== undefined &&\n          (typeof function_call === 'string' ||\n            function_call.arguments === undefined ||\n            function_call.name === undefined)\n        ) {\n          throw new Error(\n            'Invalid function call in message. Expected a function call object',\n          );\n        }\n\n        return {\n          role: message.role,\n          content: message.content,\n          function_call:\n            function_call === undefined\n              ? undefined\n              : {\n                  name: function_call.name!,\n                  arguments: function_call.arguments!,\n                },\n        } satisfies ChatCompletionMessageParam;\n      }\n\n      case 'function': {\n        if (message.name === undefined) {\n          throw new Error('Invalid function call in message. Expected a name');\n        }\n\n        return {\n          role: message.role,\n          content: message.content,\n          name: message.name,\n        } satisfies ChatCompletionMessageParam;\n      }\n\n      case 'data': {\n        throw \"unsupported message role 'data'\";\n      }\n\n      case 'tool': {\n        if (message.name === undefined) {\n          throw new Error('Invalid tool message. Expected a name');\n        }\n\n        if (message.tool_call_id === undefined) {\n          throw new Error('Invalid tool message. Expected a tool_call_id');\n        }\n\n        return {\n          role: message.role,\n          content: message.content,\n          tool_call_id: message.tool_call_id,\n        } satisfies ChatCompletionMessageParam;\n      }\n    }\n  });\n}\n\n// copy of open ai messages (so we don't have a dependency on the openai package)\nexport type ChatCompletionMessageParam =\n  | ChatCompletionSystemMessageParam\n  | ChatCompletionUserMessageParam\n  | ChatCompletionAssistantMessageParam\n  | ChatCompletionToolMessageParam\n  | ChatCompletionFunctionMessageParam;\n\nexport interface ChatCompletionSystemMessageParam {\n  /**\n   * The contents of the system message.\n   */\n  content: string;\n\n  /**\n   * The role of the messages author, in this case `system`.\n   */\n  role: 'system';\n\n  /**\n   * An optional name for the participant. Provides the model information to\n   * differentiate between participants of the same role.\n   */\n  name?: string;\n}\n\nexport interface ChatCompletionUserMessageParam {\n  /**\n   * The contents of the user message.\n   */\n  content: string | Array<ChatCompletionContentPart>;\n\n  /**\n   * The role of the messages author, in this case `user`.\n   */\n  role: 'user';\n\n  /**\n   * An optional name for the participant. Provides the model information to\n   * differentiate between participants of the same role.\n   */\n  name?: string;\n}\n\nexport type ChatCompletionContentPart =\n  | ChatCompletionContentPartText\n  | ChatCompletionContentPartImage;\n\nexport interface ChatCompletionContentPartText {\n  /**\n   * The text content.\n   */\n  text: string;\n\n  /**\n   * The type of the content part.\n   */\n  type: 'text';\n}\n\nexport interface ChatCompletionContentPartImage {\n  image_url: ChatCompletionContentPartImage.ImageURL;\n\n  /**\n   * The type of the content part.\n   */\n  type: 'image_url';\n}\n\nexport namespace ChatCompletionContentPartImage {\n  export interface ImageURL {\n    /**\n     * Either a URL of the image or the base64 encoded image data.\n     */\n    url: string;\n\n    /**\n     * Specifies the detail level of the image. Learn more in the\n     * [Vision guide](https://platform.openai.com/docs/guides/vision/low-or-high-fidelity-image-understanding).\n     */\n    detail?: 'auto' | 'low' | 'high';\n  }\n}\n\nexport interface ChatCompletionAssistantMessageParam {\n  /**\n   * The role of the messages author, in this case `assistant`.\n   */\n  role: 'assistant';\n\n  /**\n   * The contents of the assistant message. Required unless `tool_calls` or\n   * `function_call` is specified.\n   */\n  content?: string | null;\n\n  /**\n   * @deprecated: Deprecated and replaced by `tool_calls`. The name and arguments of\n   * a function that should be called, as generated by the model.\n   */\n  function_call?: ChatCompletionAssistantMessageParam.FunctionCall;\n\n  /**\n   * An optional name for the participant. Provides the model information to\n   * differentiate between participants of the same role.\n   */\n  name?: string;\n\n  /**\n   * The tool calls generated by the model, such as function calls.\n   */\n  tool_calls?: Array<ChatCompletionMessageToolCall>;\n}\n\nexport namespace ChatCompletionAssistantMessageParam {\n  /**\n   * @deprecated: Deprecated and replaced by `tool_calls`. The name and arguments of\n   * a function that should be called, as generated by the model.\n   */\n  export interface FunctionCall {\n    /**\n     * The arguments to call the function with, as generated by the model in JSON\n     * format. Note that the model does not always generate valid JSON, and may\n     * hallucinate parameters not defined by your function schema. Validate the\n     * arguments in your code before calling your function.\n     */\n    arguments: string;\n\n    /**\n     * The name of the function to call.\n     */\n    name: string;\n  }\n}\n\nexport interface ChatCompletionMessageToolCall {\n  /**\n   * The ID of the tool call.\n   */\n  id: string;\n\n  /**\n   * The function that the model called.\n   */\n  function: ChatCompletionMessageToolCall.Function;\n\n  /**\n   * The type of the tool. Currently, only `function` is supported.\n   */\n  type: 'function';\n}\n\nexport namespace ChatCompletionMessageToolCall {\n  /**\n   * The function that the model called.\n   */\n  export interface Function {\n    /**\n     * The arguments to call the function with, as generated by the model in JSON\n     * format. Note that the model does not always generate valid JSON, and may\n     * hallucinate parameters not defined by your function schema. Validate the\n     * arguments in your code before calling your function.\n     */\n    arguments: string;\n\n    /**\n     * The name of the function to call.\n     */\n    name: string;\n  }\n}\n\nexport interface ChatCompletionToolMessageParam {\n  /**\n   * The contents of the tool message.\n   */\n  content: string;\n\n  /**\n   * The role of the messages author, in this case `tool`.\n   */\n  role: 'tool';\n\n  /**\n   * Tool call that this message is responding to.\n   */\n  tool_call_id: string;\n}\n\nexport interface ChatCompletionFunctionMessageParam {\n  /**\n   * The return value from the function call, to return to the model.\n   */\n  content: string | null;\n\n  /**\n   * The name of the function to call.\n   */\n  name: string;\n\n  /**\n   * The role of the messages author, in this case `function`.\n   */\n  role: 'function';\n}\n"], "mappings": ";AAOO,SAAS,kCACd,UACA;AACA,SACE,SAAS,IAAI,CAAC,EAAE,SAAS,KAAK,MAAM;AAClC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA;AAAA,SAAc,OAAO;AAAA,IAC9B,OAAO;AACL,aAAO;AAAA;AAAA,aAAkB,OAAO;AAAA,IAClC;AAAA,EACF,CAAC,IAAI;AAET;AAOO,SAAS,oCACd,UACA;AACA,SAAO,SAAS,IAAI,CAAC,EAAE,SAAS,KAAK,MAAM;AACzC,QAAI,CAAC,CAAC,aAAa,MAAM,EAAE,SAAS,IAAI,GAAG;AACzC,YAAM,IAAI,MAAM,cAAc,IAAI,+BAA+B;AAAA,IACnE;AACA,WAAO;AAAA,MACL;AAAA,MACA,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,IAC3C;AAAA,EACF,CAAC;AACH;;;AC/BO,SAAS,qCACd,UACA;AACA,SACE,SACG,IAAI,CAAC,EAAE,SAAS,KAAK,MAAM;AAC1B,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,EAAa,OAAO;AAAA;AAAA,IAC7B,WAAW,SAAS,aAAa;AAC/B,aAAO;AAAA,EAAkB,OAAO;AAAA;AAAA,IAClC,WAAW,SAAS,UAAU;AAC5B,aAAO;AAAA,EAAe,OAAO;AAAA;AAAA,IAC/B,WAAW,SAAS,YAAY;AAC9B,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AAAA,EACF,CAAC,EACA,KAAK,EAAE,IAAI;AAElB;AAOO,SAAS,sCACd,UACA;AACA,SACE,SACG,IAAI,CAAC,EAAE,SAAS,KAAK,MAAM;AAC1B,QAAI,SAAS,QAAQ;AACnB,aAAO,eAAe,OAAO;AAAA,IAC/B,WAAW,SAAS,YAAY;AAC9B,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE,WAAW,SAAS,UAAU;AAC5B,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE,OAAO;AACL,aAAO,gBAAgB,OAAO;AAAA,IAChC;AAAA,EACF,CAAC,EACA,KAAK,EAAE,IAAI;AAElB;AAOO,SAAS,+BACd,UACA;AACA,QAAM,cAAc;AACpB,QAAM,YAAY;AAClB,QAAM,eAAe,SAAS,IAAI,CAAC,EAAE,SAAS,KAAK,GAAG,UAAU;AAC9D,QAAI,SAAS,QAAQ;AACnB,aAAO,QAAQ,KAAK;AAAA,IACtB,WAAW,SAAS,aAAa;AAC/B,aAAO,YAAY,OAAO;AAAA,IAC5B,WAAW,SAAS,YAAY;AAC9B,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC5D,WAAW,SAAS,YAAY,UAAU,GAAG;AAC3C,aAAO;AAAA,EAAY,OAAO;AAAA;AAAA;AAAA;AAAA,IAC5B,OAAO;AACL,YAAM,IAAI,MAAM,yBAAyB,IAAI,EAAE;AAAA,IACjD;AAAA,EACF,CAAC;AAED,SAAO,cAAc,aAAa,KAAK,EAAE,IAAI;AAC/C;;;AC3EO,SAAS,iCACd,UAC8B;AAC9B,SAAO,SAAS,IAAI,aAAW;AAC7B,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,UACL,MAAM,QAAQ;AAAA,UACd,SAAS,QAAQ;AAAA,QACnB;AAAA,MAEF,KAAK,aAAa;AAChB,cAAM,gBAAgB,QAAQ;AAE9B,YACE,kBAAkB,WACjB,OAAO,kBAAkB,YACxB,cAAc,cAAc,UAC5B,cAAc,SAAS,SACzB;AACA,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,UACL,MAAM,QAAQ;AAAA,UACd,SAAS,QAAQ;AAAA,UACjB,eACE,kBAAkB,SACd,SACA;AAAA,YACE,MAAM,cAAc;AAAA,YACpB,WAAW,cAAc;AAAA,UAC3B;AAAA,QACR;AAAA,MACF;AAAA,MAEA,KAAK,YAAY;AACf,YAAI,QAAQ,SAAS,QAAW;AAC9B,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACrE;AAEA,eAAO;AAAA,UACL,MAAM,QAAQ;AAAA,UACd,SAAS,QAAQ;AAAA,UACjB,MAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,cAAM;AAAA,MACR;AAAA,MAEA,KAAK,QAAQ;AACX,YAAI,QAAQ,SAAS,QAAW;AAC9B,gBAAM,IAAI,MAAM,uCAAuC;AAAA,QACzD;AAEA,YAAI,QAAQ,iBAAiB,QAAW;AACtC,gBAAM,IAAI,MAAM,+CAA+C;AAAA,QACjE;AAEA,eAAO;AAAA,UACL,MAAM,QAAQ;AAAA,UACd,SAAS,QAAQ;AAAA,UACjB,cAAc,QAAQ;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;", "names": []}