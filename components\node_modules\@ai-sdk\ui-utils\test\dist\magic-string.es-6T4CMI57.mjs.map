{"version": 3, "sources": ["../../../../node_modules/.pnpm/@jridgewell+sourcemap-codec@1.4.15/node_modules/@jridgewell/sourcemap-codec/src/sourcemap-codec.ts", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/BitSet.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/Chunk.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/SourceMap.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/utils/guessIndent.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/utils/getRelativePath.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/utils/isObject.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/utils/getLocator.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/utils/Mappings.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/MagicString.js", "../../../../node_modules/.pnpm/magic-string@0.30.10/node_modules/magic-string/src/Bundle.js"], "sourcesContent": ["export type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nconst comma = ','.charCodeAt(0);\nconst semicolon = ';'.charCodeAt(0);\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n    ? {\n        decode(buf: Uint8Array) {\n          const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n          return out.toString();\n        },\n      }\n    : {\n        decode(buf: Uint8Array) {\n          let out = '';\n          for (let i = 0; i < buf.length; i++) {\n            out += String.fromCharCode(buf[i]);\n          }\n          return out;\n        },\n      };\n\nexport function decode(mappings: string): SourceMapMappings {\n  const state: [number, number, number, number, number] = new Int32Array(5) as any;\n  const decoded: SourceMapMappings = [];\n\n  let index = 0;\n  do {\n    const semi = indexOf(mappings, index);\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    state[0] = 0;\n\n    for (let i = index; i < semi; i++) {\n      let seg: SourceMapSegment;\n\n      i = decodeInteger(mappings, i, state, 0); // genColumn\n      const col = state[0];\n      if (col < lastCol) sorted = false;\n      lastCol = col;\n\n      if (hasMoreVlq(mappings, i, semi)) {\n        i = decodeInteger(mappings, i, state, 1); // sourcesIndex\n        i = decodeInteger(mappings, i, state, 2); // sourceLine\n        i = decodeInteger(mappings, i, state, 3); // sourceColumn\n\n        if (hasMoreVlq(mappings, i, semi)) {\n          i = decodeInteger(mappings, i, state, 4); // namesIndex\n          seg = [col, state[1], state[2], state[3], state[4]];\n        } else {\n          seg = [col, state[1], state[2], state[3]];\n        }\n      } else {\n        seg = [col];\n      }\n\n      line.push(seg);\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    index = semi + 1;\n  } while (index <= mappings.length);\n\n  return decoded;\n}\n\nfunction indexOf(mappings: string, index: number): number {\n  const idx = mappings.indexOf(';', index);\n  return idx === -1 ? mappings.length : idx;\n}\n\nfunction decodeInteger(mappings: string, pos: number, state: SourceMapSegment, j: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = mappings.charCodeAt(pos++);\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  state[j] += value;\n  return pos;\n}\n\nfunction hasMoreVlq(mappings: string, i: number, length: number): boolean {\n  if (i >= length) return false;\n  return mappings.charCodeAt(i) !== comma;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const state: [number, number, number, number, number] = new Int32Array(5) as any;\n  const bufLength = 1024 * 16;\n  const subLength = bufLength - 36;\n  const buf = new Uint8Array(bufLength);\n  const sub = buf.subarray(0, subLength);\n  let pos = 0;\n  let out = '';\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) {\n      if (pos === bufLength) {\n        out += td.decode(buf);\n        pos = 0;\n      }\n      buf[pos++] = semicolon;\n    }\n    if (line.length === 0) continue;\n\n    state[0] = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      // We can push up to 5 ints, each int can take at most 7 chars, and we\n      // may push a comma.\n      if (pos > subLength) {\n        out += td.decode(sub);\n        buf.copyWithin(0, subLength, pos);\n        pos -= subLength;\n      }\n      if (j > 0) buf[pos++] = comma;\n\n      pos = encodeInteger(buf, pos, state, segment, 0); // genColumn\n\n      if (segment.length === 1) continue;\n      pos = encodeInteger(buf, pos, state, segment, 1); // sourcesIndex\n      pos = encodeInteger(buf, pos, state, segment, 2); // sourceLine\n      pos = encodeInteger(buf, pos, state, segment, 3); // sourceColumn\n\n      if (segment.length === 4) continue;\n      pos = encodeInteger(buf, pos, state, segment, 4); // namesIndex\n    }\n  }\n\n  return out + td.decode(buf.subarray(0, pos));\n}\n\nfunction encodeInteger(\n  buf: Uint8Array,\n  pos: number,\n  state: SourceMapSegment,\n  segment: SourceMapSegment,\n  j: number,\n): number {\n  const next = segment[j];\n  let num = next - state[j];\n  state[j] = next;\n\n  num = num < 0 ? (-num << 1) | 1 : num << 1;\n  do {\n    let clamped = num & 0b011111;\n    num >>>= 5;\n    if (num > 0) clamped |= 0b100000;\n    buf[pos++] = intToChar[clamped];\n  } while (num > 0);\n\n  return pos;\n}\n", "export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}\n", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\tif (DEBUG) {\n\t\t\t// we make these non-enumerable, for sanity while debugging\n\t\t\tObject.defineProperties(this, {\n\t\t\t\tprevious: { writable: true, value: null },\n\t\t\t\tnext: { writable: true, value: null },\n\t\t\t});\n\t\t} else {\n\t\t\tthis.previous = null;\n\t\t\tthis.next = null;\n\t\t}\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\treset() {\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\t\tif (this.edited) {\n\t\t\tthis.content = this.original;\n\t\t\tthis.storeName = false;\n\t\t\tthis.edited = false;\n\t\t}\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// after split we should save the edit content record into the correct chunk\n\t\t\t// to make sure sourcemap correct\n\t\t\t// For example:\n\t\t\t// '  test'.trim()\n\t\t\t//     split   -> '  ' + 'test'\n\t\t\t//   ✔️ edit    -> '' + 'test'\n\t\t\t//   ✖️ edit    -> 'test' + '' \n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tthis.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tconst newChunk = this.split(this.end - trimmed.length);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tnewChunk.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "import { encode } from '@jridgewell/sourcemap-codec';\n\nfunction getBtoa() {\n\tif (typeof globalThis !== 'undefined' && typeof globalThis.btoa === 'function') {\n\t\treturn (str) => globalThis.btoa(unescape(encodeURIComponent(str)));\n\t} else if (typeof Buffer === 'function') {\n\t\treturn (str) => Buffer.from(str, 'utf-8').toString('base64');\n\t} else {\n\t\treturn () => {\n\t\t\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n\t\t};\n\t}\n}\n\nconst btoa = /*#__PURE__*/ getBtoa();\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t\tif (typeof properties.x_google_ignoreList !== 'undefined') {\n\t\t\tthis.x_google_ignoreList = properties.x_google_ignoreList;\n\t\t}\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter((line) => /^\\t+/.test(line));\n\tconst spaced = lines.filter((line) => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "const wordRegex = /\\w/;\n\nexport default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst contentLengthMinusOne = content.length - 1;\n\t\t\tlet contentLineEnd = content.indexOf('\\n', 0);\n\t\t\tlet previousContentLineEnd = -1;\n\t\t\t// Loop through each line in the content and add a segment, but stop if the last line is empty,\n\t\t\t// else code afterwards would fill one line too many\n\t\t\twhile (contentLineEnd >= 0 && contentLengthMinusOne > contentLineEnd) {\n\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\t\tif (nameIndex >= 0) {\n\t\t\t\t\tsegment.push(nameIndex);\n\t\t\t\t}\n\t\t\t\tthis.rawSegments.push(segment);\n\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\n\t\t\t\tpreviousContentLineEnd = contentLineEnd;\n\t\t\t\tcontentLineEnd = content.indexOf('\\n', contentLineEnd + 1);\n\t\t\t}\n\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\n\t\t\tthis.advance(content.slice(previousContentLineEnd + 1));\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t\tthis.advance(content);\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\t\t// when iterating each char, check if it's in a word boundary\n\t\tlet charInHiresBoundary = false;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\n\t\t\t\tif (this.hires === 'boundary') {\n\t\t\t\t\t// in hires \"boundary\", group segments per word boundary than per char\n\t\t\t\t\tif (wordRegex.test(original[originalCharIndex])) {\n\t\t\t\t\t\t// for first char in the boundary found, start the boundary by pushing a segment\n\t\t\t\t\t\tif (!charInHiresBoundary) {\n\t\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\t\tcharInHiresBoundary = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// for non-word char, end the boundary by pushing a segment\n\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\tcharInHiresBoundary = false;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t} else {\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false,\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal: { writable: true, value: string },\n\t\t\toutro: { writable: true, value: '' },\n\t\t\tintro: { writable: true, value: '' },\n\t\t\tfirstChunk: { writable: true, value: chunk },\n\t\t\tlastChunk: { writable: true, value: chunk },\n\t\t\tlastSearchedChunk: { writable: true, value: chunk },\n\t\t\tbyStart: { writable: true, value: {} },\n\t\t\tbyEnd: { writable: true, value: {} },\n\t\t\tfilename: { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations: { writable: true, value: new BitSet() },\n\t\t\tstoredNames: { writable: true, value: {} },\n\t\t\tindentStr: { writable: true, value: undefined },\n\t\t\tignoreList: { writable: true, value: options.ignoreList },\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext((chunk) => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: [\n\t\t\t\toptions.source ? getRelativePath(options.file || '', options.source) : options.file || '',\n\t\t\t],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : undefined,\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList: this.ignoreList ? [sourceIndex] : undefined,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\t_ensureindentStr() {\n\t\tif (this.indentStr === undefined) {\n\t\t\tthis.indentStr = guessIndent(this.original);\n\t\t}\n\t}\n\n\t_getRawIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr;\n\t}\n\n\tgetIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tif (indentStr === undefined) {\n\t\t\tthis._ensureindentStr();\n\t\t\tindentStr = this.indentStr || '\\t';\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach((exclusion) => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = (match) => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error(\n\t\t\t'magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)',\n\t\t);\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead',\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead',\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\toptions = options || {};\n\t\treturn this.update(start, end, content, { ...options, overwrite: !options.contentOnly });\n\t}\n\n\tupdate(start, end, content, options) {\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot overwrite a zero-length range – use appendLeft or prependRight instead',\n\t\t\t);\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string',\n\t\t\t\t); // eslint-disable-line no-console\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst overwrite = options !== undefined ? options.overwrite : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tObject.defineProperty(this.storedNames, original, {\n\t\t\t\twritable: true,\n\t\t\t\tvalue: true,\n\t\t\t\tenumerable: true,\n\t\t\t});\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tlet chunk = first;\n\t\t\twhile (chunk !== last) {\n\t\t\t\tif (chunk.next !== this.byStart[chunk.end]) {\n\t\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t\t}\n\t\t\t\tchunk = chunk.next;\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, !overwrite);\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\treset(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('reset');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.reset();\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('reset');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length) return this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length) return chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length) return chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length) return chunk.intro[chunk.intro.length - 1];\n\t\t} while ((chunk = chunk.previous));\n\t\tif (this.intro.length) return this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while ((chunk = chunk.previous));\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${chunk.original}\")`,\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (\n\t\t\t\t(chunk.intro.length && chunk.intro.trim()) ||\n\t\t\t\t(chunk.content.length && chunk.content.trim()) ||\n\t\t\t\t(chunk.outro.length && chunk.outro.trim())\n\t\t\t)\n\t\t\t\treturn false;\n\t\t} while ((chunk = chunk.next));\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while ((chunk = chunk.next));\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n\n\thasChanged() {\n\t\treturn this.original !== this.toString();\n\t}\n\n\t_replaceRegexp(searchValue, replacement) {\n\t\tfunction getReplacement(match, str) {\n\t\t\tif (typeof replacement === 'string') {\n\t\t\t\treturn replacement.replace(/\\$(\\$|&|\\d+)/g, (_, i) => {\n\t\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#specifying_a_string_as_a_parameter\n\t\t\t\t\tif (i === '$') return '$';\n\t\t\t\t\tif (i === '&') return match[0];\n\t\t\t\t\tconst num = +i;\n\t\t\t\t\tif (num < match.length) return match[+i];\n\t\t\t\t\treturn `$${i}`;\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\treturn replacement(...match, match.index, str, match.groups);\n\t\t\t}\n\t\t}\n\t\tfunction matchAll(re, str) {\n\t\t\tlet match;\n\t\t\tconst matches = [];\n\t\t\twhile ((match = re.exec(str))) {\n\t\t\t\tmatches.push(match);\n\t\t\t}\n\t\t\treturn matches;\n\t\t}\n\t\tif (searchValue.global) {\n\t\t\tconst matches = matchAll(searchValue, this.original);\n\t\t\tmatches.forEach((match) => {\n\t\t\t\tif (match.index != null) {\n\t\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\t\tthis.overwrite(\n\t\t\t\t\t\t\tmatch.index,\n\t\t\t\t\t\t\tmatch.index + match[0].length,\n\t\t\t\t\t\t\treplacement\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tconst match = this.original.match(searchValue);\n\t\t\tif (match && match.index != null) {\n\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\tthis.overwrite(\n\t\t\t\t\t\tmatch.index,\n\t\t\t\t\t\tmatch.index + match[0].length,\n\t\t\t\t\t\treplacement\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\t_replaceString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst index = original.indexOf(string);\n\n\t\tif (index !== -1) {\n\t\t\tthis.overwrite(index, index + string.length, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplace(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceString(searchValue, replacement);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n\n\t_replaceAllString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst stringLength = string.length;\n\t\tfor (\n\t\t\tlet index = original.indexOf(string);\n\t\t\tindex !== -1;\n\t\t\tindex = original.indexOf(string, index + stringLength)\n\t\t) {\n\t\t\tconst previous = original.slice(index, index + stringLength);\n\t\t\tif (previous !== replacement)\n\t\t\t\tthis.overwrite(index, index + stringLength, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplaceAll(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceAllString(searchValue, replacement);\n\t\t}\n\n\t\tif (!searchValue.global) {\n\t\t\tthrow new TypeError(\n\t\t\t\t'MagicString.prototype.replaceAll called with a non-global RegExp argument',\n\t\t\t);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator,\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error(\n\t\t\t\t'bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`',\n\t\t\t);\n\t\t}\n\n\t\t['filename', 'ignoreList', 'indentExclusionRanges', 'separator'].forEach((option) => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || '',\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator,\n\t\t});\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator,\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tlet x_google_ignoreList = undefined;\n\t\tthis.sources.forEach((source) => {\n\t\t\tObject.keys(source.content.storedNames).forEach((name) => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext((chunk) => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\n\t\t\tif (source.ignoreList && sourceIndex !== -1) {\n\t\t\t\tif (x_google_ignoreList === undefined) {\n\t\t\t\t\tx_google_ignoreList = [];\n\t\t\t\t}\n\t\t\t\tx_google_ignoreList.push(sourceIndex);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tconst indentStr = source.content._getRawIndentString();\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart, //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty() {\n\t\tif (this.intro.length && this.intro.trim()) return false;\n\t\tif (this.sources.some((source) => !source.content.isEmpty())) return false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce(\n\t\t\t(length, source) => length + source.content.length(),\n\t\t\tthis.intro.length,\n\t\t);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n"], "mappings": ";;;AAOA,IAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,IAAM,YAAY,IAAI,WAAW,CAAC;AAClC,IAAM,QAAQ;AACd,IAAM,YAAY,IAAI,WAAW,EAAE;AACnC,IAAM,YAAY,IAAI,WAAW,GAAG;AAEpC,SAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAM,IAAI,MAAM,WAAW,CAAC;AAC5B,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;;AAIjB,IAAM,KACJ,OAAO,gBAAgB,cACH,oBAAI,YAAW,IAC/B,OAAO,WAAW,cAClB;EACE,OAAO,KAAe;AACpB,UAAM,MAAM,OAAO,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,WAAO,IAAI,SAAQ;;IAGvB;EACE,OAAO,KAAe;AACpB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,aAAO,OAAO,aAAa,IAAI,CAAC,CAAC;;AAEnC,WAAO;;;SA6FD,OAAO,SAAoC;AACzD,QAAM,QAAkD,IAAI,WAAW,CAAC;AACxE,QAAM,YAAY,OAAO;AACzB,QAAM,YAAY,YAAY;AAC9B,QAAM,MAAM,IAAI,WAAW,SAAS;AACpC,QAAM,MAAM,IAAI,SAAS,GAAG,SAAS;AACrC,MAAI,MAAM;AACV,MAAI,MAAM;AAEV,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,OAAO,QAAQ,CAAC;AACtB,QAAI,IAAI,GAAG;AACT,UAAI,QAAQ,WAAW;AACrB,eAAO,GAAG,OAAO,GAAG;AACpB,cAAM;;AAER,UAAI,KAAK,IAAI;;AAEf,QAAI,KAAK,WAAW;AAAG;AAEvB,UAAM,CAAC,IAAI;AAEX,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,UAAU,KAAK,CAAC;AAGtB,UAAI,MAAM,WAAW;AACnB,eAAO,GAAG,OAAO,GAAG;AACpB,YAAI,WAAW,GAAG,WAAW,GAAG;AAChC,eAAO;;AAET,UAAI,IAAI;AAAG,YAAI,KAAK,IAAI;AAExB,YAAM,cAAc,KAAK,KAAK,OAAO,SAAS,CAAC;AAE/C,UAAI,QAAQ,WAAW;AAAG;AAC1B,YAAM,cAAc,KAAK,KAAK,OAAO,SAAS,CAAC;AAC/C,YAAM,cAAc,KAAK,KAAK,OAAO,SAAS,CAAC;AAC/C,YAAM,cAAc,KAAK,KAAK,OAAO,SAAS,CAAC;AAE/C,UAAI,QAAQ,WAAW;AAAG;AAC1B,YAAM,cAAc,KAAK,KAAK,OAAO,SAAS,CAAC;;;AAInD,SAAO,MAAM,GAAG,OAAO,IAAI,SAAS,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,cACP,KACA,KACA,OACA,SACA,GAAS;AAET,QAAM,OAAO,QAAQ,CAAC;AACtB,MAAI,MAAM,OAAO,MAAM,CAAC;AACxB,QAAM,CAAC,IAAI;AAEX,QAAM,MAAM,IAAK,CAAC,OAAO,IAAK,IAAI,OAAO;AACzC,KAAG;AACD,QAAI,UAAU,MAAM;AACpB,aAAS;AACT,QAAI,MAAM;AAAG,iBAAW;AACxB,QAAI,KAAK,IAAI,UAAU,OAAO;WACvB,MAAM;AAEf,SAAO;AACT;;;ACrMe,IAAM,SAAN,MAAM,QAAO;EAC3B,YAAY,KAAK;AAChB,SAAK,OAAO,eAAe,UAAS,IAAI,KAAK,MAAK,IAAK,CAAA;EACzD;EAEC,IAAIA,IAAG;AACN,SAAK,KAAKA,MAAK,CAAC,KAAK,MAAMA,KAAI;EACjC;EAEC,IAAIA,IAAG;AACN,WAAO,CAAC,EAAE,KAAK,KAAKA,MAAK,CAAC,IAAK,MAAMA,KAAI;EAC3C;AACA;ACZe,IAAM,QAAN,MAAM,OAAM;EAC1B,YAAY,OAAO,KAAK,SAAS;AAChC,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAEhB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAEb,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AAQP;AACN,WAAK,WAAW;AAChB,WAAK,OAAO;IACf;EACA;EAEC,WAAW,SAAS;AACnB,SAAK,SAAS;EAChB;EAEC,YAAY,SAAS;AACpB,SAAK,QAAQ,KAAK,QAAQ;EAC5B;EAEC,QAAQ;AACP,UAAM,QAAQ,IAAI,OAAM,KAAK,OAAO,KAAK,KAAK,KAAK,QAAQ;AAE3D,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU,KAAK;AACrB,UAAM,YAAY,KAAK;AACvB,UAAM,SAAS,KAAK;AAEpB,WAAO;EACT;EAEC,SAAS,OAAO;AACf,WAAO,KAAK,QAAQ,SAAS,QAAQ,KAAK;EAC5C;EAEC,SAAS,IAAI;AACZ,QAAI,QAAQ;AACZ,WAAO,OAAO;AACb,SAAG,KAAK;AACR,cAAQ,MAAM;IACjB;EACA;EAEC,aAAa,IAAI;AAChB,QAAI,QAAQ;AACZ,WAAO,OAAO;AACb,SAAG,KAAK;AACR,cAAQ,MAAM;IACjB;EACA;EAEC,KAAK,SAAS,WAAW,aAAa;AACrC,SAAK,UAAU;AACf,QAAI,CAAC,aAAa;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;IAChB;AACE,SAAK,YAAY;AAEjB,SAAK,SAAS;AAEd,WAAO;EACT;EAEC,YAAY,SAAS;AACpB,SAAK,QAAQ,UAAU,KAAK;EAC9B;EAEC,aAAa,SAAS;AACrB,SAAK,QAAQ,UAAU,KAAK;EAC9B;EAEC,QAAQ;AACP,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,QAAI,KAAK,QAAQ;AAChB,WAAK,UAAU,KAAK;AACpB,WAAK,YAAY;AACjB,WAAK,SAAS;IACjB;EACA;EAEC,MAAM,OAAO;AACZ,UAAM,aAAa,QAAQ,KAAK;AAEhC,UAAM,iBAAiB,KAAK,SAAS,MAAM,GAAG,UAAU;AACxD,UAAM,gBAAgB,KAAK,SAAS,MAAM,UAAU;AAEpD,SAAK,WAAW;AAEhB,UAAM,WAAW,IAAI,OAAM,OAAO,KAAK,KAAK,aAAa;AACzD,aAAS,QAAQ,KAAK;AACtB,SAAK,QAAQ;AAEb,SAAK,MAAM;AAEX,QAAI,KAAK,QAAQ;AAShB,eAAS,KAAK,IAAI,KAAK;AACvB,WAAK,UAAU;IAClB,OAAS;AACN,WAAK,UAAU;IAClB;AAEE,aAAS,OAAO,KAAK;AACrB,QAAI,SAAS;AAAM,eAAS,KAAK,WAAW;AAC5C,aAAS,WAAW;AACpB,SAAK,OAAO;AAEZ,WAAO;EACT;EAEC,WAAW;AACV,WAAO,KAAK,QAAQ,KAAK,UAAU,KAAK;EAC1C;EAEC,QAAQ,IAAI;AACX,SAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,QAAI,KAAK,MAAM;AAAQ,aAAO;AAE9B,UAAM,UAAU,KAAK,QAAQ,QAAQ,IAAI,EAAE;AAE3C,QAAI,QAAQ,QAAQ;AACnB,UAAI,YAAY,KAAK,SAAS;AAC7B,aAAK,MAAM,KAAK,QAAQ,QAAQ,MAAM,EAAE,KAAK,IAAI,QAAW,IAAI;AAChE,YAAI,KAAK,QAAQ;AAEhB,eAAK,KAAK,SAAS,KAAK,WAAW,IAAI;QAC5C;MACA;AACG,aAAO;IACV,OAAS;AACN,WAAK,KAAK,IAAI,QAAW,IAAI;AAE7B,WAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,UAAI,KAAK,MAAM;AAAQ,eAAO;IACjC;EACA;EAEC,UAAU,IAAI;AACb,SAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,QAAI,KAAK,MAAM;AAAQ,aAAO;AAE9B,UAAM,UAAU,KAAK,QAAQ,QAAQ,IAAI,EAAE;AAE3C,QAAI,QAAQ,QAAQ;AACnB,UAAI,YAAY,KAAK,SAAS;AAC7B,cAAM,WAAW,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM;AACrD,YAAI,KAAK,QAAQ;AAEhB,mBAAS,KAAK,SAAS,KAAK,WAAW,IAAI;QAChD;AACI,aAAK,KAAK,IAAI,QAAW,IAAI;MACjC;AACG,aAAO;IACV,OAAS;AACN,WAAK,KAAK,IAAI,QAAW,IAAI;AAE7B,WAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,UAAI,KAAK,MAAM;AAAQ,eAAO;IACjC;EACA;AACA;ACrLA,SAAS,UAAU;AAClB,MAAI,OAAO,eAAe,eAAe,OAAO,WAAW,SAAS,YAAY;AAC/E,WAAO,CAAC,QAAQ,WAAW,KAAK,SAAS,mBAAmB,GAAG,CAAC,CAAC;EACnE,WAAY,OAAO,WAAW,YAAY;AACxC,WAAO,CAAC,QAAQ,OAAO,KAAK,KAAK,OAAO,EAAE,SAAS,QAAQ;EAC7D,OAAQ;AACN,WAAO,MAAM;AACZ,YAAM,IAAI,MAAM,yEAAyE;IAC5F;EACA;AACA;AAEA,IAAM,OAAqB,wBAAO;AAEnB,IAAM,YAAN,MAAgB;EAC9B,YAAY,YAAY;AACvB,SAAK,UAAU;AACf,SAAK,OAAO,WAAW;AACvB,SAAK,UAAU,WAAW;AAC1B,SAAK,iBAAiB,WAAW;AACjC,SAAK,QAAQ,WAAW;AACxB,SAAK,WAAW,OAAO,WAAW,QAAQ;AAC1C,QAAI,OAAO,WAAW,wBAAwB,aAAa;AAC1D,WAAK,sBAAsB,WAAW;IACzC;EACA;EAEC,WAAW;AACV,WAAO,KAAK,UAAU,IAAI;EAC5B;EAEC,QAAQ;AACP,WAAO,gDAAgD,KAAK,KAAK,SAAQ,CAAE;EAC7E;AACA;ACpCe,SAAS,YAAY,MAAM;AACzC,QAAM,QAAQ,KAAK,MAAM,IAAI;AAE7B,QAAM,SAAS,MAAM,OAAO,CAAC,SAAS,OAAO,KAAK,IAAI,CAAC;AACvD,QAAM,SAAS,MAAM,OAAO,CAAC,SAAS,SAAS,KAAK,IAAI,CAAC;AAEzD,MAAI,OAAO,WAAW,KAAK,OAAO,WAAW,GAAG;AAC/C,WAAO;EACT;AAKC,MAAI,OAAO,UAAU,OAAO,QAAQ;AACnC,WAAO;EACT;AAGC,QAAM,MAAM,OAAO,OAAO,CAAC,UAAU,YAAY;AAChD,UAAM,YAAY,MAAM,KAAK,OAAO,EAAE,CAAC,EAAE;AACzC,WAAO,KAAK,IAAI,WAAW,QAAQ;EACrC,GAAI,QAAQ;AAEX,SAAO,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AACnC;ACxBe,SAAS,gBAAgB,MAAM,IAAI;AACjD,QAAM,YAAY,KAAK,MAAM,OAAO;AACpC,QAAM,UAAU,GAAG,MAAM,OAAO;AAEhC,YAAU,IAAG;AAEb,SAAO,UAAU,CAAC,MAAM,QAAQ,CAAC,GAAG;AACnC,cAAU,MAAK;AACf,YAAQ,MAAK;EACf;AAEC,MAAI,UAAU,QAAQ;AACrB,QAAI,IAAI,UAAU;AAClB,WAAO;AAAK,gBAAU,CAAC,IAAI;EAC7B;AAEC,SAAO,UAAU,OAAO,OAAO,EAAE,KAAK,GAAG;AAC1C;ACjBA,IAAM,WAAW,OAAO,UAAU;AAEnB,SAAS,SAAS,OAAO;AACvC,SAAO,SAAS,KAAK,KAAK,MAAM;AACjC;ACJe,SAAS,WAAW,QAAQ;AAC1C,QAAM,gBAAgB,OAAO,MAAM,IAAI;AACvC,QAAM,cAAc,CAAA;AAEpB,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,cAAc,QAAQ,KAAK;AACvD,gBAAY,KAAK,GAAG;AACpB,WAAO,cAAc,CAAC,EAAE,SAAS;EACnC;AAEC,SAAO,SAAS,OAAO,OAAO;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI,YAAY;AACpB,WAAO,IAAI,GAAG;AACb,YAAM,IAAK,IAAI,KAAM;AACrB,UAAI,QAAQ,YAAY,CAAC,GAAG;AAC3B,YAAI;MACR,OAAU;AACN,YAAI,IAAI;MACZ;IACA;AACE,UAAM,OAAO,IAAI;AACjB,UAAM,SAAS,QAAQ,YAAY,IAAI;AACvC,WAAO,EAAE,MAAM,OAAM;EACvB;AACA;ACxBA,IAAM,YAAY;AAEH,IAAM,WAAN,MAAe;EAC7B,YAAY,OAAO;AAClB,SAAK,QAAQ;AACb,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B,SAAK,MAAM,CAAA;AACX,SAAK,cAAc,KAAK,IAAI,KAAK,iBAAiB,IAAI,CAAA;AACtD,SAAK,UAAU;EACjB;EAEC,QAAQ,aAAa,SAAS,KAAK,WAAW;AAC7C,QAAI,QAAQ,QAAQ;AACnB,YAAM,wBAAwB,QAAQ,SAAS;AAC/C,UAAI,iBAAiB,QAAQ,QAAQ,MAAM,CAAC;AAC5C,UAAI,yBAAyB;AAG7B,aAAO,kBAAkB,KAAK,wBAAwB,gBAAgB;AACrE,cAAMC,WAAU,CAAC,KAAK,qBAAqB,aAAa,IAAI,MAAM,IAAI,MAAM;AAC5E,YAAI,aAAa,GAAG;AACnB,UAAAA,SAAQ,KAAK,SAAS;QAC3B;AACI,aAAK,YAAY,KAAKA,QAAO;AAE7B,aAAK,qBAAqB;AAC1B,aAAK,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,CAAA;AACtD,aAAK,sBAAsB;AAE3B,iCAAyB;AACzB,yBAAiB,QAAQ,QAAQ,MAAM,iBAAiB,CAAC;MAC7D;AAEG,YAAM,UAAU,CAAC,KAAK,qBAAqB,aAAa,IAAI,MAAM,IAAI,MAAM;AAC5E,UAAI,aAAa,GAAG;AACnB,gBAAQ,KAAK,SAAS;MAC1B;AACG,WAAK,YAAY,KAAK,OAAO;AAE7B,WAAK,QAAQ,QAAQ,MAAM,yBAAyB,CAAC,CAAC;IACzD,WAAa,KAAK,SAAS;AACxB,WAAK,YAAY,KAAK,KAAK,OAAO;AAClC,WAAK,QAAQ,OAAO;IACvB;AAEE,SAAK,UAAU;EACjB;EAEC,iBAAiB,aAAa,OAAO,UAAU,KAAK,oBAAoB;AACvE,QAAI,oBAAoB,MAAM;AAC9B,QAAI,QAAQ;AAEZ,QAAI,sBAAsB;AAE1B,WAAO,oBAAoB,MAAM,KAAK;AACrC,UAAI,KAAK,SAAS,SAAS,mBAAmB,IAAI,iBAAiB,GAAG;AACrE,cAAM,UAAU,CAAC,KAAK,qBAAqB,aAAa,IAAI,MAAM,IAAI,MAAM;AAE5E,YAAI,KAAK,UAAU,YAAY;AAE9B,cAAI,UAAU,KAAK,SAAS,iBAAiB,CAAC,GAAG;AAEhD,gBAAI,CAAC,qBAAqB;AACzB,mBAAK,YAAY,KAAK,OAAO;AAC7B,oCAAsB;YAC7B;UACA,OAAY;AAEN,iBAAK,YAAY,KAAK,OAAO;AAC7B,kCAAsB;UAC5B;QACA,OAAW;AACN,eAAK,YAAY,KAAK,OAAO;QAClC;MACA;AAEG,UAAI,SAAS,iBAAiB,MAAM,MAAM;AACzC,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,aAAK,qBAAqB;AAC1B,aAAK,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,CAAA;AACtD,aAAK,sBAAsB;AAC3B,gBAAQ;MACZ,OAAU;AACN,YAAI,UAAU;AACd,aAAK,uBAAuB;AAC5B,gBAAQ;MACZ;AAEG,2BAAqB;IACxB;AAEE,SAAK,UAAU;EACjB;EAEC,QAAQ,KAAK;AACZ,QAAI,CAAC;AAAK;AAEV,UAAM,QAAQ,IAAI,MAAM,IAAI;AAE5B,QAAI,MAAM,SAAS,GAAG;AACrB,eAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AAC1C,aAAK;AACL,aAAK,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,CAAA;MAC1D;AACG,WAAK,sBAAsB;IAC9B;AAEE,SAAK,uBAAuB,MAAM,MAAM,SAAS,CAAC,EAAE;EACtD;AACA;ACrGA,IAAM,IAAI;AAEV,IAAM,SAAS;EACd,YAAY;EACZ,aAAa;EACb,WAAW;AACZ;AAEe,IAAM,cAAN,MAAM,aAAY;EAChC,YAAY,QAAQ,UAAU,CAAA,GAAI;AACjC,UAAM,QAAQ,IAAI,MAAM,GAAG,OAAO,QAAQ,MAAM;AAEhD,WAAO,iBAAiB,MAAM;MAC7B,UAAU,EAAE,UAAU,MAAM,OAAO,OAAM;MACzC,OAAO,EAAE,UAAU,MAAM,OAAO,GAAE;MAClC,OAAO,EAAE,UAAU,MAAM,OAAO,GAAE;MAClC,YAAY,EAAE,UAAU,MAAM,OAAO,MAAK;MAC1C,WAAW,EAAE,UAAU,MAAM,OAAO,MAAK;MACzC,mBAAmB,EAAE,UAAU,MAAM,OAAO,MAAK;MACjD,SAAS,EAAE,UAAU,MAAM,OAAO,CAAA,EAAE;MACpC,OAAO,EAAE,UAAU,MAAM,OAAO,CAAA,EAAE;MAClC,UAAU,EAAE,UAAU,MAAM,OAAO,QAAQ,SAAQ;MACnD,uBAAuB,EAAE,UAAU,MAAM,OAAO,QAAQ,sBAAqB;MAC7E,oBAAoB,EAAE,UAAU,MAAM,OAAO,IAAI,OAAM,EAAE;MACzD,aAAa,EAAE,UAAU,MAAM,OAAO,CAAA,EAAE;MACxC,WAAW,EAAE,UAAU,MAAM,OAAO,OAAS;MAC7C,YAAY,EAAE,UAAU,MAAM,OAAO,QAAQ,WAAU;IAC1D,CAAG;AAMD,SAAK,QAAQ,CAAC,IAAI;AAClB,SAAK,MAAM,OAAO,MAAM,IAAI;EAC9B;EAEC,qBAAqB,MAAM;AAC1B,SAAK,mBAAmB,IAAI,IAAI;EAClC;EAEC,OAAO,SAAS;AACf,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,gCAAgC;AAErF,SAAK,SAAS;AACd,WAAO;EACT;EAEC,WAAW,OAAO,SAAS;AAC1B,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,mCAAmC;AAIxF,SAAK,OAAO,KAAK;AAEjB,UAAM,QAAQ,KAAK,MAAM,KAAK;AAE9B,QAAI,OAAO;AACV,YAAM,WAAW,OAAO;IAC3B,OAAS;AACN,WAAK,SAAS;IACjB;AAGE,WAAO;EACT;EAEC,YAAY,OAAO,SAAS;AAC3B,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,mCAAmC;AAIxF,SAAK,OAAO,KAAK;AAEjB,UAAM,QAAQ,KAAK,QAAQ,KAAK;AAEhC,QAAI,OAAO;AACV,YAAM,YAAY,OAAO;IAC5B,OAAS;AACN,WAAK,SAAS;IACjB;AAGE,WAAO;EACT;EAEC,QAAQ;AACP,UAAM,SAAS,IAAI,aAAY,KAAK,UAAU,EAAE,UAAU,KAAK,SAAQ,CAAE;AAEzE,QAAI,gBAAgB,KAAK;AACzB,QAAI,cAAe,OAAO,aAAa,OAAO,oBAAoB,cAAc,MAAK;AAErF,WAAO,eAAe;AACrB,aAAO,QAAQ,YAAY,KAAK,IAAI;AACpC,aAAO,MAAM,YAAY,GAAG,IAAI;AAEhC,YAAM,oBAAoB,cAAc;AACxC,YAAM,kBAAkB,qBAAqB,kBAAkB,MAAK;AAEpE,UAAI,iBAAiB;AACpB,oBAAY,OAAO;AACnB,wBAAgB,WAAW;AAE3B,sBAAc;MAClB;AAEG,sBAAgB;IACnB;AAEE,WAAO,YAAY;AAEnB,QAAI,KAAK,uBAAuB;AAC/B,aAAO,wBAAwB,KAAK,sBAAsB,MAAK;IAClE;AAEE,WAAO,qBAAqB,IAAI,OAAO,KAAK,kBAAkB;AAE9D,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,KAAK;AAEpB,WAAO;EACT;EAEC,mBAAmB,SAAS;AAC3B,cAAU,WAAW,CAAA;AAErB,UAAM,cAAc;AACpB,UAAM,QAAQ,OAAO,KAAK,KAAK,WAAW;AAC1C,UAAM,WAAW,IAAI,SAAS,QAAQ,KAAK;AAE3C,UAAM,SAAS,WAAW,KAAK,QAAQ;AAEvC,QAAI,KAAK,OAAO;AACf,eAAS,QAAQ,KAAK,KAAK;IAC9B;AAEE,SAAK,WAAW,SAAS,CAAC,UAAU;AACnC,YAAM,MAAM,OAAO,MAAM,KAAK;AAE9B,UAAI,MAAM,MAAM;AAAQ,iBAAS,QAAQ,MAAM,KAAK;AAEpD,UAAI,MAAM,QAAQ;AACjB,iBAAS;UACR;UACA,MAAM;UACN;UACA,MAAM,YAAY,MAAM,QAAQ,MAAM,QAAQ,IAAI;QACvD;MACA,OAAU;AACN,iBAAS,iBAAiB,aAAa,OAAO,KAAK,UAAU,KAAK,KAAK,kBAAkB;MAC7F;AAEG,UAAI,MAAM,MAAM;AAAQ,iBAAS,QAAQ,MAAM,KAAK;IACvD,CAAG;AAED,WAAO;MACN,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM,OAAO,EAAE,IAAG,IAAK;MACzD,SAAS;QACR,QAAQ,SAAS,gBAAgB,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,QAAQ;MAC3F;MACG,gBAAgB,QAAQ,iBAAiB,CAAC,KAAK,QAAQ,IAAI;MAC3D;MACA,UAAU,SAAS;MACnB,qBAAqB,KAAK,aAAa,CAAC,WAAW,IAAI;IAC1D;EACA;EAEC,YAAY,SAAS;AACpB,WAAO,IAAI,UAAU,KAAK,mBAAmB,OAAO,CAAC;EACvD;EAEC,mBAAmB;AAClB,QAAI,KAAK,cAAc,QAAW;AACjC,WAAK,YAAY,YAAY,KAAK,QAAQ;IAC7C;EACA;EAEC,sBAAsB;AACrB,SAAK,iBAAgB;AACrB,WAAO,KAAK;EACd;EAEC,kBAAkB;AACjB,SAAK,iBAAgB;AACrB,WAAO,KAAK,cAAc,OAAO,MAAO,KAAK;EAC/C;EAEC,OAAO,WAAW,SAAS;AAC1B,UAAM,UAAU;AAEhB,QAAI,SAAS,SAAS,GAAG;AACxB,gBAAU;AACV,kBAAY;IACf;AAEE,QAAI,cAAc,QAAW;AAC5B,WAAK,iBAAgB;AACrB,kBAAY,KAAK,aAAa;IACjC;AAEE,QAAI,cAAc;AAAI,aAAO;AAE7B,cAAU,WAAW,CAAA;AAGrB,UAAM,aAAa,CAAA;AAEnB,QAAI,QAAQ,SAAS;AACpB,YAAM,aACL,OAAO,QAAQ,QAAQ,CAAC,MAAM,WAAW,CAAC,QAAQ,OAAO,IAAI,QAAQ;AACtE,iBAAW,QAAQ,CAAC,cAAc;AACjC,iBAAS,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,KAAK,GAAG;AACpD,qBAAW,CAAC,IAAI;QACrB;MACA,CAAI;IACJ;AAEE,QAAI,4BAA4B,QAAQ,gBAAgB;AACxD,UAAM,WAAW,CAAC,UAAU;AAC3B,UAAI;AAA2B,eAAO,GAAG,SAAS,GAAG,KAAK;AAC1D,kCAA4B;AAC5B,aAAO;IACV;AAEE,SAAK,QAAQ,KAAK,MAAM,QAAQ,SAAS,QAAQ;AAEjD,QAAI,YAAY;AAChB,QAAI,QAAQ,KAAK;AAEjB,WAAO,OAAO;AACb,YAAM,MAAM,MAAM;AAElB,UAAI,MAAM,QAAQ;AACjB,YAAI,CAAC,WAAW,SAAS,GAAG;AAC3B,gBAAM,UAAU,MAAM,QAAQ,QAAQ,SAAS,QAAQ;AAEvD,cAAI,MAAM,QAAQ,QAAQ;AACzB,wCAA4B,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,MAAM;UAC9E;QACA;MACA,OAAU;AACN,oBAAY,MAAM;AAElB,eAAO,YAAY,KAAK;AACvB,cAAI,CAAC,WAAW,SAAS,GAAG;AAC3B,kBAAM,OAAO,KAAK,SAAS,SAAS;AAEpC,gBAAI,SAAS,MAAM;AAClB,0CAA4B;YACnC,WAAiB,SAAS,QAAQ,2BAA2B;AACtD,0CAA4B;AAE5B,kBAAI,cAAc,MAAM,OAAO;AAC9B,sBAAM,aAAa,SAAS;cACpC,OAAc;AACN,qBAAK,YAAY,OAAO,SAAS;AACjC,wBAAQ,MAAM;AACd,sBAAM,aAAa,SAAS;cACpC;YACA;UACA;AAEK,uBAAa;QAClB;MACA;AAEG,kBAAY,MAAM;AAClB,cAAQ,MAAM;IACjB;AAEE,SAAK,QAAQ,KAAK,MAAM,QAAQ,SAAS,QAAQ;AAEjD,WAAO;EACT;EAEC,SAAS;AACR,UAAM,IAAI;MACT;IACH;EACA;EAEC,WAAW,OAAO,SAAS;AAC1B,QAAI,CAAC,OAAO,YAAY;AACvB,cAAQ;QACP;MACJ;AACG,aAAO,aAAa;IACvB;AAEE,WAAO,KAAK,WAAW,OAAO,OAAO;EACvC;EAEC,YAAY,OAAO,SAAS;AAC3B,QAAI,CAAC,OAAO,aAAa;AACxB,cAAQ;QACP;MACJ;AACG,aAAO,cAAc;IACxB;AAEE,WAAO,KAAK,aAAa,OAAO,OAAO;EACzC;EAEC,KAAK,OAAO,KAAK,OAAO;AACvB,QAAI,SAAS,SAAS,SAAS;AAAK,YAAM,IAAI,MAAM,uCAAuC;AAI3F,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,GAAG;AACf,SAAK,OAAO,KAAK;AAEjB,UAAM,QAAQ,KAAK,QAAQ,KAAK;AAChC,UAAM,OAAO,KAAK,MAAM,GAAG;AAE3B,UAAM,UAAU,MAAM;AACtB,UAAM,WAAW,KAAK;AAEtB,UAAM,WAAW,KAAK,QAAQ,KAAK;AACnC,QAAI,CAAC,YAAY,SAAS,KAAK;AAAW,aAAO;AACjD,UAAM,UAAU,WAAW,SAAS,WAAW,KAAK;AAEpD,QAAI;AAAS,cAAQ,OAAO;AAC5B,QAAI;AAAU,eAAS,WAAW;AAElC,QAAI;AAAS,cAAQ,OAAO;AAC5B,QAAI;AAAU,eAAS,WAAW;AAElC,QAAI,CAAC,MAAM;AAAU,WAAK,aAAa,KAAK;AAC5C,QAAI,CAAC,KAAK,MAAM;AACf,WAAK,YAAY,MAAM;AACvB,WAAK,UAAU,OAAO;IACzB;AAEE,UAAM,WAAW;AACjB,SAAK,OAAO,YAAY;AAExB,QAAI,CAAC;AAAS,WAAK,aAAa;AAChC,QAAI,CAAC;AAAU,WAAK,YAAY;AAGhC,WAAO;EACT;EAEC,UAAU,OAAO,KAAK,SAAS,SAAS;AACvC,cAAU,WAAW,CAAA;AACrB,WAAO,KAAK,OAAO,OAAO,KAAK,SAAS,EAAE,GAAG,SAAS,WAAW,CAAC,QAAQ,YAAW,CAAE;EACzF;EAEC,OAAO,OAAO,KAAK,SAAS,SAAS;AACpC,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,sCAAsC;AAE3F,WAAO,QAAQ;AAAG,eAAS,KAAK,SAAS;AACzC,WAAO,MAAM;AAAG,aAAO,KAAK,SAAS;AAErC,QAAI,MAAM,KAAK,SAAS;AAAQ,YAAM,IAAI,MAAM,sBAAsB;AACtE,QAAI,UAAU;AACb,YAAM,IAAI;QACT;MACJ;AAIE,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,GAAG;AAEf,QAAI,YAAY,MAAM;AACrB,UAAI,CAAC,OAAO,WAAW;AACtB,gBAAQ;UACP;QACL;AACI,eAAO,YAAY;MACvB;AAEG,gBAAU,EAAE,WAAW,KAAI;IAC9B;AACE,UAAM,YAAY,YAAY,SAAY,QAAQ,YAAY;AAC9D,UAAM,YAAY,YAAY,SAAY,QAAQ,YAAY;AAE9D,QAAI,WAAW;AACd,YAAM,WAAW,KAAK,SAAS,MAAM,OAAO,GAAG;AAC/C,aAAO,eAAe,KAAK,aAAa,UAAU;QACjD,UAAU;QACV,OAAO;QACP,YAAY;MAChB,CAAI;IACJ;AAEE,UAAM,QAAQ,KAAK,QAAQ,KAAK;AAChC,UAAM,OAAO,KAAK,MAAM,GAAG;AAE3B,QAAI,OAAO;AACV,UAAI,QAAQ;AACZ,aAAO,UAAU,MAAM;AACtB,YAAI,MAAM,SAAS,KAAK,QAAQ,MAAM,GAAG,GAAG;AAC3C,gBAAM,IAAI,MAAM,uCAAuC;QAC5D;AACI,gBAAQ,MAAM;AACd,cAAM,KAAK,IAAI,KAAK;MACxB;AAEG,YAAM,KAAK,SAAS,WAAW,CAAC,SAAS;IAC5C,OAAS;AAEN,YAAM,WAAW,IAAI,MAAM,OAAO,KAAK,EAAE,EAAE,KAAK,SAAS,SAAS;AAGlE,WAAK,OAAO;AACZ,eAAS,WAAW;IACvB;AAGE,WAAO;EACT;EAEC,QAAQ,SAAS;AAChB,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,gCAAgC;AAErF,SAAK,QAAQ,UAAU,KAAK;AAC5B,WAAO;EACT;EAEC,YAAY,OAAO,SAAS;AAC3B,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,mCAAmC;AAIxF,SAAK,OAAO,KAAK;AAEjB,UAAM,QAAQ,KAAK,MAAM,KAAK;AAE9B,QAAI,OAAO;AACV,YAAM,YAAY,OAAO;IAC5B,OAAS;AACN,WAAK,QAAQ,UAAU,KAAK;IAC/B;AAGE,WAAO;EACT;EAEC,aAAa,OAAO,SAAS;AAC5B,QAAI,OAAO,YAAY;AAAU,YAAM,IAAI,UAAU,mCAAmC;AAIxF,SAAK,OAAO,KAAK;AAEjB,UAAM,QAAQ,KAAK,QAAQ,KAAK;AAEhC,QAAI,OAAO;AACV,YAAM,aAAa,OAAO;IAC7B,OAAS;AACN,WAAK,QAAQ,UAAU,KAAK;IAC/B;AAGE,WAAO;EACT;EAEC,OAAO,OAAO,KAAK;AAClB,WAAO,QAAQ;AAAG,eAAS,KAAK,SAAS;AACzC,WAAO,MAAM;AAAG,aAAO,KAAK,SAAS;AAErC,QAAI,UAAU;AAAK,aAAO;AAE1B,QAAI,QAAQ,KAAK,MAAM,KAAK,SAAS;AAAQ,YAAM,IAAI,MAAM,4BAA4B;AACzF,QAAI,QAAQ;AAAK,YAAM,IAAI,MAAM,gCAAgC;AAIjE,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,GAAG;AAEf,QAAI,QAAQ,KAAK,QAAQ,KAAK;AAE9B,WAAO,OAAO;AACb,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAM,KAAK,EAAE;AAEb,cAAQ,MAAM,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,IAAI;IACvD;AAGE,WAAO;EACT;EAEC,MAAM,OAAO,KAAK;AACjB,WAAO,QAAQ;AAAG,eAAS,KAAK,SAAS;AACzC,WAAO,MAAM;AAAG,aAAO,KAAK,SAAS;AAErC,QAAI,UAAU;AAAK,aAAO;AAE1B,QAAI,QAAQ,KAAK,MAAM,KAAK,SAAS;AAAQ,YAAM,IAAI,MAAM,4BAA4B;AACzF,QAAI,QAAQ;AAAK,YAAM,IAAI,MAAM,gCAAgC;AAIjE,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,GAAG;AAEf,QAAI,QAAQ,KAAK,QAAQ,KAAK;AAE9B,WAAO,OAAO;AACb,YAAM,MAAK;AAEX,cAAQ,MAAM,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,IAAI;IACvD;AAGE,WAAO;EACT;EAEC,WAAW;AACV,QAAI,KAAK,MAAM;AAAQ,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC9D,QAAI,QAAQ,KAAK;AACjB,OAAG;AACF,UAAI,MAAM,MAAM;AAAQ,eAAO,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AACjE,UAAI,MAAM,QAAQ;AAAQ,eAAO,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC;AACvE,UAAI,MAAM,MAAM;AAAQ,eAAO,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC;IACpE,SAAY,QAAQ,MAAM;AACxB,QAAI,KAAK,MAAM;AAAQ,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC9D,WAAO;EACT;EAEC,WAAW;AACV,QAAI,YAAY,KAAK,MAAM,YAAY,CAAC;AACxC,QAAI,cAAc;AAAI,aAAO,KAAK,MAAM,OAAO,YAAY,CAAC;AAC5D,QAAI,UAAU,KAAK;AACnB,QAAI,QAAQ,KAAK;AACjB,OAAG;AACF,UAAI,MAAM,MAAM,SAAS,GAAG;AAC3B,oBAAY,MAAM,MAAM,YAAY,CAAC;AACrC,YAAI,cAAc;AAAI,iBAAO,MAAM,MAAM,OAAO,YAAY,CAAC,IAAI;AACjE,kBAAU,MAAM,QAAQ;MAC5B;AAEG,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC7B,oBAAY,MAAM,QAAQ,YAAY,CAAC;AACvC,YAAI,cAAc;AAAI,iBAAO,MAAM,QAAQ,OAAO,YAAY,CAAC,IAAI;AACnE,kBAAU,MAAM,UAAU;MAC9B;AAEG,UAAI,MAAM,MAAM,SAAS,GAAG;AAC3B,oBAAY,MAAM,MAAM,YAAY,CAAC;AACrC,YAAI,cAAc;AAAI,iBAAO,MAAM,MAAM,OAAO,YAAY,CAAC,IAAI;AACjE,kBAAU,MAAM,QAAQ;MAC5B;IACA,SAAY,QAAQ,MAAM;AACxB,gBAAY,KAAK,MAAM,YAAY,CAAC;AACpC,QAAI,cAAc;AAAI,aAAO,KAAK,MAAM,OAAO,YAAY,CAAC,IAAI;AAChE,WAAO,KAAK,QAAQ;EACtB;EAEC,MAAM,QAAQ,GAAG,MAAM,KAAK,SAAS,QAAQ;AAC5C,WAAO,QAAQ;AAAG,eAAS,KAAK,SAAS;AACzC,WAAO,MAAM;AAAG,aAAO,KAAK,SAAS;AAErC,QAAI,SAAS;AAGb,QAAI,QAAQ,KAAK;AACjB,WAAO,UAAU,MAAM,QAAQ,SAAS,MAAM,OAAO,QAAQ;AAE5D,UAAI,MAAM,QAAQ,OAAO,MAAM,OAAO,KAAK;AAC1C,eAAO;MACX;AAEG,cAAQ,MAAM;IACjB;AAEE,QAAI,SAAS,MAAM,UAAU,MAAM,UAAU;AAC5C,YAAM,IAAI,MAAM,iCAAiC,KAAK,yBAAyB;AAEhF,UAAM,aAAa;AACnB,WAAO,OAAO;AACb,UAAI,MAAM,UAAU,eAAe,SAAS,MAAM,UAAU,QAAQ;AACnE,kBAAU,MAAM;MACpB;AAEG,YAAM,cAAc,MAAM,QAAQ,OAAO,MAAM,OAAO;AACtD,UAAI,eAAe,MAAM,UAAU,MAAM,QAAQ;AAChD,cAAM,IAAI,MAAM,iCAAiC,GAAG,uBAAuB;AAE5E,YAAM,aAAa,eAAe,QAAQ,QAAQ,MAAM,QAAQ;AAChE,YAAM,WAAW,cAAc,MAAM,QAAQ,SAAS,MAAM,MAAM,MAAM,MAAM,QAAQ;AAEtF,gBAAU,MAAM,QAAQ,MAAM,YAAY,QAAQ;AAElD,UAAI,MAAM,UAAU,CAAC,eAAe,MAAM,QAAQ,MAAM;AACvD,kBAAU,MAAM;MACpB;AAEG,UAAI,aAAa;AAChB;MACJ;AAEG,cAAQ,MAAM;IACjB;AAEE,WAAO;EACT;;EAGC,KAAK,OAAO,KAAK;AAChB,UAAM,QAAQ,KAAK,MAAK;AACxB,UAAM,OAAO,GAAG,KAAK;AACrB,UAAM,OAAO,KAAK,MAAM,SAAS,MAAM;AAEvC,WAAO;EACT;EAEC,OAAO,OAAO;AACb,QAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK;AAAG;AAI9C,QAAI,QAAQ,KAAK;AACjB,UAAM,gBAAgB,QAAQ,MAAM;AAEpC,WAAO,OAAO;AACb,UAAI,MAAM,SAAS,KAAK;AAAG,eAAO,KAAK,YAAY,OAAO,KAAK;AAE/D,cAAQ,gBAAgB,KAAK,QAAQ,MAAM,GAAG,IAAI,KAAK,MAAM,MAAM,KAAK;IAC3E;EACA;EAEC,YAAY,OAAO,OAAO;AACzB,QAAI,MAAM,UAAU,MAAM,QAAQ,QAAQ;AAEzC,YAAM,MAAM,WAAW,KAAK,QAAQ,EAAE,KAAK;AAC3C,YAAM,IAAI;QACT,sDAAsD,IAAI,IAAI,IAAI,IAAI,MAAM,YAAO,MAAM,QAAQ;MACrG;IACA;AAEE,UAAM,WAAW,MAAM,MAAM,KAAK;AAElC,SAAK,MAAM,KAAK,IAAI;AACpB,SAAK,QAAQ,KAAK,IAAI;AACtB,SAAK,MAAM,SAAS,GAAG,IAAI;AAE3B,QAAI,UAAU,KAAK;AAAW,WAAK,YAAY;AAE/C,SAAK,oBAAoB;AAEzB,WAAO;EACT;EAEC,WAAW;AACV,QAAI,MAAM,KAAK;AAEf,QAAI,QAAQ,KAAK;AACjB,WAAO,OAAO;AACb,aAAO,MAAM,SAAQ;AACrB,cAAQ,MAAM;IACjB;AAEE,WAAO,MAAM,KAAK;EACpB;EAEC,UAAU;AACT,QAAI,QAAQ,KAAK;AACjB,OAAG;AACF,UACE,MAAM,MAAM,UAAU,MAAM,MAAM,KAAI,KACtC,MAAM,QAAQ,UAAU,MAAM,QAAQ,KAAI,KAC1C,MAAM,MAAM,UAAU,MAAM,MAAM,KAAI;AAEvC,eAAO;IACX,SAAY,QAAQ,MAAM;AACxB,WAAO;EACT;EAEC,SAAS;AACR,QAAI,QAAQ,KAAK;AACjB,QAAI,SAAS;AACb,OAAG;AACF,gBAAU,MAAM,MAAM,SAAS,MAAM,QAAQ,SAAS,MAAM,MAAM;IACrE,SAAY,QAAQ,MAAM;AACxB,WAAO;EACT;EAEC,YAAY;AACX,WAAO,KAAK,KAAK,UAAU;EAC7B;EAEC,KAAK,UAAU;AACd,WAAO,KAAK,UAAU,QAAQ,EAAE,QAAQ,QAAQ;EAClD;EAEC,eAAe,UAAU;AACxB,UAAM,KAAK,IAAI,QAAQ,YAAY,SAAS,IAAI;AAEhD,SAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,QAAI,KAAK,MAAM;AAAQ,aAAO;AAE9B,QAAI,QAAQ,KAAK;AAEjB,OAAG;AACF,YAAM,MAAM,MAAM;AAClB,YAAM,UAAU,MAAM,QAAQ,EAAE;AAGhC,UAAI,MAAM,QAAQ,KAAK;AACtB,YAAI,KAAK,cAAc,OAAO;AAC7B,eAAK,YAAY,MAAM;QAC5B;AAEI,aAAK,MAAM,MAAM,GAAG,IAAI;AACxB,aAAK,QAAQ,MAAM,KAAK,KAAK,IAAI,MAAM;AACvC,aAAK,MAAM,MAAM,KAAK,GAAG,IAAI,MAAM;MACvC;AAEG,UAAI;AAAS,eAAO;AACpB,cAAQ,MAAM;IACjB,SAAW;AAET,WAAO;EACT;EAEC,QAAQ,UAAU;AACjB,SAAK,eAAe,QAAQ;AAC5B,WAAO;EACT;EACC,iBAAiB,UAAU;AAC1B,UAAM,KAAK,IAAI,OAAO,OAAO,YAAY,SAAS,GAAG;AAErD,SAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC,QAAI,KAAK,MAAM;AAAQ,aAAO;AAE9B,QAAI,QAAQ,KAAK;AAEjB,OAAG;AACF,YAAM,MAAM,MAAM;AAClB,YAAM,UAAU,MAAM,UAAU,EAAE;AAElC,UAAI,MAAM,QAAQ,KAAK;AAEtB,YAAI,UAAU,KAAK;AAAW,eAAK,YAAY,MAAM;AAErD,aAAK,MAAM,MAAM,GAAG,IAAI;AACxB,aAAK,QAAQ,MAAM,KAAK,KAAK,IAAI,MAAM;AACvC,aAAK,MAAM,MAAM,KAAK,GAAG,IAAI,MAAM;MACvC;AAEG,UAAI;AAAS,eAAO;AACpB,cAAQ,MAAM;IACjB,SAAW;AAET,WAAO;EACT;EAEC,UAAU,UAAU;AACnB,SAAK,iBAAiB,QAAQ;AAC9B,WAAO;EACT;EAEC,aAAa;AACZ,WAAO,KAAK,aAAa,KAAK,SAAQ;EACxC;EAEC,eAAe,aAAa,aAAa;AACxC,aAAS,eAAe,OAAO,KAAK;AACnC,UAAI,OAAO,gBAAgB,UAAU;AACpC,eAAO,YAAY,QAAQ,iBAAiB,CAAC,GAAG,MAAM;AAErD,cAAI,MAAM;AAAK,mBAAO;AACtB,cAAI,MAAM;AAAK,mBAAO,MAAM,CAAC;AAC7B,gBAAM,MAAM,CAAC;AACb,cAAI,MAAM,MAAM;AAAQ,mBAAO,MAAM,CAAC,CAAC;AACvC,iBAAO,IAAI,CAAC;QACjB,CAAK;MACL,OAAU;AACN,eAAO,YAAY,GAAG,OAAO,MAAM,OAAO,KAAK,MAAM,MAAM;MAC/D;IACA;AACE,aAAS,SAAS,IAAI,KAAK;AAC1B,UAAI;AACJ,YAAM,UAAU,CAAA;AAChB,aAAQ,QAAQ,GAAG,KAAK,GAAG,GAAI;AAC9B,gBAAQ,KAAK,KAAK;MACtB;AACG,aAAO;IACV;AACE,QAAI,YAAY,QAAQ;AACvB,YAAM,UAAU,SAAS,aAAa,KAAK,QAAQ;AACnD,cAAQ,QAAQ,CAAC,UAAU;AAC1B,YAAI,MAAM,SAAS,MAAM;AACxB,gBAAMC,eAAc,eAAe,OAAO,KAAK,QAAQ;AACvD,cAAIA,iBAAgB,MAAM,CAAC,GAAG;AAC7B,iBAAK;cACJ,MAAM;cACN,MAAM,QAAQ,MAAM,CAAC,EAAE;cACvBA;YACP;UACA;QACA;MACA,CAAI;IACJ,OAAS;AACN,YAAM,QAAQ,KAAK,SAAS,MAAM,WAAW;AAC7C,UAAI,SAAS,MAAM,SAAS,MAAM;AACjC,cAAMA,eAAc,eAAe,OAAO,KAAK,QAAQ;AACvD,YAAIA,iBAAgB,MAAM,CAAC,GAAG;AAC7B,eAAK;YACJ,MAAM;YACN,MAAM,QAAQ,MAAM,CAAC,EAAE;YACvBA;UACN;QACA;MACA;IACA;AACE,WAAO;EACT;EAEC,eAAe,QAAQ,aAAa;AACnC,UAAM,EAAE,SAAQ,IAAK;AACrB,UAAM,QAAQ,SAAS,QAAQ,MAAM;AAErC,QAAI,UAAU,IAAI;AACjB,WAAK,UAAU,OAAO,QAAQ,OAAO,QAAQ,WAAW;IAC3D;AAEE,WAAO;EACT;EAEC,QAAQ,aAAa,aAAa;AACjC,QAAI,OAAO,gBAAgB,UAAU;AACpC,aAAO,KAAK,eAAe,aAAa,WAAW;IACtD;AAEE,WAAO,KAAK,eAAe,aAAa,WAAW;EACrD;EAEC,kBAAkB,QAAQ,aAAa;AACtC,UAAM,EAAE,SAAQ,IAAK;AACrB,UAAM,eAAe,OAAO;AAC5B,aACK,QAAQ,SAAS,QAAQ,MAAM,GACnC,UAAU,IACV,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,YAAY,GACpD;AACD,YAAM,WAAW,SAAS,MAAM,OAAO,QAAQ,YAAY;AAC3D,UAAI,aAAa;AAChB,aAAK,UAAU,OAAO,QAAQ,cAAc,WAAW;IAC3D;AAEE,WAAO;EACT;EAEC,WAAW,aAAa,aAAa;AACpC,QAAI,OAAO,gBAAgB,UAAU;AACpC,aAAO,KAAK,kBAAkB,aAAa,WAAW;IACzD;AAEE,QAAI,CAAC,YAAY,QAAQ;AACxB,YAAM,IAAI;QACT;MACJ;IACA;AAEE,WAAO,KAAK,eAAe,aAAa,WAAW;EACrD;AACA;ACn2BA,IAAM,aAAa,OAAO,UAAU;AAErB,IAAM,SAAN,MAAM,QAAO;EAC3B,YAAY,UAAU,CAAA,GAAI;AACzB,SAAK,QAAQ,QAAQ,SAAS;AAC9B,SAAK,YAAY,QAAQ,cAAc,SAAY,QAAQ,YAAY;AACvE,SAAK,UAAU,CAAA;AACf,SAAK,gBAAgB,CAAA;AACrB,SAAK,8BAA8B,CAAA;EACrC;EAEC,UAAU,QAAQ;AACjB,QAAI,kBAAkB,aAAa;AAClC,aAAO,KAAK,UAAU;QACrB,SAAS;QACT,UAAU,OAAO;QACjB,WAAW,KAAK;MACpB,CAAI;IACJ;AAEE,QAAI,CAAC,SAAS,MAAM,KAAK,CAAC,OAAO,SAAS;AACzC,YAAM,IAAI;QACT;MACJ;IACA;AAEE,KAAC,YAAY,cAAc,yBAAyB,WAAW,EAAE,QAAQ,CAAC,WAAW;AACpF,UAAI,CAAC,WAAW,KAAK,QAAQ,MAAM;AAAG,eAAO,MAAM,IAAI,OAAO,QAAQ,MAAM;IAC/E,CAAG;AAED,QAAI,OAAO,cAAc,QAAW;AAEnC,aAAO,YAAY,KAAK;IAC3B;AAEE,QAAI,OAAO,UAAU;AACpB,UAAI,CAAC,WAAW,KAAK,KAAK,6BAA6B,OAAO,QAAQ,GAAG;AACxE,aAAK,4BAA4B,OAAO,QAAQ,IAAI,KAAK,cAAc;AACvE,aAAK,cAAc,KAAK,EAAE,UAAU,OAAO,UAAU,SAAS,OAAO,QAAQ,SAAQ,CAAE;MAC3F,OAAU;AACN,cAAM,eAAe,KAAK,cAAc,KAAK,4BAA4B,OAAO,QAAQ,CAAC;AACzF,YAAI,OAAO,QAAQ,aAAa,aAAa,SAAS;AACrD,gBAAM,IAAI,MAAM,kCAAkC,OAAO,QAAQ,uBAAuB;QAC7F;MACA;IACA;AAEE,SAAK,QAAQ,KAAK,MAAM;AACxB,WAAO;EACT;EAEC,OAAO,KAAK,SAAS;AACpB,SAAK,UAAU;MACd,SAAS,IAAI,YAAY,GAAG;MAC5B,WAAY,WAAW,QAAQ,aAAc;IAChD,CAAG;AAED,WAAO;EACT;EAEC,QAAQ;AACP,UAAM,SAAS,IAAI,QAAO;MACzB,OAAO,KAAK;MACZ,WAAW,KAAK;IACnB,CAAG;AAED,SAAK,QAAQ,QAAQ,CAAC,WAAW;AAChC,aAAO,UAAU;QAChB,UAAU,OAAO;QACjB,SAAS,OAAO,QAAQ,MAAK;QAC7B,WAAW,OAAO;MACtB,CAAI;IACJ,CAAG;AAED,WAAO;EACT;EAEC,mBAAmB,UAAU,CAAA,GAAI;AAChC,UAAM,QAAQ,CAAA;AACd,QAAI,sBAAsB;AAC1B,SAAK,QAAQ,QAAQ,CAAC,WAAW;AAChC,aAAO,KAAK,OAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,SAAS;AACzD,YAAI,CAAC,CAAC,MAAM,QAAQ,IAAI;AAAG,gBAAM,KAAK,IAAI;MAC9C,CAAI;IACJ,CAAG;AAED,UAAM,WAAW,IAAI,SAAS,QAAQ,KAAK;AAE3C,QAAI,KAAK,OAAO;AACf,eAAS,QAAQ,KAAK,KAAK;IAC9B;AAEE,SAAK,QAAQ,QAAQ,CAAC,QAAQ,MAAM;AACnC,UAAI,IAAI,GAAG;AACV,iBAAS,QAAQ,KAAK,SAAS;MACnC;AAEG,YAAM,cAAc,OAAO,WAAW,KAAK,4BAA4B,OAAO,QAAQ,IAAI;AAC1F,YAAM,cAAc,OAAO;AAC3B,YAAM,SAAS,WAAW,YAAY,QAAQ;AAE9C,UAAI,YAAY,OAAO;AACtB,iBAAS,QAAQ,YAAY,KAAK;MACtC;AAEG,kBAAY,WAAW,SAAS,CAAC,UAAU;AAC1C,cAAM,MAAM,OAAO,MAAM,KAAK;AAE9B,YAAI,MAAM,MAAM;AAAQ,mBAAS,QAAQ,MAAM,KAAK;AAEpD,YAAI,OAAO,UAAU;AACpB,cAAI,MAAM,QAAQ;AACjB,qBAAS;cACR;cACA,MAAM;cACN;cACA,MAAM,YAAY,MAAM,QAAQ,MAAM,QAAQ,IAAI;YACzD;UACA,OAAY;AACN,qBAAS;cACR;cACA;cACA,YAAY;cACZ;cACA,YAAY;YACnB;UACA;QACA,OAAW;AACN,mBAAS,QAAQ,MAAM,OAAO;QACnC;AAEI,YAAI,MAAM,MAAM;AAAQ,mBAAS,QAAQ,MAAM,KAAK;MACxD,CAAI;AAED,UAAI,YAAY,OAAO;AACtB,iBAAS,QAAQ,YAAY,KAAK;MACtC;AAEG,UAAI,OAAO,cAAc,gBAAgB,IAAI;AAC5C,YAAI,wBAAwB,QAAW;AACtC,gCAAsB,CAAA;QAC3B;AACI,4BAAoB,KAAK,WAAW;MACxC;IACA,CAAG;AAED,WAAO;MACN,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM,OAAO,EAAE,IAAG,IAAK;MACzD,SAAS,KAAK,cAAc,IAAI,CAAC,WAAW;AAC3C,eAAO,QAAQ,OAAO,gBAAgB,QAAQ,MAAM,OAAO,QAAQ,IAAI,OAAO;MAClF,CAAI;MACD,gBAAgB,KAAK,cAAc,IAAI,CAAC,WAAW;AAClD,eAAO,QAAQ,iBAAiB,OAAO,UAAU;MACrD,CAAI;MACD;MACA,UAAU,SAAS;MACnB;IACH;EACA;EAEC,YAAY,SAAS;AACpB,WAAO,IAAI,UAAU,KAAK,mBAAmB,OAAO,CAAC;EACvD;EAEC,kBAAkB;AACjB,UAAM,qBAAqB,CAAA;AAE3B,SAAK,QAAQ,QAAQ,CAAC,WAAW;AAChC,YAAM,YAAY,OAAO,QAAQ,oBAAmB;AAEpD,UAAI,cAAc;AAAM;AAExB,UAAI,CAAC,mBAAmB,SAAS;AAAG,2BAAmB,SAAS,IAAI;AACpE,yBAAmB,SAAS,KAAK;IACpC,CAAG;AAED,WACC,OAAO,KAAK,kBAAkB,EAAE,KAAK,CAAC,GAAG,MAAM;AAC9C,aAAO,mBAAmB,CAAC,IAAI,mBAAmB,CAAC;IACvD,CAAI,EAAE,CAAC,KAAK;EAEZ;EAEC,OAAO,WAAW;AACjB,QAAI,CAAC,UAAU,QAAQ;AACtB,kBAAY,KAAK,gBAAe;IACnC;AAEE,QAAI,cAAc;AAAI,aAAO;AAE7B,QAAI,kBAAkB,CAAC,KAAK,SAAS,KAAK,MAAM,MAAM,EAAE,MAAM;AAE9D,SAAK,QAAQ,QAAQ,CAAC,QAAQ,MAAM;AACnC,YAAM,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY,KAAK;AAC3E,YAAM,cAAc,mBAAoB,IAAI,KAAK,SAAS,KAAK,SAAS;AAExE,aAAO,QAAQ,OAAO,WAAW;QAChC,SAAS,OAAO;QAChB;;MACJ,CAAI;AAED,wBAAkB,OAAO,QAAQ,SAAQ,MAAO;IACnD,CAAG;AAED,QAAI,KAAK,OAAO;AACf,WAAK,QACJ,YACA,KAAK,MAAM,QAAQ,YAAY,CAAC,OAAO,UAAU;AAChD,eAAO,QAAQ,IAAI,YAAY,QAAQ;MAC5C,CAAK;IACL;AAEE,WAAO;EACT;EAEC,QAAQ,KAAK;AACZ,SAAK,QAAQ,MAAM,KAAK;AACxB,WAAO;EACT;EAEC,WAAW;AACV,UAAM,OAAO,KAAK,QAChB,IAAI,CAAC,QAAQ,MAAM;AACnB,YAAM,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY,KAAK;AAC3E,YAAM,OAAO,IAAI,IAAI,YAAY,MAAM,OAAO,QAAQ,SAAQ;AAE9D,aAAO;IACX,CAAI,EACA,KAAK,EAAE;AAET,WAAO,KAAK,QAAQ;EACtB;EAEC,UAAU;AACT,QAAI,KAAK,MAAM,UAAU,KAAK,MAAM,KAAI;AAAI,aAAO;AACnD,QAAI,KAAK,QAAQ,KAAK,CAAC,WAAW,CAAC,OAAO,QAAQ,QAAO,CAAE;AAAG,aAAO;AACrE,WAAO;EACT;EAEC,SAAS;AACR,WAAO,KAAK,QAAQ;MACnB,CAAC,QAAQ,WAAW,SAAS,OAAO,QAAQ,OAAM;MAClD,KAAK,MAAM;IACd;EACA;EAEC,YAAY;AACX,WAAO,KAAK,KAAK,UAAU;EAC7B;EAEC,KAAK,UAAU;AACd,WAAO,KAAK,UAAU,QAAQ,EAAE,QAAQ,QAAQ;EAClD;EAEC,UAAU,UAAU;AACnB,UAAM,KAAK,IAAI,OAAO,OAAO,YAAY,SAAS,GAAG;AACrD,SAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AAEtC,QAAI,CAAC,KAAK,OAAO;AAChB,UAAI;AACJ,UAAI,IAAI;AAER,SAAG;AACF,iBAAS,KAAK,QAAQ,GAAG;AACzB,YAAI,CAAC,QAAQ;AACZ;QACL;MACA,SAAY,CAAC,OAAO,QAAQ,iBAAiB,QAAQ;IACrD;AAEE,WAAO;EACT;EAEC,QAAQ,UAAU;AACjB,UAAM,KAAK,IAAI,QAAQ,YAAY,SAAS,IAAI;AAEhD,QAAI;AACJ,QAAI,IAAI,KAAK,QAAQ,SAAS;AAE9B,OAAG;AACF,eAAS,KAAK,QAAQ,GAAG;AACzB,UAAI,CAAC,QAAQ;AACZ,aAAK,QAAQ,KAAK,MAAM,QAAQ,IAAI,EAAE;AACtC;MACJ;IACA,SAAW,CAAC,OAAO,QAAQ,eAAe,QAAQ;AAEhD,WAAO;EACT;AACA;", "names": ["n", "segment", "replacement"]}