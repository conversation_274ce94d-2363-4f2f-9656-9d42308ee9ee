{"version": 3, "file": "use-swrv.js", "sourceRoot": "", "sources": ["../src/use-swrv.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,2BAUY;AACZ,gEAAwC;AACxC,kDAA+B;AAK/B,IAAM,UAAU,GAAG,IAAI,eAAS,EAA6B,CAAA;AAC7D,IAAM,SAAS,GAAG,IAAI,eAAS,EAAwB,CAAA;AACvD,IAAM,cAAc,GAAG,IAAI,eAAS,EAA6B,CAAA;AAEjE,IAAM,aAAa,GAAY;IAC7B,KAAK,EAAE,UAAU;IACjB,eAAe,EAAE,CAAC;IAClB,GAAG,EAAE,CAAC;IACN,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,IAAI;IACtB,iBAAiB,EAAE,IAAI;IACvB,kBAAkB,EAAE,CAAC;IACrB,kBAAkB,EAAE,IAAI;IACxB,kBAAkB,EAAE,IAAI;IACxB,eAAe,EAAE,CAAC;IAClB,OAAO,EAAE,oBAAS,CAAC,OAAO;IAC1B,QAAQ,EAAE,oBAAS,CAAC,QAAQ;IAC5B,iBAAiB,EAAE,oBAAS,CAAC,iBAAiB;CAC/C,CAAA;AAED;;GAEG;AACH,SAAS,WAAW,CAAE,GAAW,EAAE,MAA0B,EAAE,GAAW;IACxE,IAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACvC,IAAI,YAAY,EAAE;QAChB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;KAC/B;SAAM;QACL,gDAAgD;QAChD,IAAM,WAAW,GAAG,IAAI,CAAA;QACxB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;KAChE;AACH,CAAC;AAED,SAAS,YAAY,CAAE,UAAkD,EAAE,eAAuB,EAAE,MAAe;IACjH,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE;QAC/B,OAAM;KACP;IAED,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,eAAe,GAAG,MAAM,CAAC,eAAe,EAAE;QACpF,OAAM;KACP;IAED,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAA;IACpE,IAAM,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC,kBAAkB,CAAA;IACjD,UAAU,CAAC;QACT,UAAU,CAAC,IAAI,EAAE,EAAE,eAAe,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAA;IAC5E,CAAC,EAAE,OAAO,CAAC,CAAA;AACb,CAAC;AAED;;;GAGG;AACH,IAAM,MAAM,GAAG,UAAa,GAAW,EAAE,GAAyB,EAAE,KAAkB,EAAE,GAAuB;IAA3C,sBAAA,EAAA,kBAAkB;IAAE,oBAAA,EAAA,MAAM,aAAa,CAAC,GAAG;;;;;;yBAGzG,SAAS,CAAC,GAAG,CAAC,EAAd,wBAAc;;;;oBAEP,qBAAM,GAAG,EAAA;;oBAAhB,IAAI,GAAG,SAAS,CAAA;;;;oBAEhB,KAAK,GAAG,KAAG,CAAA;;;;oBAGb,IAAI,GAAG,GAAG,CAAA;;;oBAGZ,wCAAwC;oBACxC,YAAY,GAAG,KAAK,CAAA;oBAEd,OAAO,GAAG,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,YAAY,cAAA,EAAE,CAAA;oBAC7C,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;wBAC/B,IAAI;4BACF,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA;yBAC7B;wBAAC,OAAO,GAAG,EAAE;4BACZ,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAA;yBACxD;qBACF;oBAKK,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;oBACnC,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;wBAIhC,SAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;wBAEnD,MAAI,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,GAAG;4BAClB,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;gCACvC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;6BACtB;4BACD,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;4BACvB,CAAC,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;4BAErC,IAAM,MAAM,GAAG,GAAG,KAAK,MAAI,CAAC,MAAM,GAAG,CAAC,CAAA;4BACtC,IAAI,CAAC,MAAM,EAAE;gCACX,0CAA0C;gCAC1C,OAAO,MAAI,CAAC,GAAG,CAAC,CAAA;6BACjB;wBACH,CAAC,CAAC,CAAA;wBAEF,MAAI,GAAG,MAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;qBAC5B;oBAED,sBAAO,OAAO,EAAA;;;;CACf,CAAA;AA+RQ,wBAAM;AApRf,SAAS,OAAO;IAAhB,iBA8QC;IA9Q0C,cAAO;SAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;QAAP,yBAAO;;IAChD,IAAI,GAAS,CAAA;IACb,IAAI,EAAsC,CAAA;IAC1C,IAAI,MAAM,gBAAiB,aAAa,CAAE,CAAA;IAC1C,IAAI,SAAS,GAAG,KAAK,CAAA;IACrB,IAAI,UAAU,GAAG,KAAK,CAAA;IAEtB,IAAM,QAAQ,GAAG,IAAA,wBAAkB,GAAS,CAAA;IAC5C,IAAM,EAAE,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,KAAI,QAAQ,CAAA,CAAC,oDAAoD;IAC3F,IAAI,CAAC,EAAE,EAAE;QACP,OAAO,CAAC,KAAK,CAAC,uHAAuH,CAAC,CAAA;QACtI,OAAO,IAAI,CAAA;KACZ;IAED,IAAM,SAAS,GAAG,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,SAAS,KAAI,KAAK,CAAA;IAExC,cAAc;IACd;;;;;;;MAOE;IACF,aAAa;IAEb,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;QACpB,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;KACd;IACD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;QACpB,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;KACb;IACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,MAAM,yBACD,MAAM,GACN,IAAI,CAAC,CAAC,CAAC,CACX,CAAA;KACF;IAED,IAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAA;IACrD,IAAM,MAAM,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAE,GAAW,CAAC,CAAC,CAAC,IAAA,SAAG,EAAC,GAAG,CAAC,CAAA;IAElE,IAAI,OAAO,EAAE,KAAK,WAAW,EAAE;QAC7B,yBAAyB;QACzB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAA;KACpB;IAED,IAAI,QAAQ,GAAG,IAA6B,CAAA;IAE5C,cAAc;IACd,wBAAwB;IACxB,+EAA+E;IAC/E,wDAAwD;IACxD,wEAAwE;IACxE,4DAA4D;IAE5D,qDAAqD;IACrD,iDAAiD;IACjD,+EAA+E;IAE/E,2BAA2B;IAC3B,2CAA2C;IAC3C,0BAA0B;IAC1B,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,aAAa;IAEb,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,IAAA,cAAQ,EAAC;YAClB,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,SAAS;YAChB,YAAY,EAAE,IAAI;YAClB,GAAG,EAAE,IAAI;SACV,CAA0B,CAAA;KAC5B;IAED;;OAEG;IACH,IAAM,UAAU,GAAG,UAAO,IAAsB,EAAE,IAAwB;;;;;;oBAClE,YAAY,GAAG,QAAQ,CAAC,IAAI,KAAK,SAAS,CAAA;oBAC1C,MAAM,GAAG,MAAM,CAAC,KAAK,CAAA;oBAC3B,IAAI,CAAC,MAAM,EAAE;wBAAE,sBAAM;qBAAE;oBAEjB,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;oBACpC,OAAO,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,CAAA;oBAE3C,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAA;oBAC5B,IAAI,OAAO,EAAE;wBACX,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;wBAC5B,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;qBAC/B;oBAEK,OAAO,GAAG,IAAI,IAAI,EAAE,CAAA;oBAC1B,IACE,CAAC,OAAO;wBACR,CAAC,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC;wBAC9C,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,MAAK,SAAS,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,CAAA,CAAC,EAC/D;wBACA,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAA;wBAC7B,sBAAM;qBACP;oBAED,0DAA0D;oBAC1D,IAAI,SAAS,EAAE;wBACP,gBAAgB,GAAG,OAAO,CAC9B,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,gBAAgB,CAAC,KAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,CAAA,CACzF,CAAA;wBAED,IAAI,CAAC,gBAAgB,EAAE;4BACrB,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAA;4BAC7B,sBAAM;yBACP;qBACF;oBAEK,OAAO,GAAG;;;;;oCACR,gBAAgB,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;yCAC/C,CAAC,gBAAgB,EAAjB,wBAAiB;oCACb,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;oCACvD,UAAU,GAAG,OAAO,wCAAI,WAAW,UAAC,CAAA;oCAC1C,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAA;oCAC/D,qBAAM,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAA;;oCAAnD,SAAmD,CAAA;;wCAEnD,qBAAM,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAA;;oCAA9D,SAA8D,CAAA;;;oCAEhE,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAA;oCAC7B,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oCAC7B,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE;wCAC1B,kBAAkB,GAAG,CAAC,SAAS,IAAI,MAAM,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;wCAC7G,IAAI,kBAAkB,EAAE;4CACtB,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;yCAClE;qCACF;;;;yBACF,CAAA;yBAEG,CAAA,OAAO,IAAI,MAAM,CAAC,kBAAkB,CAAA,EAApC,wBAAoC;oBACtC,UAAU,CAAC;;;;yCACL,CAAC,SAAS,EAAV,wBAAU;oCACZ,qBAAM,OAAO,EAAE,EAAA;;oCAAf,SAAe,CAAA;;;;;yBAElB,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAA;;wBAE7B,qBAAM,OAAO,EAAE,EAAA;;oBAAf,SAAe,CAAA;;;;;SAElB,CAAA;IAED,IAAM,cAAc,GAAG;QAAY,sBAAA,UAAU,CAAC,IAAI,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,EAAA;aAAA,CAAA;IAClF,IAAI,KAAK,GAAG,IAAI,CAAA;IAChB;;OAEG;IACH,IAAA,eAAS,EAAC;QACR,IAAM,IAAI,GAAG;;;;6BAKP,CAAA,CAAC,QAAQ,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAA,EAApC,wBAAoC;wBACtC,wDAAwD;wBACxD,6CAA6C;wBAC7C,qBAAM,UAAU,EAAE,EAAA;;wBAFlB,wDAAwD;wBACxD,6CAA6C;wBAC7C,SAAkB,CAAA;;;wBAElB,IAAI,KAAK,EAAE;4BACT,YAAY,CAAC,KAAK,CAAC,CAAA;yBACpB;;;wBAGH,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,SAAS,EAAE;4BACxC,KAAK,GAAG,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,CAAA;yBACjD;;;;aACF,CAAA;QAED,IAAI,MAAM,CAAC,eAAe,EAAE;YAC1B,KAAK,GAAG,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,CAAA;SACjD;QACD,IAAI,MAAM,CAAC,iBAAiB,EAAE;YAC5B,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;YACpE,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;SACxD;IACH,CAAC,CAAC,CAAA;IAEF;;OAEG;IACH,IAAA,iBAAW,EAAC;QACV,SAAS,GAAG,IAAI,CAAA;QAChB,IAAI,KAAK,EAAE;YACT,YAAY,CAAC,KAAK,CAAC,CAAA;SACpB;QACD,IAAI,MAAM,CAAC,iBAAiB,EAAE;YAC5B,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;YACvE,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;SAC3D;QACD,IAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAChD,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,KAAK,QAAQ,EAAhB,CAAgB,CAAC,CAAA;SACxE;IACH,CAAC,CAAC,CAAA;IAEF,cAAc;IACd,mBAAmB;IACnB,2CAA2C;IAC3C,qBAAqB;IACrB,0BAA0B;IAC1B,qEAAqE;IACrE,MAAM;IAEN,kCAAkC;IAClC,sDAAsD;IACtD,oBAAoB;IACpB,qDAAqD;IACrD,QAAQ;IACR,MAAM;IAEN,sEAAsE;IACtE,oCAAoC;IAEpC,0BAA0B;IAC1B,iDAAiD;IACjD,yCAAyC;IACzC,MAAM;IAEN,mCAAmC;IACnC,yBAAyB;IAEzB,iDAAiD;IAEjD,wCAAwC;IACxC,6BAA6B;IAC7B,+BAA+B;IAC/B,4CAA4C;IAC5C,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,aAAa;IAEb;;OAEG;IACH,IAAI;QACF,IAAA,WAAK,EAAC,MAAM,EAAE,UAAC,GAAG;YAChB,IAAI,CAAC,IAAA,gBAAU,EAAC,MAAM,CAAC,EAAE;gBACvB,MAAM,CAAC,KAAK,GAAG,GAAG,CAAA;aACnB;YACD,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAA;YAClB,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;YACpC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;YAExC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC7C,UAAU,EAAE,CAAA;aACb;YACD,UAAU,GAAG,KAAK,CAAA;QACpB,CAAC,EAAE;YACD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAA;KACH;IAAC,WAAM;QACN,aAAa;KACd;IAED,IAAM,GAAG,yBACJ,IAAA,YAAM,EAAC,QAAQ,CAAC,KACnB,MAAM,EAAE,UAAC,IAAI,EAAE,IAAuB,IAAK,OAAA,UAAU,CAAC,IAAI,wBACrD,IAAI,KACP,eAAe,EAAE,IAAI,IACrB,EAHyC,CAGzC,GACH,CAAA;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,SAAS,CAAK,CAAM;IAC3B,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU,CAAA;AAC5E,CAAC;AAGD,kBAAe,OAAO,CAAA"}