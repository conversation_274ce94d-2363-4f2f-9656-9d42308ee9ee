import { SWR as _ } from "swrev";
import { beforeUpdate as w, onDestroy as E } from "svelte";
function p() {
}
function D(t) {
  return t();
}
function q(t) {
  t.forEach(D);
}
function x(t) {
  return typeof t == "function";
}
function K(t, e) {
  return t != t ? e == e : t !== e || t && typeof t == "object" || typeof t == "function";
}
function z(t, ...e) {
  if (t == null) {
    for (const r of e)
      r(void 0);
    return p;
  }
  const n = t.subscribe(...e);
  return n.unsubscribe ? () => n.unsubscribe() : n;
}
const v = [];
function A(t, e) {
  return {
    subscribe: y(t, e).subscribe
  };
}
function y(t, e = p) {
  let n;
  const r = /* @__PURE__ */ new Set();
  function i(u) {
    if (K(t, u) && (t = u, n)) {
      const f = !v.length;
      for (const s of r)
        s[1](), v.push(s, t);
      if (f) {
        for (let s = 0; s < v.length; s += 2)
          v[s][0](v[s + 1]);
        v.length = 0;
      }
    }
  }
  function a(u) {
    i(u(t));
  }
  function d(u, f = p) {
    const s = [u, f];
    return r.add(s), r.size === 1 && (n = e(i, a) || p), u(t), () => {
      r.delete(s), r.size === 0 && n && (n(), n = null);
    };
  }
  return { set: i, update: a, subscribe: d };
}
function S(t, e, n) {
  const r = !Array.isArray(t), i = r ? [t] : t;
  if (!i.every(Boolean))
    throw new Error("derived() expects stores as input, got a falsy value");
  const a = e.length < 2;
  return A(n, (d, u) => {
    let f = !1;
    const s = [];
    let h = 0, o = p;
    const l = () => {
      if (h)
        return;
      o();
      const b = e(r ? s[0] : s, d, u);
      a ? d(b) : o = x(b) ? b : p;
    }, g = i.map(
      (b, m) => z(
        b,
        (R) => {
          s[m] = R, h &= ~(1 << m), f && l();
        },
        () => {
          h |= 1 << m;
        }
      )
    );
    return f = !0, l(), function() {
      q(g), o(), f = !1;
    };
  });
}
class O extends _ {
  /**
   * Svelte specific use of SWR.
   */
  useSWR(e, n) {
    let r;
    const i = y(void 0, () => () => r == null ? void 0 : r()), a = y(void 0, () => () => r == null ? void 0 : r());
    w(() => {
      const o = (g) => {
        a.set(void 0), i.set(g);
      }, l = (g) => a.set(g);
      r || (r = this.subscribe(e, o, l, {
        loadInitialCache: !0,
        ...n
      }).unsubscribe);
    }), E(() => r == null ? void 0 : r());
    const d = (o, l) => this.mutate(this.resolveKey(e), o, {
      revalidateOptions: n,
      ...l
    }), u = (o) => this.revalidate(this.resolveKey(e), { ...n, ...o }), f = (o) => this.clear(this.resolveKey(e), o), s = S([i, a], ([o, l]) => o === void 0 && l === void 0), h = S([i, a], ([o, l]) => o !== void 0 && l === void 0);
    return { data: i, error: a, mutate: d, revalidate: u, clear: f, isLoading: s, isValid: h };
  }
}
const W = (t) => new O(t);
let c = W();
const C = (t) => (c = W(t), c), I = (t, e) => c.subscribeData(t, e), L = (t, e) => c.subscribeErrors(t, e), U = (t) => c.get(t), V = (t) => c.getWait(t), $ = (t, e, n, r) => c.subscribe(t, e, n, r), F = (t, e) => c.useSWR(t, e), G = (t, e, n) => c.mutate(t, e, n), H = (t, e) => c.revalidate(t, e), J = (t, e) => c.clear(t, e);
export {
  O as SSWR,
  J as clear,
  C as createDefaultSWR,
  W as createSWR,
  U as get,
  V as getOrWait,
  G as mutate,
  H as revalidate,
  I as subscribe,
  L as subscribeErrors,
  c as swr,
  $ as use,
  F as useSWR
};
