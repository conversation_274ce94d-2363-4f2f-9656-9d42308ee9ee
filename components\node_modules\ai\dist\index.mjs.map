{"version": 3, "sources": ["../streams/index.ts", "../core/telemetry/assemble-operation-name.ts", "../core/telemetry/get-base-telemetry-attributes.ts", "../core/telemetry/get-tracer.ts", "../core/telemetry/noop-tracer.ts", "../core/telemetry/record-span.ts", "../core/telemetry/select-telemetry-attributes.ts", "../core/util/retry-with-exponential-backoff.ts", "../core/util/delay.ts", "../core/embed/embed.ts", "../core/util/split-array.ts", "../core/embed/embed-many.ts", "../core/generate-object/generate-object.ts", "../core/prompt/convert-to-language-model-prompt.ts", "../core/util/detect-image-mimetype.ts", "../core/util/download.ts", "../core/prompt/data-content.ts", "../core/prompt/invalid-message-role-error.ts", "../core/prompt/get-validated-prompt.ts", "../core/prompt/prepare-call-settings.ts", "../core/types/token-usage.ts", "../core/util/prepare-response-headers.ts", "../core/util/schema.ts", "../core/generate-object/inject-json-schema-into-system.ts", "../core/generate-object/stream-object.ts", "../core/util/async-iterable-stream.ts", "../core/util/delayed-promise.ts", "../core/util/is-non-empty-object.ts", "../core/prompt/prepare-tools-and-tool-choice.ts", "../core/generate-text/tool-call.ts", "../core/generate-text/generate-text.ts", "../core/util/merge-streams.ts", "../core/generate-text/run-tools-transformation.ts", "../core/generate-text/stream-text.ts", "../core/prompt/attachments-to-parts.ts", "../core/prompt/convert-to-core-messages.ts", "../core/registry/invalid-model-id-error.ts", "../core/registry/no-such-model-error.ts", "../core/registry/no-such-provider-error.ts", "../core/registry/provider-registry.ts", "../core/tool/tool.ts", "../core/types/errors.ts", "../core/util/cosine-similarity.ts", "../streams/ai-stream.ts", "../streams/stream-data.ts", "../streams/anthropic-stream.ts", "../streams/assistant-response.ts", "../streams/aws-bedrock-stream.ts", "../streams/cohere-stream.ts", "../streams/google-generative-ai-stream.ts", "../streams/huggingface-stream.ts", "../streams/inkeep-stream.ts", "../streams/langchain-adapter.ts", "../streams/langchain-stream.ts", "../streams/mistral-stream.ts", "../streams/openai-stream.ts", "../streams/replicate-stream.ts", "../streams/stream-to-response.ts", "../streams/streaming-text-response.ts"], "sourcesContent": ["// forwarding exports from ui-utils:\nexport {\n  formatStreamPart,\n  parseStreamPart,\n  readDataStream,\n  parseComplexResponse,\n} from '@ai-sdk/ui-utils';\nexport type {\n  AssistantStatus,\n  UseAssistantOptions,\n  Message,\n  CreateMessage,\n  DataMessage,\n  AssistantMessage,\n  JSONValue,\n  ChatRequest,\n  ChatRequestOptions,\n  Function,\n  FunctionCall,\n  FunctionCallHandler,\n  ToolInvocation,\n  Tool,\n  ToolCall,\n  ToolCallHandler,\n  ToolChoice,\n  StreamPart,\n  IdGenerator,\n  RequestOptions,\n} from '@ai-sdk/ui-utils';\n\nimport { generateId as generateIdImpl } from '@ai-sdk/provider-utils';\nexport const generateId = generateIdImpl;\n\n/**\n@deprecated Use `generateId` instead.\n */\n// TODO remove nanoid export (breaking change)\nexport const nanoid = generateIdImpl;\n\nexport * from '../core/index';\nexport * from './ai-stream';\nexport * from './anthropic-stream';\nexport * from './assistant-response';\nexport * from './aws-bedrock-stream';\nexport * from './cohere-stream';\nexport * from './google-generative-ai-stream';\nexport * from './huggingface-stream';\nexport * from './inkeep-stream';\nexport * as LangChainAdapter from './langchain-adapter';\nexport * from './langchain-stream';\nexport * from './mistral-stream';\nexport * from './openai-stream';\nexport * from './replicate-stream';\nexport * from './stream-data';\nexport * from './stream-to-response';\nexport * from './streaming-text-response';\n", "import { TelemetrySettings } from './telemetry-settings';\n\nexport function assembleOperationName({\n  operationName,\n  telemetry,\n}: {\n  operationName: string;\n  telemetry?: TelemetrySettings;\n}) {\n  return {\n    'operation.name': `${operationName}${\n      telemetry?.functionId != null ? ` ${telemetry.functionId}` : ''\n    }`,\n  };\n}\n", "import { Attributes } from '@opentelemetry/api';\nimport { CallSettings } from '../prompt/call-settings';\nimport { TelemetrySettings } from './telemetry-settings';\n\nexport function getBaseTelemetryAttributes({\n  model,\n  settings,\n  telemetry,\n  headers,\n}: {\n  model: { modelId: string; provider: string };\n  settings: Omit<CallSettings, 'abortSignal' | 'headers'>;\n  telemetry: TelemetrySettings | undefined;\n  headers: Record<string, string | undefined> | undefined;\n}): Attributes {\n  return {\n    'ai.model.provider': model.provider,\n    'ai.model.id': model.modelId,\n\n    // settings:\n    ...Object.entries(settings).reduce((attributes, [key, value]) => {\n      attributes[`ai.settings.${key}`] = value;\n      return attributes;\n    }, {} as Attributes),\n\n    // special telemetry information\n    'resource.name': telemetry?.functionId,\n    'ai.telemetry.functionId': telemetry?.functionId,\n\n    // add metadata as attributes:\n    ...Object.entries(telemetry?.metadata ?? {}).reduce(\n      (attributes, [key, value]) => {\n        attributes[`ai.telemetry.metadata.${key}`] = value;\n        return attributes;\n      },\n      {} as Attributes,\n    ),\n\n    // request headers\n    ...Object.entries(headers ?? {}).reduce((attributes, [key, value]) => {\n      if (value !== undefined) {\n        attributes[`ai.request.headers.${key}`] = value;\n      }\n      return attributes;\n    }, {} as Attributes),\n  };\n}\n", "import { Tracer, trace } from '@opentelemetry/api';\nimport { noopTracer } from './noop-tracer';\n\n/**\n * Tracer variable for testing. Tests can set this to a mock tracer.\n */\nlet testTracer: Tracer | undefined = undefined;\n\nexport function setTestTracer(tracer: Tracer | undefined) {\n  testTracer = tracer;\n}\n\nexport function getTracer({ isEnabled }: { isEnabled: boolean }): Tracer {\n  if (!isEnabled) {\n    return noopTracer;\n  }\n\n  if (testTracer) {\n    return testTracer;\n  }\n\n  return trace.getTracer('ai');\n}\n", "import { Span, SpanContext, Tracer } from '@opentelemetry/api';\n\n/**\n * Tracer implementation that does nothing (null object).\n */\nexport const noopTracer: Tracer = {\n  startSpan(): Span {\n    return noopSpan;\n  },\n\n  startActiveSpan<F extends (span: Span) => unknown>(\n    name: unknown,\n    arg1: unknown,\n    arg2?: unknown,\n    arg3?: F,\n  ): ReturnType<any> {\n    if (typeof arg1 === 'function') {\n      return arg1(noopSpan);\n    }\n    if (typeof arg2 === 'function') {\n      return arg2(noopSpan);\n    }\n    if (typeof arg3 === 'function') {\n      return arg3(noopSpan);\n    }\n  },\n};\n\nconst noopSpan: Span = {\n  spanContext() {\n    return noopSpanContext;\n  },\n  setAttribute() {\n    return this;\n  },\n  setAttributes() {\n    return this;\n  },\n  addEvent() {\n    return this;\n  },\n  addLink() {\n    return this;\n  },\n  addLinks() {\n    return this;\n  },\n  setStatus() {\n    return this;\n  },\n  updateName() {\n    return this;\n  },\n  end() {\n    return this;\n  },\n  isRecording() {\n    return false;\n  },\n  recordException() {\n    return this;\n  },\n};\n\nconst noopSpanContext: SpanContext = {\n  traceId: '',\n  spanId: '',\n  traceFlags: 0,\n};\n", "import { Attributes, Span, Tracer, SpanStatusCode } from '@opentelemetry/api';\n\nexport function recordSpan<T>({\n  name,\n  tracer,\n  attributes,\n  fn,\n  endWhenDone = true,\n}: {\n  name: string;\n  tracer: Tracer;\n  attributes: Attributes;\n  fn: (span: Span) => Promise<T>;\n  endWhenDone?: boolean;\n}) {\n  return tracer.startActiveSpan(name, { attributes }, async span => {\n    try {\n      const result = await fn(span);\n\n      if (endWhenDone) {\n        span.end();\n      }\n\n      return result;\n    } catch (error) {\n      try {\n        if (error instanceof Error) {\n          span.recordException({\n            name: error.name,\n            message: error.message,\n            stack: error.stack,\n          });\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n        } else {\n          span.setStatus({ code: SpanStatusCode.ERROR });\n        }\n      } finally {\n        // always stop the span when there is an error:\n        span.end();\n      }\n\n      throw error;\n    }\n  });\n}\n", "import type { Attributes, AttributeValue } from '@opentelemetry/api';\nimport type { TelemetrySettings } from './telemetry-settings';\n\nexport function selectTelemetryAttributes({\n  telemetry,\n  attributes,\n}: {\n  telemetry?: TelemetrySettings;\n  attributes: {\n    [attributeKey: string]:\n      | AttributeValue\n      | { input: () => AttributeValue | undefined }\n      | { output: () => AttributeValue | undefined }\n      | undefined;\n  };\n}): Attributes {\n  return Object.entries(attributes).reduce((attributes, [key, value]) => {\n    if (value === undefined) {\n      return attributes;\n    }\n\n    // input value, check if it should be recorded:\n    if (\n      typeof value === 'object' &&\n      'input' in value &&\n      typeof value.input === 'function'\n    ) {\n      // default to true:\n      if (telemetry?.recordInputs === false) {\n        return attributes;\n      }\n\n      const result = value.input();\n\n      return result === undefined\n        ? attributes\n        : { ...attributes, [key]: result };\n    }\n\n    // output value, check if it should be recorded:\n    if (\n      typeof value === 'object' &&\n      'output' in value &&\n      typeof value.output === 'function'\n    ) {\n      // default to true:\n      if (telemetry?.recordOutputs === false) {\n        return attributes;\n      }\n\n      const result = value.output();\n\n      return result === undefined\n        ? attributes\n        : { ...attributes, [key]: result };\n    }\n\n    // value is an attribute value already:\n    return { ...attributes, [key]: value };\n  }, {});\n}\n", "import { APICallError, RetryError } from '@ai-sdk/provider';\nimport { getErrorMessage, isAbortError } from '@ai-sdk/provider-utils';\nimport { delay } from './delay';\n\nexport type RetryFunction = <OUTPUT>(\n  fn: () => PromiseLike<OUTPUT>,\n) => PromiseLike<OUTPUT>;\n\n/**\nThe `retryWithExponentialBackoff` strategy retries a failed API call with an exponential backoff.\nYou can configure the maximum number of retries, the initial delay, and the backoff factor.\n */\nexport const retryWithExponentialBackoff =\n  ({\n    maxRetries = 2,\n    initialDelayInMs = 2000,\n    backoffFactor = 2,\n  } = {}): RetryFunction =>\n  async <OUTPUT>(f: () => PromiseLike<OUTPUT>) =>\n    _retryWithExponentialBackoff(f, {\n      maxRetries,\n      delayInMs: initialDelayInMs,\n      backoffFactor,\n    });\n\nasync function _retryWithExponentialBackoff<OUTPUT>(\n  f: () => PromiseLike<OUTPUT>,\n  {\n    maxRetries,\n    delayInMs,\n    backoffFactor,\n  }: { maxRetries: number; delayInMs: number; backoffFactor: number },\n  errors: unknown[] = [],\n): Promise<OUTPUT> {\n  try {\n    return await f();\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error; // don't retry when the request was aborted\n    }\n\n    if (maxRetries === 0) {\n      throw error; // don't wrap the error when retries are disabled\n    }\n\n    const errorMessage = getErrorMessage(error);\n    const newErrors = [...errors, error];\n    const tryNumber = newErrors.length;\n\n    if (tryNumber > maxRetries) {\n      throw new RetryError({\n        message: `Failed after ${tryNumber} attempts. Last error: ${errorMessage}`,\n        reason: 'maxRetriesExceeded',\n        errors: newErrors,\n      });\n    }\n\n    if (\n      error instanceof Error &&\n      APICallError.isAPICallError(error) &&\n      error.isRetryable === true &&\n      tryNumber <= maxRetries\n    ) {\n      await delay(delayInMs);\n      return _retryWithExponentialBackoff(\n        f,\n        { maxRetries, delayInMs: backoffFactor * delayInMs, backoffFactor },\n        newErrors,\n      );\n    }\n\n    if (tryNumber === 1) {\n      throw error; // don't wrap the error when a non-retryable error occurs on the first try\n    }\n\n    throw new RetryError({\n      message: `Failed after ${tryNumber} attempts with non-retryable error: '${errorMessage}'`,\n      reason: 'errorNotRetryable',\n      errors: newErrors,\n    });\n  }\n}\n", "export async function delay(delayInMs: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "import { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { EmbeddingModel } from '../types';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { EmbedResult } from './embed-result';\n\n/**\nEmbed a value using an embedding model. The type of the value is defined by the embedding model.\n\n@param model - The embedding model to use.\n@param value - The value that should be embedded.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@returns A result object that contains the embedding, the value, and additional information.\n */\nexport async function embed<VALUE>({\n  model,\n  value,\n  maxRetries,\n  abortSignal,\n  headers,\n  experimental_telemetry: telemetry,\n}: {\n  /**\nThe embedding model to use.\n     */\n  model: EmbeddingModel<VALUE>;\n\n  /**\nThe value that should be embedded.\n   */\n  value: VALUE;\n\n  /**\nMaximum number of retries per embedding model call. Set to 0 to disable retries.\n\n@default 2\n   */\n  maxRetries?: number;\n\n  /**\nAbort signal.\n */\n  abortSignal?: AbortSignal;\n\n  /**\nAdditional headers to include in the request.\nOnly applicable for HTTP-based providers.\n */\n  headers?: Record<string, string>;\n\n  /**\n   * Optional telemetry configuration (experimental).\n   */\n  experimental_telemetry?: TelemetrySettings;\n}): Promise<EmbedResult<VALUE>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { maxRetries },\n  });\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n\n  return recordSpan({\n    name: 'ai.embed',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({ operationName: 'ai.embed', telemetry }),\n        ...baseTelemetryAttributes,\n        'ai.value': { input: () => JSON.stringify(value) },\n      },\n    }),\n    tracer,\n    fn: async span => {\n      const retry = retryWithExponentialBackoff({ maxRetries });\n\n      const { embedding, usage, rawResponse } = await retry(() =>\n        // nested spans to align with the embedMany telemetry data:\n        recordSpan({\n          name: 'ai.embed.doEmbed',\n          attributes: selectTelemetryAttributes({\n            telemetry,\n            attributes: {\n              ...assembleOperationName({\n                operationName: 'ai.embed.doEmbed',\n                telemetry,\n              }),\n              ...baseTelemetryAttributes,\n              // specific settings that only make sense on the outer level:\n              'ai.values': { input: () => [JSON.stringify(value)] },\n            },\n          }),\n          tracer,\n          fn: async doEmbedSpan => {\n            const modelResponse = await model.doEmbed({\n              values: [value],\n              abortSignal,\n              headers,\n            });\n\n            const embedding = modelResponse.embeddings[0];\n            const usage = modelResponse.usage ?? { tokens: NaN };\n\n            doEmbedSpan.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.embeddings': {\n                    output: () =>\n                      modelResponse.embeddings.map(embedding =>\n                        JSON.stringify(embedding),\n                      ),\n                  },\n                  'ai.usage.tokens': usage.tokens,\n                },\n              }),\n            );\n\n            return {\n              embedding,\n              usage,\n              rawResponse: modelResponse.rawResponse,\n            };\n          },\n        }),\n      );\n\n      span.setAttributes(\n        selectTelemetryAttributes({\n          telemetry,\n          attributes: {\n            'ai.embedding': { output: () => JSON.stringify(embedding) },\n            'ai.usage.tokens': usage.tokens,\n          },\n        }),\n      );\n\n      return new DefaultEmbedResult({ value, embedding, usage, rawResponse });\n    },\n  });\n}\n\nclass DefaultEmbedResult<VALUE> implements EmbedResult<VALUE> {\n  readonly value: EmbedResult<VALUE>['value'];\n  readonly embedding: EmbedResult<VALUE>['embedding'];\n  readonly usage: EmbedResult<VALUE>['usage'];\n  readonly rawResponse: EmbedResult<VALUE>['rawResponse'];\n\n  constructor(options: {\n    value: EmbedResult<VALUE>['value'];\n    embedding: EmbedResult<VALUE>['embedding'];\n    usage: EmbedResult<VALUE>['usage'];\n    rawResponse?: EmbedResult<VALUE>['rawResponse'];\n  }) {\n    this.value = options.value;\n    this.embedding = options.embedding;\n    this.usage = options.usage;\n    this.rawResponse = options.rawResponse;\n  }\n}\n", "/**\n * Splits an array into chunks of a specified size.\n *\n * @template T - The type of elements in the array.\n * @param {T[]} array - The array to split.\n * @param {number} chunkSize - The size of each chunk.\n * @returns {T[][]} - A new array containing the chunks.\n */\nexport function splitArray<T>(array: T[], chunkSize: number): T[][] {\n  if (chunkSize <= 0) {\n    throw new Error('chunkSize must be greater than 0');\n  }\n\n  const result = [];\n  for (let i = 0; i < array.length; i += chunkSize) {\n    result.push(array.slice(i, i + chunkSize));\n  }\n\n  return result;\n}\n", "import { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { Embedding, EmbeddingModel } from '../types';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { splitArray } from '../util/split-array';\nimport { EmbedManyResult } from './embed-many-result';\n\n/**\nEmbed several values using an embedding model. The type of the value is defined\nby the embedding model.\n\n`embedMany` automatically splits large requests into smaller chunks if the model\nhas a limit on how many embeddings can be generated in a single call.\n\n@param model - The embedding model to use.\n@param values - The values that should be embedded.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@returns A result object that contains the embeddings, the value, and additional information.\n */\nexport async function embedMany<VALUE>({\n  model,\n  values,\n  maxRetries,\n  abortSignal,\n  headers,\n  experimental_telemetry: telemetry,\n}: {\n  /**\nThe embedding model to use.\n     */\n  model: EmbeddingModel<VALUE>;\n\n  /**\nThe values that should be embedded.\n   */\n  values: Array<VALUE>;\n\n  /**\nMaximum number of retries per embedding model call. Set to 0 to disable retries.\n\n@default 2\n   */\n  maxRetries?: number;\n\n  /**\nAbort signal.\n */\n  abortSignal?: AbortSignal;\n\n  /**\nAdditional headers to include in the request.\nOnly applicable for HTTP-based providers.\n */\n  headers?: Record<string, string>;\n\n  /**\n   * Optional telemetry configuration (experimental).\n   */\n  experimental_telemetry?: TelemetrySettings;\n}): Promise<EmbedManyResult<VALUE>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { maxRetries },\n  });\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n\n  return recordSpan({\n    name: 'ai.embedMany',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({ operationName: 'ai.embedMany', telemetry }),\n        ...baseTelemetryAttributes,\n        // specific settings that only make sense on the outer level:\n        'ai.values': {\n          input: () => values.map(value => JSON.stringify(value)),\n        },\n      },\n    }),\n    tracer,\n    fn: async span => {\n      const retry = retryWithExponentialBackoff({ maxRetries });\n      const maxEmbeddingsPerCall = model.maxEmbeddingsPerCall;\n\n      // the model has not specified limits on\n      // how many embeddings can be generated in a single call\n      if (maxEmbeddingsPerCall == null) {\n        const { embeddings, usage } = await retry(() => {\n          // nested spans to align with the embedMany telemetry data:\n          return recordSpan({\n            name: 'ai.embedMany.doEmbed',\n            attributes: selectTelemetryAttributes({\n              telemetry,\n              attributes: {\n                ...assembleOperationName({\n                  operationName: 'ai.embedMany.doEmbed',\n                  telemetry,\n                }),\n                ...baseTelemetryAttributes,\n                // specific settings that only make sense on the outer level:\n                'ai.values': {\n                  input: () => values.map(value => JSON.stringify(value)),\n                },\n              },\n            }),\n            tracer,\n            fn: async doEmbedSpan => {\n              const modelResponse = await model.doEmbed({\n                values,\n                abortSignal,\n                headers,\n              });\n\n              const embeddings = modelResponse.embeddings;\n              const usage = modelResponse.usage ?? { tokens: NaN };\n\n              doEmbedSpan.setAttributes(\n                selectTelemetryAttributes({\n                  telemetry,\n                  attributes: {\n                    'ai.embeddings': {\n                      output: () =>\n                        embeddings.map(embedding => JSON.stringify(embedding)),\n                    },\n                    'ai.usage.tokens': usage.tokens,\n                  },\n                }),\n              );\n\n              return { embeddings, usage };\n            },\n          });\n        });\n\n        span.setAttributes(\n          selectTelemetryAttributes({\n            telemetry,\n            attributes: {\n              'ai.embeddings': {\n                output: () =>\n                  embeddings.map(embedding => JSON.stringify(embedding)),\n              },\n              'ai.usage.tokens': usage.tokens,\n            },\n          }),\n        );\n\n        return new DefaultEmbedManyResult({ values, embeddings, usage });\n      }\n\n      // split the values into chunks that are small enough for the model:\n      const valueChunks = splitArray(values, maxEmbeddingsPerCall);\n\n      // serially embed the chunks:\n      const embeddings: Array<Embedding> = [];\n      let tokens = 0;\n\n      for (const chunk of valueChunks) {\n        const { embeddings: responseEmbeddings, usage } = await retry(() => {\n          // nested spans to align with the embedMany telemetry data:\n          return recordSpan({\n            name: 'ai.embedMany.doEmbed',\n            attributes: selectTelemetryAttributes({\n              telemetry,\n              attributes: {\n                ...assembleOperationName({\n                  operationName: 'ai.embedMany.doEmbed',\n                  telemetry,\n                }),\n                ...baseTelemetryAttributes,\n                // specific settings that only make sense on the outer level:\n                'ai.values': {\n                  input: () => chunk.map(value => JSON.stringify(value)),\n                },\n              },\n            }),\n            tracer,\n            fn: async doEmbedSpan => {\n              const modelResponse = await model.doEmbed({\n                values: chunk,\n                abortSignal,\n                headers,\n              });\n\n              const embeddings = modelResponse.embeddings;\n              const usage = modelResponse.usage ?? { tokens: NaN };\n\n              doEmbedSpan.setAttributes(\n                selectTelemetryAttributes({\n                  telemetry,\n                  attributes: {\n                    'ai.embeddings': {\n                      output: () =>\n                        embeddings.map(embedding => JSON.stringify(embedding)),\n                    },\n                    'ai.usage.tokens': usage.tokens,\n                  },\n                }),\n              );\n\n              return { embeddings, usage };\n            },\n          });\n        });\n\n        embeddings.push(...responseEmbeddings);\n        tokens += usage.tokens;\n      }\n\n      span.setAttributes(\n        selectTelemetryAttributes({\n          telemetry,\n          attributes: {\n            'ai.embeddings': {\n              output: () =>\n                embeddings.map(embedding => JSON.stringify(embedding)),\n            },\n            'ai.usage.tokens': tokens,\n          },\n        }),\n      );\n\n      return new DefaultEmbedManyResult({\n        values,\n        embeddings,\n        usage: { tokens },\n      });\n    },\n  });\n}\n\nclass DefaultEmbedManyResult<VALUE> implements EmbedManyResult<VALUE> {\n  readonly values: EmbedManyResult<VALUE>['values'];\n  readonly embeddings: EmbedManyResult<VALUE>['embeddings'];\n  readonly usage: EmbedManyResult<VALUE>['usage'];\n\n  constructor(options: {\n    values: EmbedManyResult<VALUE>['values'];\n    embeddings: EmbedManyResult<VALUE>['embeddings'];\n    usage: EmbedManyResult<VALUE>['usage'];\n  }) {\n    this.values = options.values;\n    this.embeddings = options.embeddings;\n    this.usage = options.usage;\n  }\n}\n", "import { NoObjectGeneratedError } from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { CallSettings } from '../prompt/call-settings';\nimport { convertToLanguageModelPrompt } from '../prompt/convert-to-language-model-prompt';\nimport { getValidatedPrompt } from '../prompt/get-validated-prompt';\nimport { prepareCallSettings } from '../prompt/prepare-call-settings';\nimport { Prompt } from '../prompt/prompt';\nimport { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { CallWarning, FinishReason, LanguageModel, LogProbs } from '../types';\nimport { calculateCompletionTokenUsage } from '../types/token-usage';\nimport { prepareResponseHeaders } from '../util/prepare-response-headers';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { Schema, asSchema } from '../util/schema';\nimport { GenerateObjectResult } from './generate-object-result';\nimport { injectJsonSchemaIntoSystem } from './inject-json-schema-into-system';\n\n/**\nGenerate a structured, typed object for a given prompt and schema using a language model.\n\nThis function does not stream the output. If you want to stream the output, use `streamObject` instead.\n\n@param model - The language model to use.\n\n@param schema - The schema of the object that the model should generate.\n@param mode - The mode to use for object generation. Not all models support all modes. Defaults to 'auto'.\n\n@param system - A system message that will be part of the prompt.\n@param prompt - A simple text prompt. You can either use `prompt` or `messages` but not both.\n@param messages - A list of messages. You can either use `prompt` or `messages` but not both.\n\n@param maxTokens - Maximum number of tokens to generate.\n@param temperature - Temperature setting.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topP - Nucleus sampling.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topK - Only sample from the top K options for each subsequent token.\nUsed to remove \"long tail\" low probability responses.\nRecommended for advanced use cases only. You usually only need to use temperature.\n@param presencePenalty - Presence penalty setting.\nIt affects the likelihood of the model to repeat information that is already in the prompt.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param frequencyPenalty - Frequency penalty setting.\nIt affects the likelihood of the model to repeatedly use the same words or phrases.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param seed - The seed (integer) to use for random sampling.\nIf set and supported by the model, calls will generate deterministic results.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@returns\nA result object that contains the generated object, the finish reason, the token usage, and additional information.\n */\nexport async function generateObject<T>({\n  model,\n  schema: inputSchema,\n  mode,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  experimental_telemetry: telemetry,\n  ...settings\n}: Omit<CallSettings, 'stopSequences'> &\n  Prompt & {\n    /**\nThe language model to use.\n     */\n    model: LanguageModel;\n\n    /**\nThe schema of the object that the model should generate.\n     */\n    schema: z.Schema<T> | Schema<T>;\n\n    /**\nThe mode to use for object generation.\n\nThe schema is converted in a JSON schema and used in one of the following ways\n\n- 'auto': The provider will choose the best mode for the model.\n- 'tool': A tool with the JSON schema as parameters is is provided and the provider is instructed to use it.\n- 'json': The JSON schema and an instruction is injected into the prompt. If the provider supports JSON mode, it is enabled. If the provider supports JSON grammars, the grammar is used.\n\nPlease note that most providers do not support all modes.\n\nDefault and recommended: 'auto' (best mode for the model).\n     */\n    mode?: 'auto' | 'json' | 'tool';\n\n    /**\n     * Optional telemetry configuration (experimental).\n     */\n    experimental_telemetry?: TelemetrySettings;\n  }): Promise<DefaultGenerateObjectResult<T>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { ...settings, maxRetries },\n  });\n\n  const schema = asSchema(inputSchema);\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n  return recordSpan({\n    name: 'ai.generateObject',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({\n          operationName: 'ai.generateObject',\n          telemetry,\n        }),\n        ...baseTelemetryAttributes,\n        // specific settings that only make sense on the outer level:\n        'ai.prompt': {\n          input: () => JSON.stringify({ system, prompt, messages }),\n        },\n        'ai.schema': {\n          input: () => JSON.stringify(schema.jsonSchema),\n        },\n        'ai.settings.mode': mode,\n      },\n    }),\n    tracer,\n    fn: async span => {\n      const retry = retryWithExponentialBackoff({ maxRetries });\n\n      // use the default provider mode when the mode is set to 'auto' or unspecified\n      if (mode === 'auto' || mode == null) {\n        mode = model.defaultObjectGenerationMode;\n      }\n\n      let result: string;\n      let finishReason: FinishReason;\n      let usage: Parameters<typeof calculateCompletionTokenUsage>[0];\n      let warnings: CallWarning[] | undefined;\n      let rawResponse: { headers?: Record<string, string> } | undefined;\n      let logprobs: LogProbs | undefined;\n\n      switch (mode) {\n        case 'json': {\n          const validatedPrompt = getValidatedPrompt({\n            system: injectJsonSchemaIntoSystem({\n              system,\n              schema: schema.jsonSchema,\n            }),\n            prompt,\n            messages,\n          });\n\n          const promptMessages = await convertToLanguageModelPrompt({\n            prompt: validatedPrompt,\n            modelSupportsImageUrls: model.supportsImageUrls,\n          });\n\n          const inputFormat = validatedPrompt.type;\n\n          const generateResult = await retry(() =>\n            recordSpan({\n              name: 'ai.generateObject.doGenerate',\n              attributes: selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  ...assembleOperationName({\n                    operationName: 'ai.generateObject.doGenerate',\n                    telemetry,\n                  }),\n                  ...baseTelemetryAttributes,\n                  'ai.prompt.format': {\n                    input: () => inputFormat,\n                  },\n                  'ai.prompt.messages': {\n                    input: () => JSON.stringify(promptMessages),\n                  },\n                  'ai.settings.mode': mode,\n\n                  // standardized gen-ai llm span attributes:\n                  'gen_ai.request.model': model.modelId,\n                  'gen_ai.system': model.provider,\n                  'gen_ai.request.max_tokens': settings.maxTokens,\n                  'gen_ai.request.temperature': settings.temperature,\n                  'gen_ai.request.top_p': settings.topP,\n                },\n              }),\n              tracer,\n              fn: async span => {\n                const result = await model.doGenerate({\n                  mode: { type: 'object-json' },\n                  ...prepareCallSettings(settings),\n                  inputFormat,\n                  prompt: promptMessages,\n                  abortSignal,\n                  headers,\n                });\n\n                if (result.text === undefined) {\n                  throw new NoObjectGeneratedError();\n                }\n\n                // Add response information to the span:\n                span.setAttributes(\n                  selectTelemetryAttributes({\n                    telemetry,\n                    attributes: {\n                      'ai.finishReason': result.finishReason,\n                      'ai.usage.promptTokens': result.usage.promptTokens,\n                      'ai.usage.completionTokens':\n                        result.usage.completionTokens,\n                      'ai.result.object': { output: () => result.text },\n\n                      // standardized gen-ai llm span attributes:\n                      'gen_ai.response.finish_reasons': [result.finishReason],\n                      'gen_ai.usage.prompt_tokens': result.usage.promptTokens,\n                      'gen_ai.usage.completion_tokens':\n                        result.usage.completionTokens,\n                    },\n                  }),\n                );\n\n                return { ...result, objectText: result.text };\n              },\n            }),\n          );\n\n          result = generateResult.objectText;\n          finishReason = generateResult.finishReason;\n          usage = generateResult.usage;\n          warnings = generateResult.warnings;\n          rawResponse = generateResult.rawResponse;\n          logprobs = generateResult.logprobs;\n\n          break;\n        }\n\n        case 'tool': {\n          const validatedPrompt = getValidatedPrompt({\n            system,\n            prompt,\n            messages,\n          });\n\n          const promptMessages = await convertToLanguageModelPrompt({\n            prompt: validatedPrompt,\n            modelSupportsImageUrls: model.supportsImageUrls,\n          });\n          const inputFormat = validatedPrompt.type;\n\n          const generateResult = await retry(() =>\n            recordSpan({\n              name: 'ai.generateObject.doGenerate',\n              attributes: selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  ...assembleOperationName({\n                    operationName: 'ai.generateObject.doGenerate',\n                    telemetry,\n                  }),\n                  ...baseTelemetryAttributes,\n                  'ai.prompt.format': {\n                    input: () => inputFormat,\n                  },\n                  'ai.prompt.messages': {\n                    input: () => JSON.stringify(promptMessages),\n                  },\n                  'ai.settings.mode': mode,\n\n                  // standardized gen-ai llm span attributes:\n                  'gen_ai.request.model': model.modelId,\n                  'gen_ai.system': model.provider,\n                  'gen_ai.request.max_tokens': settings.maxTokens,\n                  'gen_ai.request.temperature': settings.temperature,\n                  'gen_ai.request.top_p': settings.topP,\n                },\n              }),\n              tracer,\n              fn: async span => {\n                const result = await model.doGenerate({\n                  mode: {\n                    type: 'object-tool',\n                    tool: {\n                      type: 'function',\n                      name: 'json',\n                      description: 'Respond with a JSON object.',\n                      parameters: schema.jsonSchema,\n                    },\n                  },\n                  ...prepareCallSettings(settings),\n                  inputFormat,\n                  prompt: promptMessages,\n                  abortSignal,\n                  headers,\n                });\n\n                const objectText = result.toolCalls?.[0]?.args;\n\n                if (objectText === undefined) {\n                  throw new NoObjectGeneratedError();\n                }\n\n                // Add response information to the span:\n                span.setAttributes(\n                  selectTelemetryAttributes({\n                    telemetry,\n                    attributes: {\n                      'ai.finishReason': result.finishReason,\n                      'ai.usage.promptTokens': result.usage.promptTokens,\n                      'ai.usage.completionTokens':\n                        result.usage.completionTokens,\n                      'ai.result.object': { output: () => objectText },\n\n                      // standardized gen-ai llm span attributes:\n                      'gen_ai.response.finish_reasons': [result.finishReason],\n                      'gen_ai.usage.prompt_tokens': result.usage.promptTokens,\n                      'gen_ai.usage.completion_tokens':\n                        result.usage.completionTokens,\n                    },\n                  }),\n                );\n\n                return { ...result, objectText };\n              },\n            }),\n          );\n\n          result = generateResult.objectText;\n          finishReason = generateResult.finishReason;\n          usage = generateResult.usage;\n          warnings = generateResult.warnings;\n          rawResponse = generateResult.rawResponse;\n          logprobs = generateResult.logprobs;\n\n          break;\n        }\n\n        case undefined: {\n          throw new Error(\n            'Model does not have a default object generation mode.',\n          );\n        }\n\n        default: {\n          const _exhaustiveCheck: never = mode;\n          throw new Error(`Unsupported mode: ${_exhaustiveCheck}`);\n        }\n      }\n\n      const parseResult = safeParseJSON({ text: result, schema });\n\n      if (!parseResult.success) {\n        throw parseResult.error;\n      }\n\n      // Add response information to the span:\n      span.setAttributes(\n        selectTelemetryAttributes({\n          telemetry,\n          attributes: {\n            'ai.finishReason': finishReason,\n            'ai.usage.promptTokens': usage.promptTokens,\n            'ai.usage.completionTokens': usage.completionTokens,\n            'ai.result.object': {\n              output: () => JSON.stringify(parseResult.value),\n            },\n          },\n        }),\n      );\n\n      return new DefaultGenerateObjectResult({\n        object: parseResult.value,\n        finishReason,\n        usage: calculateCompletionTokenUsage(usage),\n        warnings,\n        rawResponse,\n        logprobs,\n      });\n    },\n  });\n}\n\nclass DefaultGenerateObjectResult<T> implements GenerateObjectResult<T> {\n  readonly object: GenerateObjectResult<T>['object'];\n  readonly finishReason: GenerateObjectResult<T>['finishReason'];\n  readonly usage: GenerateObjectResult<T>['usage'];\n  readonly warnings: GenerateObjectResult<T>['warnings'];\n  readonly rawResponse: GenerateObjectResult<T>['rawResponse'];\n  readonly logprobs: GenerateObjectResult<T>['logprobs'];\n\n  constructor(options: {\n    object: GenerateObjectResult<T>['object'];\n    finishReason: GenerateObjectResult<T>['finishReason'];\n    usage: GenerateObjectResult<T>['usage'];\n    warnings: GenerateObjectResult<T>['warnings'];\n    rawResponse: GenerateObjectResult<T>['rawResponse'];\n    logprobs: GenerateObjectResult<T>['logprobs'];\n  }) {\n    this.object = options.object;\n    this.finishReason = options.finishReason;\n    this.usage = options.usage;\n    this.warnings = options.warnings;\n    this.rawResponse = options.rawResponse;\n    this.logprobs = options.logprobs;\n  }\n\n  toJsonResponse(init?: ResponseInit): Response {\n    return new Response(JSON.stringify(this.object), {\n      status: init?.status ?? 200,\n      headers: prepareResponseHeaders(init, {\n        contentType: 'application/json; charset=utf-8',\n      }),\n    });\n  }\n}\n\n/**\n * @deprecated Use `generateObject` instead.\n */\nexport const experimental_generateObject = generateObject;\n", "import {\n  LanguageModelV1ImagePart,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1TextPart,\n} from '@ai-sdk/provider';\nimport { getErrorMessage } from '@ai-sdk/provider-utils';\nimport { CoreMessage } from '../prompt/message';\nimport { detectImageMimeType } from '../util/detect-image-mimetype';\nimport { download } from '../util/download';\nimport { ImagePart, TextPart } from './content-part';\nimport { convertDataContentToUint8Array } from './data-content';\nimport { ValidatedPrompt } from './get-validated-prompt';\nimport { InvalidMessageRoleError } from './invalid-message-role-error';\n\nexport async function convertToLanguageModelPrompt({\n  prompt,\n  modelSupportsImageUrls = true,\n  downloadImplementation = download,\n}: {\n  prompt: ValidatedPrompt;\n  modelSupportsImageUrls: boolean | undefined;\n  downloadImplementation?: typeof download;\n}): Promise<LanguageModelV1Prompt> {\n  const languageModelMessages: LanguageModelV1Prompt = [];\n\n  if (prompt.system != null) {\n    languageModelMessages.push({ role: 'system', content: prompt.system });\n  }\n\n  const downloadedImages =\n    modelSupportsImageUrls || prompt.messages == null\n      ? null\n      : await downloadImages(prompt.messages, downloadImplementation);\n\n  const promptType = prompt.type;\n  switch (promptType) {\n    case 'prompt': {\n      languageModelMessages.push({\n        role: 'user',\n        content: [{ type: 'text', text: prompt.prompt }],\n      });\n      break;\n    }\n\n    case 'messages': {\n      languageModelMessages.push(\n        ...prompt.messages.map(\n          (message): LanguageModelV1Message =>\n            convertToLanguageModelMessage(message, downloadedImages),\n        ),\n      );\n      break;\n    }\n\n    default: {\n      const _exhaustiveCheck: never = promptType;\n      throw new Error(`Unsupported prompt type: ${_exhaustiveCheck}`);\n    }\n  }\n\n  return languageModelMessages;\n}\n\n/**\n * Convert a CoreMessage to a LanguageModelV1Message.\n *\n * @param message The CoreMessage to convert.\n * @param downloadedImages A map of image URLs to their downloaded data. Only\n *   available if the model does not support image URLs, null otherwise.\n */\nexport function convertToLanguageModelMessage(\n  message: CoreMessage,\n  downloadedImages: Record<\n    string,\n    { mimeType: string | undefined; data: Uint8Array }\n  > | null,\n): LanguageModelV1Message {\n  const role = message.role;\n  switch (role) {\n    case 'system': {\n      return { role: 'system', content: message.content };\n    }\n\n    case 'user': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'user',\n          content: [{ type: 'text', text: message.content }],\n        };\n      }\n\n      return {\n        role: 'user',\n        content: message.content.map(\n          (part): LanguageModelV1TextPart | LanguageModelV1ImagePart => {\n            switch (part.type) {\n              case 'text': {\n                return part;\n              }\n\n              case 'image': {\n                if (part.image instanceof URL) {\n                  if (downloadedImages == null) {\n                    return {\n                      type: 'image',\n                      image: part.image,\n                      mimeType: part.mimeType,\n                    };\n                  } else {\n                    const downloadedImage =\n                      downloadedImages[part.image.toString()];\n                    return {\n                      type: 'image',\n                      image: downloadedImage.data,\n                      mimeType: part.mimeType ?? downloadedImage.mimeType,\n                    };\n                  }\n                }\n\n                // try to convert string image parts to urls\n                if (typeof part.image === 'string') {\n                  try {\n                    const url = new URL(part.image);\n\n                    switch (url.protocol) {\n                      case 'http:':\n                      case 'https:': {\n                        if (downloadedImages == null) {\n                          return {\n                            type: 'image',\n                            image: url,\n                            mimeType: part.mimeType,\n                          };\n                        } else {\n                          const downloadedImage = downloadedImages[part.image];\n                          return {\n                            type: 'image',\n                            image: downloadedImage.data,\n                            mimeType: part.mimeType ?? downloadedImage.mimeType,\n                          };\n                        }\n                      }\n                      case 'data:': {\n                        try {\n                          const [header, base64Content] = part.image.split(',');\n                          const mimeType = header.split(';')[0].split(':')[1];\n\n                          if (mimeType == null || base64Content == null) {\n                            throw new Error('Invalid data URL format');\n                          }\n\n                          return {\n                            type: 'image',\n                            image:\n                              convertDataContentToUint8Array(base64Content),\n                            mimeType,\n                          };\n                        } catch (error) {\n                          throw new Error(\n                            `Error processing data URL: ${getErrorMessage(\n                              message,\n                            )}`,\n                          );\n                        }\n                      }\n                      default: {\n                        throw new Error(\n                          `Unsupported URL protocol: ${url.protocol}`,\n                        );\n                      }\n                    }\n                  } catch (_ignored) {\n                    // not a URL\n                  }\n                }\n\n                const imageUint8 = convertDataContentToUint8Array(part.image);\n\n                return {\n                  type: 'image',\n                  image: imageUint8,\n                  mimeType: part.mimeType ?? detectImageMimeType(imageUint8),\n                };\n              }\n            }\n          },\n        ),\n      };\n    }\n\n    case 'assistant': {\n      if (typeof message.content === 'string') {\n        return {\n          role: 'assistant',\n          content: [{ type: 'text', text: message.content }],\n        };\n      }\n\n      return {\n        role: 'assistant',\n        content: message.content.filter(\n          // remove empty text parts:\n          part => part.type !== 'text' || part.text !== '',\n        ),\n      };\n    }\n\n    case 'tool': {\n      return message;\n    }\n\n    default: {\n      const _exhaustiveCheck: never = role;\n      throw new InvalidMessageRoleError({ role: _exhaustiveCheck });\n    }\n  }\n}\n\nasync function downloadImages(\n  messages: CoreMessage[],\n  downloadImplementation: typeof download,\n): Promise<Record<string, { mimeType: string | undefined; data: Uint8Array }>> {\n  const urls = messages\n    .filter(message => message.role === 'user')\n    .map(message => message.content)\n    .filter((content): content is Array<TextPart | ImagePart> =>\n      Array.isArray(content),\n    )\n    .flat()\n    .filter((part): part is ImagePart => part.type === 'image')\n    .map(part => part.image)\n    .map(part =>\n      // support string urls in image parts:\n      typeof part === 'string' &&\n      (part.startsWith('http:') || part.startsWith('https:'))\n        ? new URL(part)\n        : part,\n    )\n    .filter((image): image is URL => image instanceof URL);\n\n  // download images in parallel:\n  const downloadedImages = await Promise.all(\n    urls.map(async url => ({\n      url,\n      data: await downloadImplementation({ url }),\n    })),\n  );\n\n  return Object.fromEntries(\n    downloadedImages.map(({ url, data }) => [url.toString(), data]),\n  );\n}\n", "const mimeTypeSignatures = [\n  { mimeType: 'image/gif' as const, bytes: [0x47, 0x49, 0x46] },\n  { mimeType: 'image/png' as const, bytes: [0x89, 0x50, 0x4e, 0x47] },\n  { mimeType: 'image/jpeg' as const, bytes: [0xff, 0xd8] },\n  { mimeType: 'image/webp' as const, bytes: [0x52, 0x49, 0x46, 0x46] },\n];\n\nexport function detectImageMimeType(\n  image: Uint8Array,\n): 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp' | undefined {\n  for (const { bytes, mimeType } of mimeTypeSignatures) {\n    if (\n      image.length >= bytes.length &&\n      bytes.every((byte, index) => image[index] === byte)\n    ) {\n      return mimeType;\n    }\n  }\n\n  return undefined;\n}\n", "import { DownloadError } from '@ai-sdk/provider';\n\nexport async function download({\n  url,\n  fetchImplementation = fetch,\n}: {\n  url: URL;\n  fetchImplementation?: typeof fetch;\n}): Promise<{\n  data: Uint8Array;\n  mimeType: string | undefined;\n}> {\n  const urlText = url.toString();\n  try {\n    const response = await fetchImplementation(urlText);\n\n    if (!response.ok) {\n      throw new DownloadError({\n        url: urlText,\n        statusCode: response.status,\n        statusText: response.statusText,\n      });\n    }\n\n    return {\n      data: new Uint8Array(await response.arrayBuffer()),\n      mimeType: response.headers.get('content-type') ?? undefined,\n    };\n  } catch (error) {\n    if (DownloadError.isDownloadError(error)) {\n      throw error;\n    }\n\n    throw new DownloadError({ url: urlText, cause: error });\n  }\n}\n", "import { InvalidDataContentError } from '@ai-sdk/provider';\nimport {\n  convertBase64ToUint8Array,\n  convertUint8ArrayToBase64,\n} from '@ai-sdk/provider-utils';\n\n/**\nData content. Can either be a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffer.\n */\nexport type DataContent = string | Uint8Array | ArrayBuffer | Buffer;\n\n/**\nConverts data content to a base64-encoded string.\n\n@param content - Data content to convert.\n@returns Base64-encoded string.\n*/\nexport function convertDataContentToBase64String(content: DataContent): string {\n  if (typeof content === 'string') {\n    return content;\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return convertUint8ArrayToBase64(new Uint8Array(content));\n  }\n\n  return convertUint8ArrayToBase64(content);\n}\n\n/**\nConverts data content to a Uint8Array.\n\n@param content - Data content to convert.\n@returns Uint8Array.\n */\nexport function convertDataContentToUint8Array(\n  content: DataContent,\n): Uint8Array {\n  if (content instanceof Uint8Array) {\n    return content;\n  }\n\n  if (typeof content === 'string') {\n    try {\n      return convertBase64ToUint8Array(content);\n    } catch (error) {\n      throw new InvalidDataContentError({\n        message:\n          'Invalid data content. Content string is not a base64-encoded media.',\n        content,\n        cause: error,\n      });\n    }\n  }\n\n  if (content instanceof ArrayBuffer) {\n    return new Uint8Array(content);\n  }\n\n  throw new InvalidDataContentError({ content });\n}\n\n/**\n * Converts a Uint8Array to a string of text.\n *\n * @param uint8Array - The Uint8Array to convert.\n * @returns The converted string.\n */\nexport function convertUint8ArrayToText(uint8Array: Uint8Array): string {\n  try {\n    return new TextDecoder().decode(uint8Array);\n  } catch (error) {\n    throw new Error('Error decoding Uint8Array to text');\n  }\n}\n", "export class InvalidMessageRoleError extends Error {\n  readonly role: string;\n\n  constructor({\n    role,\n    message = `Invalid message role: '${role}'. Must be one of: \"system\", \"user\", \"assistant\", \"tool\".`,\n  }: {\n    role: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidMessageRoleError';\n\n    this.role = role;\n  }\n\n  static isInvalidMessageRoleError(\n    error: unknown,\n  ): error is InvalidMessageRoleError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidMessageRoleError' &&\n      typeof (error as InvalidMessageRoleError).role === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      role: this.role,\n    };\n  }\n}\n", "import { InvalidPromptError } from '@ai-sdk/provider';\nimport { CoreMessage } from './message';\nimport { Prompt } from './prompt';\n\nexport type ValidatedPrompt =\n  | {\n      type: 'prompt';\n      prompt: string;\n      messages: undefined;\n      system?: string;\n    }\n  | {\n      type: 'messages';\n      prompt: undefined;\n      messages: CoreMessage[];\n      system?: string;\n    };\n\nexport function getValidatedPrompt(prompt: Prompt): ValidatedPrompt {\n  if (prompt.prompt == null && prompt.messages == null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt or messages must be defined',\n    });\n  }\n\n  if (prompt.prompt != null && prompt.messages != null) {\n    throw new InvalidPromptError({\n      prompt,\n      message: 'prompt and messages cannot be defined at the same time',\n    });\n  }\n\n  if (prompt.messages != null) {\n    for (const message of prompt.messages) {\n      if (message.role === 'system' && typeof message.content !== 'string') {\n        throw new InvalidPromptError({\n          prompt,\n          message: 'system message content must be a string',\n        });\n      }\n    }\n  }\n\n  return prompt.prompt != null\n    ? {\n        type: 'prompt',\n        prompt: prompt.prompt,\n        messages: undefined,\n        system: prompt.system,\n      }\n    : {\n        type: 'messages',\n        prompt: undefined,\n        messages: prompt.messages!, // only possible case bc of checks above\n        system: prompt.system,\n      };\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { CallSettings } from './call-settings';\n\n/**\n * Validates call settings and sets default values.\n */\nexport function prepareCallSettings({\n  maxTokens,\n  temperature,\n  topP,\n  presencePenalty,\n  frequencyPenalty,\n  stopSequences,\n  seed,\n  maxRetries,\n}: CallSettings): CallSettings {\n  if (maxTokens != null) {\n    if (!Number.isInteger(maxTokens)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be an integer',\n      });\n    }\n\n    if (maxTokens < 1) {\n      throw new InvalidArgumentError({\n        parameter: 'maxTokens',\n        value: maxTokens,\n        message: 'maxTokens must be >= 1',\n      });\n    }\n  }\n\n  if (temperature != null) {\n    if (typeof temperature !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'temperature',\n        value: temperature,\n        message: 'temperature must be a number',\n      });\n    }\n  }\n\n  if (topP != null) {\n    if (typeof topP !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'topP',\n        value: topP,\n        message: 'topP must be a number',\n      });\n    }\n  }\n\n  if (presencePenalty != null) {\n    if (typeof presencePenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'presencePenalty',\n        value: presencePenalty,\n        message: 'presencePenalty must be a number',\n      });\n    }\n  }\n\n  if (frequencyPenalty != null) {\n    if (typeof frequencyPenalty !== 'number') {\n      throw new InvalidArgumentError({\n        parameter: 'frequencyPenalty',\n        value: frequencyPenalty,\n        message: 'frequencyPenalty must be a number',\n      });\n    }\n  }\n\n  if (seed != null) {\n    if (!Number.isInteger(seed)) {\n      throw new InvalidArgumentError({\n        parameter: 'seed',\n        value: seed,\n        message: 'seed must be an integer',\n      });\n    }\n  }\n\n  if (maxRetries != null) {\n    if (!Number.isInteger(maxRetries)) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be an integer',\n      });\n    }\n\n    if (maxRetries < 0) {\n      throw new InvalidArgumentError({\n        parameter: 'maxRetries',\n        value: maxRetries,\n        message: 'maxRetries must be >= 0',\n      });\n    }\n  }\n\n  return {\n    maxTokens,\n    temperature: temperature ?? 0,\n    topP,\n    presencePenalty,\n    frequencyPenalty,\n    stopSequences:\n      stopSequences != null && stopSequences.length > 0\n        ? stopSequences\n        : undefined,\n    seed,\n    maxRetries: maxRetries ?? 2,\n  };\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type CompletionTokenUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingTokenUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateCompletionTokenUsage(usage: {\n  promptTokens: number;\n  completionTokens: number;\n}): CompletionTokenUsage {\n  return {\n    promptTokens: usage.promptTokens,\n    completionTokens: usage.completionTokens,\n    totalTokens: usage.promptTokens + usage.completionTokens,\n  };\n}\n", "export function prepareResponseHeaders(\n  init: ResponseInit | undefined,\n  {\n    contentType,\n    dataStreamVersion,\n  }: { contentType: string; dataStreamVersion?: 'v1' | undefined },\n) {\n  const headers = new Headers(init?.headers ?? {});\n\n  if (!headers.has('Content-Type')) {\n    headers.set('Content-Type', contentType);\n  }\n\n  if (dataStreamVersion !== undefined) {\n    headers.set('X-Vercel-AI-Data-Stream', dataStreamVersion);\n  }\n\n  return headers;\n}\n", "import { Validator, validatorSymbol } from '@ai-sdk/provider-utils';\nimport { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema: z.Schema<OBJECT> | Schema<OBJECT>,\n): Schema<OBJECT> {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\nexport function zodSchema<OBJECT>(zodSchema: z.Schema<OBJECT>): Schema<OBJECT> {\n  return jsonSchema(\n    // we assume that zodToJsonSchema will return a valid JSONSchema7:\n    zodToJsonSchema(zodSchema) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n", "import { JSONSchema7 } from 'json-schema';\n\nconst DEFAULT_SCHEMA_PREFIX = 'JSON schema:';\nconst DEFAULT_SCHEMA_SUFFIX =\n  'You MUST answer with a JSON object that matches the JSON schema above.';\n\nexport function injectJsonSchemaIntoSystem({\n  system,\n  schema,\n  schemaPrefix = DEFAULT_SCHEMA_PREFIX,\n  schemaSuffix = DEFAULT_SCHEMA_SUFFIX,\n}: {\n  system?: string;\n  schema: JSONSchema7;\n  schemaPrefix?: string;\n  schemaSuffix?: string;\n}): string {\n  return [\n    system,\n    system != null ? '' : null, // add a newline if system is not null\n    schemaPrefix,\n    JSON.stringify(schema),\n    schemaSuffix,\n  ]\n    .filter(line => line != null)\n    .join('\\n');\n}\n", "import {\n  LanguageModelV1CallOptions,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport { safeValidateTypes } from '@ai-sdk/provider-utils';\nimport {\n  DeepPartial,\n  isDeepEqualData,\n  parsePartialJson,\n} from '@ai-sdk/ui-utils';\nimport { Span } from '@opentelemetry/api';\nimport { ServerResponse } from 'http';\nimport { z } from 'zod';\nimport { CallSettings } from '../prompt/call-settings';\nimport { convertToLanguageModelPrompt } from '../prompt/convert-to-language-model-prompt';\nimport { getValidatedPrompt } from '../prompt/get-validated-prompt';\nimport { prepareCallSettings } from '../prompt/prepare-call-settings';\nimport { Prompt } from '../prompt/prompt';\nimport { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { CallWarning, LanguageModel } from '../types';\nimport {\n  CompletionTokenUsage,\n  calculateCompletionTokenUsage,\n} from '../types/token-usage';\nimport {\n  AsyncIterableStream,\n  createAsyncIterableStream,\n} from '../util/async-iterable-stream';\nimport { DelayedPromise } from '../util/delayed-promise';\nimport { prepareResponseHeaders } from '../util/prepare-response-headers';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { Schema, asSchema } from '../util/schema';\nimport { injectJsonSchemaIntoSystem } from './inject-json-schema-into-system';\nimport {\n  ObjectStreamInputPart,\n  ObjectStreamPart,\n  StreamObjectResult,\n} from './stream-object-result';\n\n/**\nGenerate a structured, typed object for a given prompt and schema using a language model.\n\nThis function streams the output. If you do not want to stream the output, use `generateObject` instead.\n\n@param model - The language model to use.\n\n@param schema - The schema of the object that the model should generate.\n@param mode - The mode to use for object generation. Not all models support all modes. Defaults to 'auto'.\n\n@param system - A system message that will be part of the prompt.\n@param prompt - A simple text prompt. You can either use `prompt` or `messages` but not both.\n@param messages - A list of messages. You can either use `prompt` or `messages` but not both.\n\n@param maxTokens - Maximum number of tokens to generate.\n@param temperature - Temperature setting.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topP - Nucleus sampling.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topK - Only sample from the top K options for each subsequent token.\nUsed to remove \"long tail\" low probability responses.\nRecommended for advanced use cases only. You usually only need to use temperature.\n@param presencePenalty - Presence penalty setting.\nIt affects the likelihood of the model to repeat information that is already in the prompt.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param frequencyPenalty - Frequency penalty setting.\nIt affects the likelihood of the model to repeatedly use the same words or phrases.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param seed - The seed (integer) to use for random sampling.\nIf set and supported by the model, calls will generate deterministic results.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@return\nA result object for accessing the partial object stream and additional information.\n */\nexport async function streamObject<T>({\n  model,\n  schema: inputSchema,\n  mode,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  experimental_telemetry: telemetry,\n  onFinish,\n  ...settings\n}: Omit<CallSettings, 'stopSequences'> &\n  Prompt & {\n    /**\nThe language model to use.\n     */\n    model: LanguageModel;\n\n    /**\nThe schema of the object that the model should generate.\n */\n    schema: z.Schema<T> | Schema<T>;\n\n    /**\nThe mode to use for object generation.\n\nThe schema is converted in a JSON schema and used in one of the following ways\n\n- 'auto': The provider will choose the best mode for the model.\n- 'tool': A tool with the JSON schema as parameters is is provided and the provider is instructed to use it.\n- 'json': The JSON schema and an instruction is injected into the prompt. If the provider supports JSON mode, it is enabled. If the provider supports JSON grammars, the grammar is used.\n\nPlease note that most providers do not support all modes.\n\nDefault and recommended: 'auto' (best mode for the model).\n     */\n    mode?: 'auto' | 'json' | 'tool';\n\n    /**\nOptional telemetry configuration (experimental).\n     */\n    experimental_telemetry?: TelemetrySettings;\n\n    /**\nCallback that is called when the LLM response and the final object validation are finished.\n     */\n    onFinish?: (event: {\n      /**\nThe token usage of the generated response.\n*/\n      usage: CompletionTokenUsage;\n\n      /**\nThe generated object (typed according to the schema). Can be undefined if the final object does not match the schema.\n   */\n      object: T | undefined;\n\n      /**\nOptional error object. This is e.g. a TypeValidationError when the final object does not match the schema.\n   */\n      error: unknown | undefined;\n\n      /**\nOptional raw response data.\n   */\n      rawResponse?: {\n        /**\nResponse headers.\n     */\n        headers?: Record<string, string>;\n      };\n\n      /**\nWarnings from the model provider (e.g. unsupported settings).\n       */\n      warnings?: CallWarning[];\n    }) => Promise<void> | void;\n  }): Promise<DefaultStreamObjectResult<T>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { ...settings, maxRetries },\n  });\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n\n  const retry = retryWithExponentialBackoff({ maxRetries });\n\n  const schema = asSchema(inputSchema);\n\n  return recordSpan({\n    name: 'ai.streamObject',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({\n          operationName: 'ai.streamObject',\n          telemetry,\n        }),\n        ...baseTelemetryAttributes,\n        // specific settings that only make sense on the outer level:\n        'ai.prompt': {\n          input: () => JSON.stringify({ system, prompt, messages }),\n        },\n        'ai.schema': { input: () => JSON.stringify(schema.jsonSchema) },\n        'ai.settings.mode': mode,\n      },\n    }),\n    tracer,\n    endWhenDone: false,\n    fn: async rootSpan => {\n      // use the default provider mode when the mode is set to 'auto' or unspecified\n      if (mode === 'auto' || mode == null) {\n        mode = model.defaultObjectGenerationMode;\n      }\n\n      let callOptions: LanguageModelV1CallOptions;\n      let transformer: Transformer<\n        LanguageModelV1StreamPart,\n        string | Omit<LanguageModelV1StreamPart, 'text-delta'>\n      >;\n\n      switch (mode) {\n        case 'json': {\n          const validatedPrompt = getValidatedPrompt({\n            system: injectJsonSchemaIntoSystem({\n              system,\n              schema: schema.jsonSchema,\n            }),\n            prompt,\n            messages,\n          });\n\n          callOptions = {\n            mode: { type: 'object-json' },\n            ...prepareCallSettings(settings),\n            inputFormat: validatedPrompt.type,\n            prompt: await convertToLanguageModelPrompt({\n              prompt: validatedPrompt,\n              modelSupportsImageUrls: model.supportsImageUrls,\n            }),\n            abortSignal,\n            headers,\n          };\n\n          transformer = {\n            transform: (chunk, controller) => {\n              switch (chunk.type) {\n                case 'text-delta':\n                  controller.enqueue(chunk.textDelta);\n                  break;\n                case 'finish':\n                case 'error':\n                  controller.enqueue(chunk);\n                  break;\n              }\n            },\n          };\n\n          break;\n        }\n\n        case 'tool': {\n          const validatedPrompt = getValidatedPrompt({\n            system,\n            prompt,\n            messages,\n          });\n\n          callOptions = {\n            mode: {\n              type: 'object-tool',\n              tool: {\n                type: 'function',\n                name: 'json',\n                description: 'Respond with a JSON object.',\n                parameters: schema.jsonSchema,\n              },\n            },\n            ...prepareCallSettings(settings),\n            inputFormat: validatedPrompt.type,\n            prompt: await convertToLanguageModelPrompt({\n              prompt: validatedPrompt,\n              modelSupportsImageUrls: model.supportsImageUrls,\n            }),\n            abortSignal,\n            headers,\n          };\n\n          transformer = {\n            transform(chunk, controller) {\n              switch (chunk.type) {\n                case 'tool-call-delta':\n                  controller.enqueue(chunk.argsTextDelta);\n                  break;\n                case 'finish':\n                case 'error':\n                  controller.enqueue(chunk);\n                  break;\n              }\n            },\n          };\n\n          break;\n        }\n\n        case undefined: {\n          throw new Error(\n            'Model does not have a default object generation mode.',\n          );\n        }\n\n        default: {\n          const _exhaustiveCheck: never = mode;\n          throw new Error(`Unsupported mode: ${_exhaustiveCheck}`);\n        }\n      }\n\n      // const result = await retry(() => model.doStream(callOptions));\n      const {\n        result: { stream, warnings, rawResponse },\n        doStreamSpan,\n      } = await retry(() =>\n        recordSpan({\n          name: 'ai.streamObject.doStream',\n          attributes: selectTelemetryAttributes({\n            telemetry,\n            attributes: {\n              ...assembleOperationName({\n                operationName: 'ai.streamObject.doStream',\n                telemetry,\n              }),\n              ...baseTelemetryAttributes,\n              'ai.prompt.format': {\n                input: () => callOptions.inputFormat,\n              },\n              'ai.prompt.messages': {\n                input: () => JSON.stringify(callOptions.prompt),\n              },\n              'ai.settings.mode': mode,\n\n              // standardized gen-ai llm span attributes:\n              'gen_ai.request.model': model.modelId,\n              'gen_ai.system': model.provider,\n              'gen_ai.request.max_tokens': settings.maxTokens,\n              'gen_ai.request.temperature': settings.temperature,\n              'gen_ai.request.top_p': settings.topP,\n            },\n          }),\n          tracer,\n          endWhenDone: false,\n          fn: async doStreamSpan => ({\n            result: await model.doStream(callOptions),\n            doStreamSpan,\n          }),\n        }),\n      );\n\n      return new DefaultStreamObjectResult({\n        stream: stream.pipeThrough(new TransformStream(transformer)),\n        warnings,\n        rawResponse,\n        schema,\n        onFinish,\n        rootSpan,\n        doStreamSpan,\n        telemetry,\n      });\n    },\n  });\n}\n\nclass DefaultStreamObjectResult<T> implements StreamObjectResult<T> {\n  private readonly originalStream: ReadableStream<ObjectStreamPart<T>>;\n  private readonly objectPromise: DelayedPromise<T>;\n\n  readonly warnings: StreamObjectResult<T>['warnings'];\n  readonly usage: StreamObjectResult<T>['usage'];\n  readonly rawResponse: StreamObjectResult<T>['rawResponse'];\n\n  constructor({\n    stream,\n    warnings,\n    rawResponse,\n    schema,\n    onFinish,\n    rootSpan,\n    doStreamSpan,\n    telemetry,\n  }: {\n    stream: ReadableStream<\n      string | Omit<LanguageModelV1StreamPart, 'text-delta'>\n    >;\n    warnings: StreamObjectResult<T>['warnings'];\n    rawResponse?: StreamObjectResult<T>['rawResponse'];\n    schema: z.Schema<T> | Schema<T>;\n    onFinish: Parameters<typeof streamObject<T>>[0]['onFinish'];\n    rootSpan: Span;\n    doStreamSpan: Span;\n    telemetry: TelemetrySettings | undefined;\n  }) {\n    this.warnings = warnings;\n    this.rawResponse = rawResponse;\n\n    // initialize object promise\n    this.objectPromise = new DelayedPromise<T>();\n\n    // initialize usage promise\n    let resolveUsage: (\n      value: CompletionTokenUsage | PromiseLike<CompletionTokenUsage>,\n    ) => void;\n    this.usage = new Promise<CompletionTokenUsage>(resolve => {\n      resolveUsage = resolve;\n    });\n\n    // store information for onFinish callback:\n    let usage: CompletionTokenUsage | undefined;\n    let finishReason: LanguageModelV1FinishReason | undefined;\n    let object: T | undefined;\n    let error: unknown | undefined;\n\n    // pipe chunks through a transformation stream that extracts metadata:\n    let accumulatedText = '';\n    let delta = '';\n    let latestObject: DeepPartial<T> | undefined = undefined;\n    let firstChunk = true;\n\n    const self = this;\n    this.originalStream = stream.pipeThrough(\n      new TransformStream<string | ObjectStreamInputPart, ObjectStreamPart<T>>({\n        async transform(chunk, controller): Promise<void> {\n          // Telemetry event for first chunk:\n          if (firstChunk) {\n            firstChunk = false;\n            doStreamSpan.addEvent('ai.stream.firstChunk');\n          }\n\n          // process partial text chunks\n          if (typeof chunk === 'string') {\n            accumulatedText += chunk;\n            delta += chunk;\n\n            const currentObject = parsePartialJson(\n              accumulatedText,\n            ) as DeepPartial<T>;\n\n            if (!isDeepEqualData(latestObject, currentObject)) {\n              latestObject = currentObject;\n\n              controller.enqueue({\n                type: 'object',\n                object: currentObject,\n              });\n\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: delta,\n              });\n\n              delta = '';\n            }\n\n            return;\n          }\n\n          switch (chunk.type) {\n            case 'finish': {\n              // send final text delta:\n              if (delta !== '') {\n                controller.enqueue({\n                  type: 'text-delta',\n                  textDelta: delta,\n                });\n              }\n\n              // store finish reason for telemetry:\n              finishReason = chunk.finishReason;\n\n              // store usage for promises and onFinish callback:\n              usage = calculateCompletionTokenUsage(chunk.usage);\n\n              controller.enqueue({ ...chunk, usage });\n\n              // resolve promises that can be resolved now:\n              resolveUsage(usage);\n\n              // resolve the object promise with the latest object:\n              const validationResult = safeValidateTypes({\n                value: latestObject,\n                schema,\n              });\n\n              if (validationResult.success) {\n                object = validationResult.value;\n                self.objectPromise.resolve(object);\n              } else {\n                error = validationResult.error;\n                self.objectPromise.reject(error);\n              }\n\n              break;\n            }\n\n            default: {\n              controller.enqueue(chunk);\n              break;\n            }\n          }\n        },\n\n        // invoke onFinish callback and resolve toolResults promise when the stream is about to close:\n        async flush(controller) {\n          try {\n            const finalUsage = usage ?? {\n              promptTokens: NaN,\n              completionTokens: NaN,\n              totalTokens: NaN,\n            };\n\n            doStreamSpan.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.finishReason': finishReason,\n                  'ai.usage.promptTokens': finalUsage.promptTokens,\n                  'ai.usage.completionTokens': finalUsage.completionTokens,\n                  'ai.result.object': {\n                    output: () => JSON.stringify(object),\n                  },\n\n                  // standardized gen-ai llm span attributes:\n                  'gen_ai.usage.prompt_tokens': finalUsage.promptTokens,\n                  'gen_ai.usage.completion_tokens': finalUsage.completionTokens,\n                  'gen_ai.response.finish_reasons': [finishReason],\n                },\n              }),\n            );\n\n            // finish doStreamSpan before other operations for correct timing:\n            doStreamSpan.end();\n\n            // Add response information to the root span:\n            rootSpan.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.usage.promptTokens': finalUsage.promptTokens,\n                  'ai.usage.completionTokens': finalUsage.completionTokens,\n                  'ai.result.object': {\n                    output: () => JSON.stringify(object),\n                  },\n                },\n              }),\n            );\n\n            // call onFinish callback:\n            await onFinish?.({\n              usage: finalUsage,\n              object,\n              error,\n              rawResponse,\n              warnings,\n            });\n          } catch (error) {\n            controller.error(error);\n          } finally {\n            rootSpan.end();\n          }\n        },\n      }),\n    );\n  }\n\n  get object(): Promise<T> {\n    return this.objectPromise.value;\n  }\n\n  get partialObjectStream(): AsyncIterableStream<DeepPartial<T>> {\n    return createAsyncIterableStream(this.originalStream, {\n      transform(chunk, controller) {\n        switch (chunk.type) {\n          case 'object':\n            controller.enqueue(chunk.object);\n            break;\n\n          case 'text-delta':\n          case 'finish':\n            break;\n\n          case 'error':\n            controller.error(chunk.error);\n            break;\n\n          default: {\n            const _exhaustiveCheck: never = chunk;\n            throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n          }\n        }\n      },\n    });\n  }\n\n  get textStream(): AsyncIterableStream<string> {\n    return createAsyncIterableStream(this.originalStream, {\n      transform(chunk, controller) {\n        switch (chunk.type) {\n          case 'text-delta':\n            controller.enqueue(chunk.textDelta);\n            break;\n\n          case 'object':\n          case 'finish':\n            break;\n\n          case 'error':\n            controller.error(chunk.error);\n            break;\n\n          default: {\n            const _exhaustiveCheck: never = chunk;\n            throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n          }\n        }\n      },\n    });\n  }\n\n  get fullStream(): AsyncIterableStream<ObjectStreamPart<T>> {\n    return createAsyncIterableStream(this.originalStream, {\n      transform(chunk, controller) {\n        controller.enqueue(chunk);\n      },\n    });\n  }\n\n  pipeTextStreamToResponse(\n    response: ServerResponse,\n    init?: { headers?: Record<string, string>; status?: number },\n  ) {\n    response.writeHead(init?.status ?? 200, {\n      'Content-Type': 'text/plain; charset=utf-8',\n      ...init?.headers,\n    });\n\n    const reader = this.textStream\n      .pipeThrough(new TextEncoderStream())\n      .getReader();\n\n    const read = async () => {\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n          response.write(value);\n        }\n      } catch (error) {\n        throw error;\n      } finally {\n        response.end();\n      }\n    };\n\n    read();\n  }\n\n  toTextStreamResponse(init?: ResponseInit): Response {\n    return new Response(this.textStream.pipeThrough(new TextEncoderStream()), {\n      status: init?.status ?? 200,\n      headers: prepareResponseHeaders(init, {\n        contentType: 'text/plain; charset=utf-8',\n      }),\n    });\n  }\n}\n\n/**\n * @deprecated Use `streamObject` instead.\n */\nexport const experimental_streamObject = streamObject;\n", "export type AsyncIterableStream<T> = AsyncIterable<T> & ReadableStream<T>;\n\nexport function createAsyncIterableStream<S, T>(\n  source: ReadableStream<S>,\n  transformer: Transformer<S, T>,\n): AsyncIterableStream<T> {\n  const transformedStream: any = source.pipeThrough(\n    new TransformStream(transformer),\n  );\n\n  transformedStream[Symbol.asyncIterator] = () => {\n    const reader = transformedStream.getReader();\n    return {\n      async next(): Promise<IteratorResult<string>> {\n        const { done, value } = await reader.read();\n        return done ? { done: true, value: undefined } : { done: false, value };\n      },\n    };\n  };\n\n  return transformedStream;\n}\n", "/**\n * Delayed promise. It is only constructed once the value is accessed.\n * This is useful to avoid unhandled promise rejections when the promise is created\n * but not accessed.\n */\nexport class DelayedPromise<T> {\n  private status:\n    | { type: 'pending' }\n    | { type: 'resolved'; value: T }\n    | { type: 'rejected'; error: unknown } = { type: 'pending' };\n  private promise: Promise<T> | undefined;\n  private _resolve: undefined | ((value: T) => void) = undefined;\n  private _reject: undefined | ((error: unknown) => void) = undefined;\n\n  get value(): Promise<T> {\n    if (this.promise) {\n      return this.promise;\n    }\n\n    this.promise = new Promise<T>((resolve, reject) => {\n      if (this.status.type === 'resolved') {\n        resolve(this.status.value);\n      } else if (this.status.type === 'rejected') {\n        reject(this.status.error);\n      }\n\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n\n    return this.promise;\n  }\n\n  resolve(value: T): void {\n    this.status = { type: 'resolved', value };\n\n    if (this.promise) {\n      this._resolve?.(value);\n    }\n  }\n\n  reject(error: unknown): void {\n    this.status = { type: 'rejected', error };\n\n    if (this.promise) {\n      this._reject?.(error);\n    }\n  }\n}\n", "export function isNonEmptyObject(\n  object: Record<string, unknown> | undefined | null,\n): object is Record<string, unknown> {\n  return object != null && Object.keys(object).length > 0;\n}\n", "import {\n  LanguageModelV1FunctionTool,\n  LanguageModelV1ToolChoice,\n} from '@ai-sdk/provider';\nimport { CoreTool } from '../tool/tool';\nimport { CoreToolChoice } from '../types/language-model';\nimport { isNonEmptyObject } from '../util/is-non-empty-object';\nimport { asSchema } from '../util/schema';\n\nexport function prepareToolsAndToolChoice<\n  TOOLS extends Record<string, CoreTool>,\n>({\n  tools,\n  toolChoice,\n}: {\n  tools: TOOLS | undefined;\n  toolChoice: CoreToolChoice<TOOLS> | undefined;\n}): {\n  tools: LanguageModelV1FunctionTool[] | undefined;\n  toolChoice: LanguageModelV1ToolChoice | undefined;\n} {\n  if (!isNonEmptyObject(tools)) {\n    return {\n      tools: undefined,\n      toolChoice: undefined,\n    };\n  }\n\n  return {\n    tools: Object.entries(tools).map(([name, tool]) => ({\n      type: 'function' as const,\n      name,\n      description: tool.description,\n      parameters: asSchema(tool.parameters).jsonSchema,\n    })),\n    toolChoice:\n      toolChoice == null\n        ? { type: 'auto' }\n        : typeof toolChoice === 'string'\n        ? { type: toolChoice }\n        : { type: 'tool' as const, toolName: toolChoice.toolName as string },\n  };\n}\n", "import {\n  InvalidToolArgumentsError,\n  LanguageModelV1FunctionToolCall,\n  NoSuchToolError,\n} from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { CoreTool } from '../tool';\nimport { inferParameters } from '../tool/tool';\nimport { Schema, asSchema } from '../util/schema';\nimport { ValueOf } from '../util/value-of';\n\n/**\nTyped tool call that is returned by generateText and streamText.\nIt contains the tool call ID, the tool name, and the tool arguments.\n */\nexport interface ToolCall<NAME extends string, ARGS> {\n  /**\nID of the tool call. This ID is used to match the tool call with the tool result.\n */\n  toolCallId: string;\n\n  /**\nName of the tool that is being called.\n */\n  toolName: NAME;\n\n  /**\nArguments of the tool call. This is a JSON-serializable object that matches the tool's input schema.\n   */\n  args: ARGS;\n}\n\n// transforms the tools into a tool call union\nexport type ToToolCall<TOOLS extends Record<string, CoreTool>> = ValueOf<{\n  [NAME in keyof TOOLS]: {\n    type: 'tool-call';\n    toolCallId: string;\n    toolName: NAME & string;\n    args: inferParameters<TOOLS[NAME]['parameters']>;\n  };\n}>;\n\nexport type ToToolCallArray<TOOLS extends Record<string, CoreTool>> = Array<\n  ToToolCall<TOOLS>\n>;\n\nexport function parseToolCall<TOOLS extends Record<string, CoreTool>>({\n  toolCall,\n  tools,\n}: {\n  toolCall: LanguageModelV1FunctionToolCall;\n  tools?: TOOLS;\n}): ToToolCall<TOOLS> {\n  const toolName = toolCall.toolName as keyof TOOLS & string;\n\n  if (tools == null) {\n    throw new NoSuchToolError({ toolName: toolCall.toolName });\n  }\n\n  const tool = tools[toolName];\n\n  if (tool == null) {\n    throw new NoSuchToolError({\n      toolName: toolCall.toolName,\n      availableTools: Object.keys(tools),\n    });\n  }\n\n  const parseResult = safeParseJSON({\n    text: toolCall.args,\n    schema: asSchema(tool.parameters) as Schema<\n      inferParameters<TOOLS[keyof TOOLS]['parameters']>\n    >,\n  });\n\n  if (parseResult.success === false) {\n    throw new InvalidToolArgumentsError({\n      toolName,\n      toolArgs: toolCall.args,\n      cause: parseResult.error,\n    });\n  }\n\n  return {\n    type: 'tool-call',\n    toolCallId: toolCall.toolCallId,\n    toolName,\n    args: parseResult.value,\n  };\n}\n", "import { Tracer } from '@opentelemetry/api';\nimport { CoreAssistantMessage, CoreToolMessage } from '../prompt';\nimport { CallSettings } from '../prompt/call-settings';\nimport {\n  convertToLanguageModelMessage,\n  convertToLanguageModelPrompt,\n} from '../prompt/convert-to-language-model-prompt';\nimport { getValidatedPrompt } from '../prompt/get-validated-prompt';\nimport { prepareCallSettings } from '../prompt/prepare-call-settings';\nimport { prepareToolsAndToolChoice } from '../prompt/prepare-tools-and-tool-choice';\nimport { Prompt } from '../prompt/prompt';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { CoreTool } from '../tool/tool';\nimport { CoreToolChoice, LanguageModel } from '../types';\nimport {\n  CompletionTokenUsage,\n  calculateCompletionTokenUsage,\n} from '../types/token-usage';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { GenerateTextResult } from './generate-text-result';\nimport { ToToolCallArray, parseToolCall } from './tool-call';\nimport { ToToolResultArray } from './tool-result';\nimport { assembleOperationName } from '../telemetry/assemble-operation-name';\n\n/**\nGenerate a text and call tools for a given prompt using a language model.\n\nThis function does not stream the output. If you want to stream the output, use `streamText` instead.\n\n@param model - The language model to use.\n\n@param tools - Tools that are accessible to and can be called by the model. The model needs to support calling tools.\n@param toolChoice - The tool choice strategy. Default: 'auto'.\n\n@param system - A system message that will be part of the prompt.\n@param prompt - A simple text prompt. You can either use `prompt` or `messages` but not both.\n@param messages - A list of messages. You can either use `prompt` or `messages` but not both.\n\n@param maxTokens - Maximum number of tokens to generate.\n@param temperature - Temperature setting.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topP - Nucleus sampling.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topK - Only sample from the top K options for each subsequent token.\nUsed to remove \"long tail\" low probability responses.\nRecommended for advanced use cases only. You usually only need to use temperature.\n@param presencePenalty - Presence penalty setting.\nIt affects the likelihood of the model to repeat information that is already in the prompt.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param frequencyPenalty - Frequency penalty setting.\nIt affects the likelihood of the model to repeatedly use the same words or phrases.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param stopSequences - Stop sequences.\nIf set, the model will stop generating text when one of the stop sequences is generated.\n@param seed - The seed (integer) to use for random sampling.\nIf set and supported by the model, calls will generate deterministic results.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@param maxToolRoundtrips - Maximal number of automatic roundtrips for tool calls.\n\n@returns\nA result object that contains the generated text, the results of the tool calls, and additional information.\n */\nexport async function generateText<TOOLS extends Record<string, CoreTool>>({\n  model,\n  tools,\n  toolChoice,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  maxAutomaticRoundtrips = 0,\n  maxToolRoundtrips = maxAutomaticRoundtrips,\n  experimental_telemetry: telemetry,\n  ...settings\n}: CallSettings &\n  Prompt & {\n    /**\nThe language model to use.\n     */\n    model: LanguageModel;\n\n    /**\nThe tools that the model can call. The model needs to support calling tools.\n*/\n    tools?: TOOLS;\n\n    /**\nThe tool choice strategy. Default: 'auto'.\n     */\n    toolChoice?: CoreToolChoice<TOOLS>;\n\n    /**\n@deprecated Use `maxToolRoundtrips` instead.\n     */\n    maxAutomaticRoundtrips?: number;\n\n    /**\nMaximal number of automatic roundtrips for tool calls.\n\nAn automatic tool call roundtrip is another LLM call with the\ntool call results when all tool calls of the last assistant\nmessage have results.\n\nA maximum number is required to prevent infinite loops in the\ncase of misconfigured tools.\n\nBy default, it's set to 0, which will disable the feature.\n     */\n    maxToolRoundtrips?: number;\n\n    /**\n     * Optional telemetry configuration (experimental).\n     */\n    experimental_telemetry?: TelemetrySettings;\n  }): Promise<GenerateTextResult<TOOLS>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { ...settings, maxRetries },\n  });\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n  return recordSpan({\n    name: 'ai.generateText',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({\n          operationName: 'ai.generateText',\n          telemetry,\n        }),\n        ...baseTelemetryAttributes,\n        // specific settings that only make sense on the outer level:\n        'ai.prompt': {\n          input: () => JSON.stringify({ system, prompt, messages }),\n        },\n        'ai.settings.maxToolRoundtrips': maxToolRoundtrips,\n      },\n    }),\n    tracer,\n    fn: async span => {\n      const retry = retryWithExponentialBackoff({ maxRetries });\n      const validatedPrompt = getValidatedPrompt({\n        system,\n        prompt,\n        messages,\n      });\n\n      const mode = {\n        type: 'regular' as const,\n        ...prepareToolsAndToolChoice({ tools, toolChoice }),\n      };\n      const callSettings = prepareCallSettings(settings);\n      const promptMessages = await convertToLanguageModelPrompt({\n        prompt: validatedPrompt,\n        modelSupportsImageUrls: model.supportsImageUrls,\n      });\n\n      let currentModelResponse: Awaited<\n        ReturnType<LanguageModel['doGenerate']>\n      >;\n      let currentToolCalls: ToToolCallArray<TOOLS> = [];\n      let currentToolResults: ToToolResultArray<TOOLS> = [];\n      let roundtripCount = 0;\n      const responseMessages: Array<CoreAssistantMessage | CoreToolMessage> =\n        [];\n      const roundtrips: GenerateTextResult<TOOLS>['roundtrips'] = [];\n      const usage: CompletionTokenUsage = {\n        completionTokens: 0,\n        promptTokens: 0,\n        totalTokens: 0,\n      };\n\n      do {\n        // once we have a roundtrip, we need to switch to messages format:\n        const currentInputFormat =\n          roundtripCount === 0 ? validatedPrompt.type : 'messages';\n\n        currentModelResponse = await retry(() =>\n          recordSpan({\n            name: 'ai.generateText.doGenerate',\n            attributes: selectTelemetryAttributes({\n              telemetry,\n              attributes: {\n                ...assembleOperationName({\n                  operationName: 'ai.generateText.doGenerate',\n                  telemetry,\n                }),\n                ...baseTelemetryAttributes,\n                'ai.prompt.format': { input: () => currentInputFormat },\n                'ai.prompt.messages': {\n                  input: () => JSON.stringify(promptMessages),\n                },\n\n                // standardized gen-ai llm span attributes:\n                'gen_ai.request.model': model.modelId,\n                'gen_ai.system': model.provider,\n                'gen_ai.request.max_tokens': settings.maxTokens,\n                'gen_ai.request.temperature': settings.temperature,\n                'gen_ai.request.top_p': settings.topP,\n              },\n            }),\n            tracer,\n            fn: async span => {\n              const result = await model.doGenerate({\n                mode,\n                ...callSettings,\n                inputFormat: currentInputFormat,\n                prompt: promptMessages,\n                abortSignal,\n                headers,\n              });\n\n              // Add response information to the span:\n              span.setAttributes(\n                selectTelemetryAttributes({\n                  telemetry,\n                  attributes: {\n                    'ai.finishReason': result.finishReason,\n                    'ai.usage.promptTokens': result.usage.promptTokens,\n                    'ai.usage.completionTokens': result.usage.completionTokens,\n                    'ai.result.text': {\n                      output: () => result.text,\n                    },\n                    'ai.result.toolCalls': {\n                      output: () => JSON.stringify(result.toolCalls),\n                    },\n\n                    // standardized gen-ai llm span attributes:\n                    'gen_ai.response.finish_reasons': [result.finishReason],\n                    'gen_ai.usage.prompt_tokens': result.usage.promptTokens,\n                    'gen_ai.usage.completion_tokens':\n                      result.usage.completionTokens,\n                  },\n                }),\n              );\n\n              return result;\n            },\n          }),\n        );\n\n        // parse tool calls:\n        currentToolCalls = (currentModelResponse.toolCalls ?? []).map(\n          modelToolCall => parseToolCall({ toolCall: modelToolCall, tools }),\n        );\n\n        // execute tools:\n        currentToolResults =\n          tools == null\n            ? []\n            : await executeTools({\n                toolCalls: currentToolCalls,\n                tools,\n                tracer,\n                telemetry,\n              });\n\n        // token usage:\n        const currentUsage = calculateCompletionTokenUsage(\n          currentModelResponse.usage,\n        );\n        usage.completionTokens += currentUsage.completionTokens;\n        usage.promptTokens += currentUsage.promptTokens;\n        usage.totalTokens += currentUsage.totalTokens;\n\n        // add roundtrip information:\n        roundtrips.push({\n          text: currentModelResponse.text ?? '',\n          toolCalls: currentToolCalls,\n          toolResults: currentToolResults,\n          finishReason: currentModelResponse.finishReason,\n          usage: currentUsage,\n          warnings: currentModelResponse.warnings,\n          logprobs: currentModelResponse.logprobs,\n        });\n\n        // append to messages for potential next roundtrip:\n        const newResponseMessages = toResponseMessages({\n          text: currentModelResponse.text ?? '',\n          toolCalls: currentToolCalls,\n          toolResults: currentToolResults,\n        });\n        responseMessages.push(...newResponseMessages);\n        promptMessages.push(\n          ...newResponseMessages.map(message =>\n            convertToLanguageModelMessage(message, null),\n          ),\n        );\n      } while (\n        // there are tool calls:\n        currentToolCalls.length > 0 &&\n        // all current tool calls have results:\n        currentToolResults.length === currentToolCalls.length &&\n        // the number of roundtrips is less than the maximum:\n        roundtripCount++ < maxToolRoundtrips\n      );\n\n      // Add response information to the span:\n      span.setAttributes(\n        selectTelemetryAttributes({\n          telemetry,\n          attributes: {\n            'ai.finishReason': currentModelResponse.finishReason,\n            'ai.usage.promptTokens': currentModelResponse.usage.promptTokens,\n            'ai.usage.completionTokens':\n              currentModelResponse.usage.completionTokens,\n            'ai.result.text': {\n              output: () => currentModelResponse.text,\n            },\n            'ai.result.toolCalls': {\n              output: () => JSON.stringify(currentModelResponse.toolCalls),\n            },\n          },\n        }),\n      );\n\n      return new DefaultGenerateTextResult({\n        // Always return a string so that the caller doesn't have to check for undefined.\n        // If they need to check if the model did not return any text,\n        // they can check the length of the string:\n        text: currentModelResponse.text ?? '',\n        toolCalls: currentToolCalls,\n        toolResults: currentToolResults,\n        finishReason: currentModelResponse.finishReason,\n        usage,\n        warnings: currentModelResponse.warnings,\n        rawResponse: currentModelResponse.rawResponse,\n        logprobs: currentModelResponse.logprobs,\n        responseMessages,\n        roundtrips,\n      });\n    },\n  });\n}\n\nasync function executeTools<TOOLS extends Record<string, CoreTool>>({\n  toolCalls,\n  tools,\n  tracer,\n  telemetry,\n}: {\n  toolCalls: ToToolCallArray<TOOLS>;\n  tools: TOOLS;\n  tracer: Tracer;\n  telemetry: TelemetrySettings | undefined;\n}): Promise<ToToolResultArray<TOOLS>> {\n  const toolResults = await Promise.all(\n    toolCalls.map(async toolCall => {\n      const tool = tools[toolCall.toolName];\n\n      if (tool?.execute == null) {\n        return undefined;\n      }\n\n      const result = await recordSpan({\n        name: 'ai.toolCall',\n        attributes: selectTelemetryAttributes({\n          telemetry,\n          attributes: {\n            ...assembleOperationName({\n              operationName: 'ai.toolCall',\n              telemetry,\n            }),\n            'ai.toolCall.name': toolCall.toolName,\n            'ai.toolCall.id': toolCall.toolCallId,\n            'ai.toolCall.args': {\n              output: () => JSON.stringify(toolCall.args),\n            },\n          },\n        }),\n        tracer,\n        fn: async span => {\n          const result = await tool.execute!(toolCall.args);\n\n          try {\n            span.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.toolCall.result': {\n                    output: () => JSON.stringify(result),\n                  },\n                },\n              }),\n            );\n          } catch (ignored) {\n            // JSON stringify might fail if the result is not serializable,\n            // in which case we just ignore it. In the future we might want to\n            // add an optional serialize method to the tool interface and warn\n            // if the result is not serializable.\n          }\n\n          return result;\n        },\n      });\n\n      return {\n        toolCallId: toolCall.toolCallId,\n        toolName: toolCall.toolName,\n        args: toolCall.args,\n        result,\n      } as ToToolResultArray<TOOLS>[number];\n    }),\n  );\n\n  return toolResults.filter(\n    (result): result is NonNullable<typeof result> => result != null,\n  );\n}\n\nclass DefaultGenerateTextResult<TOOLS extends Record<string, CoreTool>>\n  implements GenerateTextResult<TOOLS>\n{\n  readonly text: GenerateTextResult<TOOLS>['text'];\n  readonly toolCalls: GenerateTextResult<TOOLS>['toolCalls'];\n  readonly toolResults: GenerateTextResult<TOOLS>['toolResults'];\n  readonly finishReason: GenerateTextResult<TOOLS>['finishReason'];\n  readonly usage: GenerateTextResult<TOOLS>['usage'];\n  readonly warnings: GenerateTextResult<TOOLS>['warnings'];\n  readonly responseMessages: GenerateTextResult<TOOLS>['responseMessages'];\n  readonly roundtrips: GenerateTextResult<TOOLS>['roundtrips'];\n  readonly rawResponse: GenerateTextResult<TOOLS>['rawResponse'];\n  readonly logprobs: GenerateTextResult<TOOLS>['logprobs'];\n\n  constructor(options: {\n    text: GenerateTextResult<TOOLS>['text'];\n    toolCalls: GenerateTextResult<TOOLS>['toolCalls'];\n    toolResults: GenerateTextResult<TOOLS>['toolResults'];\n    finishReason: GenerateTextResult<TOOLS>['finishReason'];\n    usage: GenerateTextResult<TOOLS>['usage'];\n    warnings: GenerateTextResult<TOOLS>['warnings'];\n    rawResponse?: GenerateTextResult<TOOLS>['rawResponse'];\n    logprobs: GenerateTextResult<TOOLS>['logprobs'];\n    responseMessages: GenerateTextResult<TOOLS>['responseMessages'];\n    roundtrips: GenerateTextResult<TOOLS>['roundtrips'];\n  }) {\n    this.text = options.text;\n    this.toolCalls = options.toolCalls;\n    this.toolResults = options.toolResults;\n    this.finishReason = options.finishReason;\n    this.usage = options.usage;\n    this.warnings = options.warnings;\n    this.rawResponse = options.rawResponse;\n    this.logprobs = options.logprobs;\n    this.responseMessages = options.responseMessages;\n    this.roundtrips = options.roundtrips;\n  }\n}\n\n/**\nConverts the result of a `generateText` call to a list of response messages.\n */\nfunction toResponseMessages<TOOLS extends Record<string, CoreTool>>({\n  text,\n  toolCalls,\n  toolResults,\n}: {\n  text: string;\n  toolCalls: ToToolCallArray<TOOLS>;\n  toolResults: ToToolResultArray<TOOLS>;\n}): Array<CoreAssistantMessage | CoreToolMessage> {\n  const responseMessages: Array<CoreAssistantMessage | CoreToolMessage> = [];\n\n  responseMessages.push({\n    role: 'assistant',\n    content: [{ type: 'text', text }, ...toolCalls],\n  });\n\n  if (toolResults.length > 0) {\n    responseMessages.push({\n      role: 'tool',\n      content: toolResults.map(result => ({\n        type: 'tool-result',\n        toolCallId: result.toolCallId,\n        toolName: result.toolName,\n        result: result.result,\n      })),\n    });\n  }\n\n  return responseMessages;\n}\n\n/**\n * @deprecated Use `generateText` instead.\n */\nexport const experimental_generateText = generateText;\n", "/**\n * Merges two readable streams into a single readable stream, emitting values\n * from each stream as they become available.\n *\n * The first stream is prioritized over the second stream. If both streams have\n * values available, the first stream's value is emitted first.\n *\n * @template VALUE1 - The type of values emitted by the first stream.\n * @template VALUE2 - The type of values emitted by the second stream.\n * @param {ReadableStream<VALUE1>} stream1 - The first readable stream.\n * @param {ReadableStream<VALUE2>} stream2 - The second readable stream.\n * @returns {ReadableStream<VALUE1 | VALUE2>} A new readable stream that emits values from both input streams.\n */\nexport function mergeStreams<VALUE1, VALUE2>(\n  stream1: ReadableStream<VALUE1>,\n  stream2: ReadableStream<VALUE2>,\n): ReadableStream<VALUE1 | VALUE2> {\n  const reader1 = stream1.getReader();\n  const reader2 = stream2.getReader();\n\n  let lastRead1: Promise<ReadableStreamReadResult<VALUE1>> | undefined =\n    undefined;\n  let lastRead2: Promise<ReadableStreamReadResult<VALUE2>> | undefined =\n    undefined;\n\n  let stream1Done = false;\n  let stream2Done = false;\n\n  // only use when stream 2 is done:\n  async function readStream1(\n    controller: ReadableStreamDefaultController<VALUE1 | VALUE2>,\n  ) {\n    try {\n      if (lastRead1 == null) {\n        lastRead1 = reader1.read();\n      }\n\n      const result = await lastRead1;\n      lastRead1 = undefined;\n\n      if (!result.done) {\n        controller.enqueue(result.value);\n      } else {\n        controller.close();\n      }\n    } catch (error) {\n      controller.error(error);\n    }\n  }\n\n  // only use when stream 1 is done:\n  async function readStream2(\n    controller: ReadableStreamDefaultController<VALUE1 | VALUE2>,\n  ) {\n    try {\n      if (lastRead2 == null) {\n        lastRead2 = reader2.read();\n      }\n\n      const result = await lastRead2;\n      lastRead2 = undefined;\n\n      if (!result.done) {\n        controller.enqueue(result.value);\n      } else {\n        controller.close();\n      }\n    } catch (error) {\n      controller.error(error);\n    }\n  }\n\n  return new ReadableStream<VALUE1 | VALUE2>({\n    async pull(controller) {\n      try {\n        // stream 1 is done, we can only read from stream 2:\n        if (stream1Done) {\n          await readStream2(controller);\n          return;\n        }\n\n        // stream 2 is done, we can only read from stream 1:\n        if (stream2Done) {\n          await readStream1(controller);\n          return;\n        }\n\n        // pull the next value from the stream that was read last:\n        if (lastRead1 == null) {\n          lastRead1 = reader1.read();\n        }\n        if (lastRead2 == null) {\n          lastRead2 = reader2.read();\n        }\n\n        // Note on Promise.race (prioritizing stream 1 over stream 2):\n        // If the iterable contains one or more non-promise values and/or an already settled promise,\n        // then Promise.race() will settle to the first of these values found in the iterable.\n        const { result, reader } = await Promise.race([\n          lastRead1.then(result => ({ result, reader: reader1 })),\n          lastRead2.then(result => ({ result, reader: reader2 })),\n        ]);\n\n        if (!result.done) {\n          controller.enqueue(result.value);\n        }\n\n        if (reader === reader1) {\n          lastRead1 = undefined;\n          if (result.done) {\n            // stream 1 is done, we can only read from stream 2:\n            await readStream2(controller);\n            stream1Done = true;\n          }\n        } else {\n          lastRead2 = undefined;\n          // stream 2 is done, we can only read from stream 1:\n          if (result.done) {\n            stream2Done = true;\n            await readStream1(controller);\n          }\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    cancel() {\n      reader1.cancel();\n      reader2.cancel();\n    },\n  });\n}\n", "import { LanguageModelV1StreamPart, NoSuchToolError } from '@ai-sdk/provider';\nimport { generateId } from '@ai-sdk/ui-utils';\nimport { Tracer } from '@opentelemetry/api';\nimport { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { CoreTool } from '../tool';\nimport { calculateCompletionTokenUsage } from '../types/token-usage';\nimport { TextStreamPart } from './stream-text-result';\nimport { parseToolCall } from './tool-call';\n\nexport function runToolsTransformation<TOOLS extends Record<string, CoreTool>>({\n  tools,\n  generatorStream,\n  toolCallStreaming,\n  tracer,\n  telemetry,\n}: {\n  tools: TOOLS | undefined;\n  generatorStream: ReadableStream<LanguageModelV1StreamPart>;\n  toolCallStreaming: boolean;\n  tracer: Tracer;\n  telemetry: TelemetrySettings | undefined;\n}): ReadableStream<TextStreamPart<TOOLS>> {\n  let canClose = false;\n  const outstandingToolCalls = new Set<string>();\n\n  // tool results stream\n  let toolResultsStreamController: ReadableStreamDefaultController<\n    TextStreamPart<TOOLS>\n  > | null = null;\n  const toolResultsStream = new ReadableStream<TextStreamPart<TOOLS>>({\n    start(controller) {\n      toolResultsStreamController = controller;\n    },\n  });\n\n  // keep track of active tool calls\n  const activeToolCalls: Record<string, boolean> = {};\n\n  // forward stream\n  const forwardStream = new TransformStream<\n    LanguageModelV1StreamPart,\n    TextStreamPart<TOOLS>\n  >({\n    transform(\n      chunk: LanguageModelV1StreamPart,\n      controller: TransformStreamDefaultController<TextStreamPart<TOOLS>>,\n    ) {\n      const chunkType = chunk.type;\n\n      switch (chunkType) {\n        // forward:\n        case 'text-delta':\n        case 'error': {\n          controller.enqueue(chunk);\n          break;\n        }\n\n        // forward with less information:\n        case 'tool-call-delta': {\n          if (toolCallStreaming) {\n            if (!activeToolCalls[chunk.toolCallId]) {\n              controller.enqueue({\n                type: 'tool-call-streaming-start',\n                toolCallId: chunk.toolCallId,\n                toolName: chunk.toolName,\n              });\n\n              activeToolCalls[chunk.toolCallId] = true;\n            }\n\n            controller.enqueue({\n              type: 'tool-call-delta',\n              toolCallId: chunk.toolCallId,\n              toolName: chunk.toolName,\n              argsTextDelta: chunk.argsTextDelta,\n            });\n          }\n          break;\n        }\n\n        // process tool call:\n        case 'tool-call': {\n          const toolName = chunk.toolName as keyof TOOLS & string;\n\n          if (tools == null) {\n            toolResultsStreamController!.enqueue({\n              type: 'error',\n              error: new NoSuchToolError({ toolName: chunk.toolName }),\n            });\n            break;\n          }\n\n          const tool = tools[toolName];\n\n          if (tool == null) {\n            toolResultsStreamController!.enqueue({\n              type: 'error',\n              error: new NoSuchToolError({\n                toolName: chunk.toolName,\n                availableTools: Object.keys(tools),\n              }),\n            });\n\n            break;\n          }\n\n          try {\n            const toolCall = parseToolCall({\n              toolCall: chunk,\n              tools,\n            });\n\n            controller.enqueue(toolCall);\n\n            if (tool.execute != null) {\n              const toolExecutionId = generateId(); // use our own id to guarantee uniqueness\n              outstandingToolCalls.add(toolExecutionId);\n\n              // Note: we don't await the tool execution here (by leaving out 'await' on recordSpan),\n              // because we want to process the next chunk as soon as possible.\n              // This is important for the case where the tool execution takes a long time.\n              recordSpan({\n                name: 'ai.toolCall',\n                attributes: selectTelemetryAttributes({\n                  telemetry,\n                  attributes: {\n                    ...assembleOperationName({\n                      operationName: 'ai.toolCall',\n                      telemetry,\n                    }),\n                    'ai.toolCall.name': toolCall.toolName,\n                    'ai.toolCall.id': toolCall.toolCallId,\n                    'ai.toolCall.args': {\n                      output: () => JSON.stringify(toolCall.args),\n                    },\n                  },\n                }),\n                tracer,\n                fn: async span =>\n                  tool.execute!(toolCall.args).then(\n                    (result: any) => {\n                      toolResultsStreamController!.enqueue({\n                        ...toolCall,\n                        type: 'tool-result',\n                        result,\n                      } as any);\n\n                      outstandingToolCalls.delete(toolExecutionId);\n\n                      // close the tool results controller if no more outstanding tool calls\n                      if (canClose && outstandingToolCalls.size === 0) {\n                        toolResultsStreamController!.close();\n                      }\n\n                      // record telemetry\n                      try {\n                        span.setAttributes(\n                          selectTelemetryAttributes({\n                            telemetry,\n                            attributes: {\n                              'ai.toolCall.result': {\n                                output: () => JSON.stringify(result),\n                              },\n                            },\n                          }),\n                        );\n                      } catch (ignored) {\n                        // JSON stringify might fail if the result is not serializable,\n                        // in which case we just ignore it. In the future we might want to\n                        // add an optional serialize method to the tool interface and warn\n                        // if the result is not serializable.\n                      }\n                    },\n                    (error: any) => {\n                      toolResultsStreamController!.enqueue({\n                        type: 'error',\n                        error,\n                      });\n\n                      outstandingToolCalls.delete(toolExecutionId);\n\n                      // close the tool results controller if no more outstanding tool calls\n                      if (canClose && outstandingToolCalls.size === 0) {\n                        toolResultsStreamController!.close();\n                      }\n                    },\n                  ),\n              });\n            }\n          } catch (error) {\n            toolResultsStreamController!.enqueue({\n              type: 'error',\n              error,\n            });\n          }\n\n          break;\n        }\n\n        // process finish:\n        case 'finish': {\n          controller.enqueue({\n            type: 'finish',\n            finishReason: chunk.finishReason,\n            logprobs: chunk.logprobs,\n            usage: calculateCompletionTokenUsage(chunk.usage),\n          });\n          break;\n        }\n\n        default: {\n          const _exhaustiveCheck: never = chunkType;\n          throw new Error(`Unhandled chunk type: ${_exhaustiveCheck}`);\n        }\n      }\n    },\n\n    flush() {\n      canClose = true;\n\n      if (outstandingToolCalls.size === 0) {\n        toolResultsStreamController!.close();\n      }\n    },\n  });\n\n  // combine the generator stream and the tool results stream\n  return new ReadableStream<TextStreamPart<TOOLS>>({\n    async start(controller) {\n      // need to wait for both pipes so there are no dangling promises that\n      // can cause uncaught promise rejections when the stream is aborted\n      return Promise.all([\n        generatorStream.pipeThrough(forwardStream).pipeTo(\n          new WritableStream({\n            write(chunk) {\n              controller.enqueue(chunk);\n            },\n            close() {\n              // the generator stream controller is automatically closed when it's consumed\n            },\n          }),\n        ),\n        toolResultsStream.pipeTo(\n          new WritableStream({\n            write(chunk) {\n              controller.enqueue(chunk);\n            },\n            close() {\n              controller.close();\n            },\n          }),\n        ),\n      ]);\n    },\n  });\n}\n", "import { Span } from '@opentelemetry/api';\nimport { ServerResponse } from 'node:http';\nimport {\n  AIStreamCallbacksAndOptions,\n  StreamData,\n  TextStreamPart,\n  formatStreamPart,\n} from '../../streams';\nimport { CallSettings } from '../prompt/call-settings';\nimport { convertToLanguageModelPrompt } from '../prompt/convert-to-language-model-prompt';\nimport { getValidatedPrompt } from '../prompt/get-validated-prompt';\nimport { prepareCallSettings } from '../prompt/prepare-call-settings';\nimport { prepareToolsAndToolChoice } from '../prompt/prepare-tools-and-tool-choice';\nimport { Prompt } from '../prompt/prompt';\nimport { assembleOperationName } from '../telemetry/assemble-operation-name';\nimport { getBaseTelemetryAttributes } from '../telemetry/get-base-telemetry-attributes';\nimport { getTracer } from '../telemetry/get-tracer';\nimport { recordSpan } from '../telemetry/record-span';\nimport { selectTelemetryAttributes } from '../telemetry/select-telemetry-attributes';\nimport { TelemetrySettings } from '../telemetry/telemetry-settings';\nimport { CoreTool } from '../tool';\nimport {\n  CallWarning,\n  CoreToolChoice,\n  FinishReason,\n  LanguageModel,\n} from '../types';\nimport { CompletionTokenUsage } from '../types/token-usage';\nimport {\n  AsyncIterableStream,\n  createAsyncIterableStream,\n} from '../util/async-iterable-stream';\nimport { mergeStreams } from '../util/merge-streams';\nimport { prepareResponseHeaders } from '../util/prepare-response-headers';\nimport { retryWithExponentialBackoff } from '../util/retry-with-exponential-backoff';\nimport { runToolsTransformation } from './run-tools-transformation';\nimport { StreamTextResult } from './stream-text-result';\nimport { ToToolCall } from './tool-call';\nimport { ToToolResult } from './tool-result';\n\n/**\nGenerate a text and call tools for a given prompt using a language model.\n\nThis function streams the output. If you do not want to stream the output, use `generateText` instead.\n\n@param model - The language model to use.\n@param tools - Tools that are accessible to and can be called by the model. The model needs to support calling tools.\n\n@param system - A system message that will be part of the prompt.\n@param prompt - A simple text prompt. You can either use `prompt` or `messages` but not both.\n@param messages - A list of messages. You can either use `prompt` or `messages` but not both.\n\n@param maxTokens - Maximum number of tokens to generate.\n@param temperature - Temperature setting.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topP - Nucleus sampling.\nThe value is passed through to the provider. The range depends on the provider and model.\nIt is recommended to set either `temperature` or `topP`, but not both.\n@param topK - Only sample from the top K options for each subsequent token.\nUsed to remove \"long tail\" low probability responses.\nRecommended for advanced use cases only. You usually only need to use temperature.\n@param presencePenalty - Presence penalty setting.\nIt affects the likelihood of the model to repeat information that is already in the prompt.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param frequencyPenalty - Frequency penalty setting.\nIt affects the likelihood of the model to repeatedly use the same words or phrases.\nThe value is passed through to the provider. The range depends on the provider and model.\n@param stopSequences - Stop sequences.\nIf set, the model will stop generating text when one of the stop sequences is generated.\n@param seed - The seed (integer) to use for random sampling.\nIf set and supported by the model, calls will generate deterministic results.\n\n@param maxRetries - Maximum number of retries. Set to 0 to disable retries. Default: 2.\n@param abortSignal - An optional abort signal that can be used to cancel the call.\n@param headers - Additional HTTP headers to be sent with the request. Only applicable for HTTP-based providers.\n\n@param onFinish - Callback that is called when the LLM response and all request tool executions\n(for tools that have an `execute` function) are finished.\n\n@return\nA result object for accessing different stream types and additional information.\n */\nexport async function streamText<TOOLS extends Record<string, CoreTool>>({\n  model,\n  tools,\n  toolChoice,\n  system,\n  prompt,\n  messages,\n  maxRetries,\n  abortSignal,\n  headers,\n  experimental_telemetry: telemetry,\n  experimental_toolCallStreaming: toolCallStreaming = false,\n  onFinish,\n  ...settings\n}: CallSettings &\n  Prompt & {\n    /**\nThe language model to use.\n     */\n    model: LanguageModel;\n\n    /**\nThe tools that the model can call. The model needs to support calling tools.\n    */\n    tools?: TOOLS;\n\n    /**\nThe tool choice strategy. Default: 'auto'.\n     */\n    toolChoice?: CoreToolChoice<TOOLS>;\n\n    /**\nOptional telemetry configuration (experimental).\n     */\n    experimental_telemetry?: TelemetrySettings;\n\n    /**\nEnable streaming of tool call deltas as they are generated. Disabled by default.\n     */\n    experimental_toolCallStreaming?: boolean;\n\n    /**\nCallback that is called when the LLM response and all request tool executions\n(for tools that have an `execute` function) are finished.\n     */\n    onFinish?: (event: {\n      /**\nThe reason why the generation finished.\n       */\n      finishReason: FinishReason;\n\n      /**\nThe token usage of the generated response.\n */\n      usage: CompletionTokenUsage;\n\n      /**\nThe full text that has been generated.\n       */\n      text: string;\n\n      /**\nThe tool calls that have been executed.\n       */\n      toolCalls?: ToToolCall<TOOLS>[];\n\n      /**\nThe tool results that have been generated.\n       */\n      toolResults?: ToToolResult<TOOLS>[];\n\n      /**\nOptional raw response data.\n       */\n      rawResponse?: {\n        /**\nResponse headers.\n         */\n        headers?: Record<string, string>;\n      };\n\n      /**\nWarnings from the model provider (e.g. unsupported settings).\n       */\n      warnings?: CallWarning[];\n    }) => Promise<void> | void;\n  }): Promise<DefaultStreamTextResult<TOOLS>> {\n  const baseTelemetryAttributes = getBaseTelemetryAttributes({\n    model,\n    telemetry,\n    headers,\n    settings: { ...settings, maxRetries },\n  });\n\n  const tracer = getTracer({ isEnabled: telemetry?.isEnabled ?? false });\n\n  return recordSpan({\n    name: 'ai.streamText',\n    attributes: selectTelemetryAttributes({\n      telemetry,\n      attributes: {\n        ...assembleOperationName({ operationName: 'ai.streamText', telemetry }),\n        ...baseTelemetryAttributes,\n        // specific settings that only make sense on the outer level:\n        'ai.prompt': {\n          input: () => JSON.stringify({ system, prompt, messages }),\n        },\n      },\n    }),\n    tracer,\n    endWhenDone: false,\n    fn: async rootSpan => {\n      const retry = retryWithExponentialBackoff({ maxRetries });\n      const validatedPrompt = getValidatedPrompt({ system, prompt, messages });\n      const promptMessages = await convertToLanguageModelPrompt({\n        prompt: validatedPrompt,\n        modelSupportsImageUrls: model.supportsImageUrls,\n      });\n\n      const {\n        result: { stream, warnings, rawResponse },\n        doStreamSpan,\n      } = await retry(() =>\n        recordSpan({\n          name: 'ai.streamText.doStream',\n          attributes: selectTelemetryAttributes({\n            telemetry,\n            attributes: {\n              ...assembleOperationName({\n                operationName: 'ai.streamText.doStream',\n                telemetry,\n              }),\n              ...baseTelemetryAttributes,\n              'ai.prompt.format': {\n                input: () => validatedPrompt.type,\n              },\n              'ai.prompt.messages': {\n                input: () => JSON.stringify(promptMessages),\n              },\n\n              // standardized gen-ai llm span attributes:\n              'gen_ai.request.model': model.modelId,\n              'gen_ai.system': model.provider,\n              'gen_ai.request.max_tokens': settings.maxTokens,\n              'gen_ai.request.temperature': settings.temperature,\n              'gen_ai.request.top_p': settings.topP,\n            },\n          }),\n          tracer,\n          endWhenDone: false,\n          fn: async doStreamSpan => {\n            return {\n              result: await model.doStream({\n                mode: {\n                  type: 'regular',\n                  ...prepareToolsAndToolChoice({ tools, toolChoice }),\n                },\n                ...prepareCallSettings(settings),\n                inputFormat: validatedPrompt.type,\n                prompt: promptMessages,\n                abortSignal,\n                headers,\n              }),\n              doStreamSpan,\n            };\n          },\n        }),\n      );\n\n      return new DefaultStreamTextResult({\n        stream: runToolsTransformation({\n          tools,\n          generatorStream: stream,\n          toolCallStreaming,\n          tracer,\n          telemetry,\n        }),\n        warnings,\n        rawResponse,\n        onFinish,\n        rootSpan,\n        doStreamSpan,\n        telemetry,\n      });\n    },\n  });\n}\n\nclass DefaultStreamTextResult<TOOLS extends Record<string, CoreTool>>\n  implements StreamTextResult<TOOLS>\n{\n  private originalStream: ReadableStream<TextStreamPart<TOOLS>>;\n  private onFinish?: Parameters<typeof streamText>[0]['onFinish'];\n\n  readonly warnings: StreamTextResult<TOOLS>['warnings'];\n  readonly usage: StreamTextResult<TOOLS>['usage'];\n  readonly finishReason: StreamTextResult<TOOLS>['finishReason'];\n  readonly text: StreamTextResult<TOOLS>['text'];\n  readonly toolCalls: StreamTextResult<TOOLS>['toolCalls'];\n  readonly toolResults: StreamTextResult<TOOLS>['toolResults'];\n  readonly rawResponse: StreamTextResult<TOOLS>['rawResponse'];\n\n  constructor({\n    stream,\n    warnings,\n    rawResponse,\n    onFinish,\n    rootSpan,\n    doStreamSpan,\n    telemetry,\n  }: {\n    stream: ReadableStream<TextStreamPart<TOOLS>>;\n    warnings: StreamTextResult<TOOLS>['warnings'];\n    rawResponse: StreamTextResult<TOOLS>['rawResponse'];\n    onFinish?: Parameters<typeof streamText>[0]['onFinish'];\n    rootSpan: Span;\n    doStreamSpan: Span;\n    telemetry: TelemetrySettings | undefined;\n  }) {\n    this.warnings = warnings;\n    this.rawResponse = rawResponse;\n    this.onFinish = onFinish;\n\n    // initialize usage promise\n    let resolveUsage: (\n      value: CompletionTokenUsage | PromiseLike<CompletionTokenUsage>,\n    ) => void;\n    this.usage = new Promise<CompletionTokenUsage>(resolve => {\n      resolveUsage = resolve;\n    });\n\n    // initialize finish reason promise\n    let resolveFinishReason: (\n      value: FinishReason | PromiseLike<FinishReason>,\n    ) => void;\n    this.finishReason = new Promise<FinishReason>(resolve => {\n      resolveFinishReason = resolve;\n    });\n\n    // initialize text promise\n    let resolveText: (value: string | PromiseLike<string>) => void;\n    this.text = new Promise<string>(resolve => {\n      resolveText = resolve;\n    });\n\n    // initialize toolCalls promise\n    let resolveToolCalls: (\n      value: ToToolCall<TOOLS>[] | PromiseLike<ToToolCall<TOOLS>[]>,\n    ) => void;\n    this.toolCalls = new Promise<ToToolCall<TOOLS>[]>(resolve => {\n      resolveToolCalls = resolve;\n    });\n\n    // initialize toolResults promise\n    let resolveToolResults: (\n      value: ToToolResult<TOOLS>[] | PromiseLike<ToToolResult<TOOLS>[]>,\n    ) => void;\n    this.toolResults = new Promise<ToToolResult<TOOLS>[]>(resolve => {\n      resolveToolResults = resolve;\n    });\n\n    // store information for onFinish callback:\n    let finishReason: FinishReason | undefined;\n    let usage: CompletionTokenUsage | undefined;\n    let text = '';\n    const toolCalls: ToToolCall<TOOLS>[] = [];\n    const toolResults: ToToolResult<TOOLS>[] = [];\n    let firstChunk = true;\n\n    // pipe chunks through a transformation stream that extracts metadata:\n    const self = this;\n    this.originalStream = stream.pipeThrough(\n      new TransformStream<TextStreamPart<TOOLS>, TextStreamPart<TOOLS>>({\n        async transform(chunk, controller): Promise<void> {\n          controller.enqueue(chunk);\n\n          // Telemetry event for first chunk:\n          if (firstChunk) {\n            firstChunk = false;\n            doStreamSpan.addEvent('ai.stream.firstChunk');\n          }\n\n          const chunkType = chunk.type;\n          switch (chunkType) {\n            case 'text-delta':\n              // create the full text from text deltas (for onFinish callback and text promise):\n              text += chunk.textDelta;\n              break;\n\n            case 'tool-call':\n              // store tool calls for onFinish callback and toolCalls promise:\n              toolCalls.push(chunk);\n              break;\n\n            case 'tool-result':\n              // store tool results for onFinish callback and toolResults promise:\n              toolResults.push(chunk);\n              break;\n\n            case 'finish':\n              // Note: tool executions might not be finished yet when the finish event is emitted.\n              // store usage and finish reason for promises and onFinish callback:\n              usage = chunk.usage;\n              finishReason = chunk.finishReason;\n\n              // resolve promises that can be resolved now:\n              resolveUsage(usage);\n              resolveFinishReason(finishReason);\n              resolveText(text);\n              resolveToolCalls(toolCalls);\n              break;\n\n            case 'tool-call-streaming-start':\n            case 'tool-call-delta':\n            case 'error':\n              // ignored\n              break;\n\n            default: {\n              const exhaustiveCheck: never = chunkType;\n              throw new Error(`Unknown chunk type: ${exhaustiveCheck}`);\n            }\n          }\n        },\n\n        // invoke onFinish callback and resolve toolResults promise when the stream is about to close:\n        async flush(controller) {\n          try {\n            const finalUsage = usage ?? {\n              promptTokens: NaN,\n              completionTokens: NaN,\n              totalTokens: NaN,\n            };\n            const finalFinishReason = finishReason ?? 'unknown';\n            const telemetryToolCalls =\n              toolCalls.length > 0 ? JSON.stringify(toolCalls) : undefined;\n\n            doStreamSpan.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.finishReason': finalFinishReason,\n                  'ai.usage.promptTokens': finalUsage.promptTokens,\n                  'ai.usage.completionTokens': finalUsage.completionTokens,\n                  'ai.result.text': { output: () => text },\n                  'ai.result.toolCalls': { output: () => telemetryToolCalls },\n\n                  // standardized gen-ai llm span attributes:\n                  'gen_ai.response.finish_reasons': [finalFinishReason],\n                  'gen_ai.usage.prompt_tokens': finalUsage.promptTokens,\n                  'gen_ai.usage.completion_tokens': finalUsage.completionTokens,\n                },\n              }),\n            );\n\n            // finish doStreamSpan before other operations for correct timing:\n            doStreamSpan.end();\n\n            // Add response information to the root span:\n            rootSpan.setAttributes(\n              selectTelemetryAttributes({\n                telemetry,\n                attributes: {\n                  'ai.finishReason': finalFinishReason,\n                  'ai.usage.promptTokens': finalUsage.promptTokens,\n                  'ai.usage.completionTokens': finalUsage.completionTokens,\n                  'ai.result.text': { output: () => text },\n                  'ai.result.toolCalls': { output: () => telemetryToolCalls },\n                },\n              }),\n            );\n\n            // resolve toolResults promise:\n            resolveToolResults(toolResults);\n\n            // call onFinish callback:\n            await self.onFinish?.({\n              finishReason: finalFinishReason,\n              usage: finalUsage,\n              text,\n              toolCalls,\n              // The tool results are inferred as a never[] type, because they are\n              // optional and the execute method with an inferred result type is\n              // optional as well. Therefore we need to cast the toolResults to any.\n              // The type exposed to the users will be correctly inferred.\n              toolResults: toolResults as any,\n              rawResponse,\n              warnings,\n            });\n          } catch (error) {\n            controller.error(error);\n          } finally {\n            rootSpan.end();\n          }\n        },\n      }),\n    );\n  }\n\n  /**\nSplit out a new stream from the original stream.\nThe original stream is replaced to allow for further splitting,\nsince we do not know how many times the stream will be split.\n\nNote: this leads to buffering the stream content on the server.\nHowever, the LLM results are expected to be small enough to not cause issues.\n   */\n  private teeStream() {\n    const [stream1, stream2] = this.originalStream.tee();\n    this.originalStream = stream2;\n    return stream1;\n  }\n\n  get textStream(): AsyncIterableStream<string> {\n    return createAsyncIterableStream(this.teeStream(), {\n      transform(chunk, controller) {\n        if (chunk.type === 'text-delta') {\n          // do not stream empty text deltas:\n          if (chunk.textDelta.length > 0) {\n            controller.enqueue(chunk.textDelta);\n          }\n        } else if (chunk.type === 'error') {\n          controller.error(chunk.error);\n        }\n      },\n    });\n  }\n\n  get fullStream(): AsyncIterableStream<TextStreamPart<TOOLS>> {\n    return createAsyncIterableStream(this.teeStream(), {\n      transform(chunk, controller) {\n        if (chunk.type === 'text-delta') {\n          // do not stream empty text deltas:\n          if (chunk.textDelta.length > 0) {\n            controller.enqueue(chunk);\n          }\n        } else {\n          controller.enqueue(chunk);\n        }\n      },\n    });\n  }\n\n  toAIStream(callbacks: AIStreamCallbacksAndOptions = {}) {\n    let aggregatedResponse = '';\n\n    const callbackTransformer = new TransformStream<\n      TextStreamPart<TOOLS>,\n      TextStreamPart<TOOLS>\n    >({\n      async start(): Promise<void> {\n        if (callbacks.onStart) await callbacks.onStart();\n      },\n\n      async transform(chunk, controller): Promise<void> {\n        controller.enqueue(chunk);\n\n        if (chunk.type === 'text-delta') {\n          const textDelta = chunk.textDelta;\n\n          aggregatedResponse += textDelta;\n\n          if (callbacks.onToken) await callbacks.onToken(textDelta);\n          if (callbacks.onText) await callbacks.onText(textDelta);\n        }\n      },\n\n      async flush(): Promise<void> {\n        if (callbacks.onCompletion)\n          await callbacks.onCompletion(aggregatedResponse);\n        if (callbacks.onFinal) await callbacks.onFinal(aggregatedResponse);\n      },\n    });\n\n    const streamPartsTransformer = new TransformStream<\n      TextStreamPart<TOOLS>,\n      string\n    >({\n      transform: async (chunk, controller) => {\n        const chunkType = chunk.type;\n        switch (chunkType) {\n          case 'text-delta':\n            controller.enqueue(formatStreamPart('text', chunk.textDelta));\n            break;\n          case 'tool-call-streaming-start':\n            controller.enqueue(\n              formatStreamPart('tool_call_streaming_start', {\n                toolCallId: chunk.toolCallId,\n                toolName: chunk.toolName,\n              }),\n            );\n            break;\n          case 'tool-call-delta':\n            controller.enqueue(\n              formatStreamPart('tool_call_delta', {\n                toolCallId: chunk.toolCallId,\n                argsTextDelta: chunk.argsTextDelta,\n              }),\n            );\n            break;\n          case 'tool-call':\n            controller.enqueue(\n              formatStreamPart('tool_call', {\n                toolCallId: chunk.toolCallId,\n                toolName: chunk.toolName,\n                args: chunk.args,\n              }),\n            );\n            break;\n          case 'tool-result':\n            controller.enqueue(\n              formatStreamPart('tool_result', {\n                toolCallId: chunk.toolCallId,\n                result: chunk.result,\n              }),\n            );\n            break;\n          case 'error':\n            controller.enqueue(\n              formatStreamPart('error', JSON.stringify(chunk.error)),\n            );\n            break;\n          case 'finish':\n            controller.enqueue(\n              formatStreamPart('finish_message', {\n                finishReason: chunk.finishReason,\n                usage: {\n                  promptTokens: chunk.usage.promptTokens,\n                  completionTokens: chunk.usage.completionTokens,\n                },\n              }),\n            );\n            break;\n          default: {\n            const exhaustiveCheck: never = chunkType;\n            throw new Error(`Unknown chunk type: ${exhaustiveCheck}`);\n          }\n        }\n      },\n    });\n\n    return this.fullStream\n      .pipeThrough(callbackTransformer)\n      .pipeThrough(streamPartsTransformer)\n      .pipeThrough(new TextEncoderStream());\n  }\n\n  pipeAIStreamToResponse(\n    response: ServerResponse,\n    init?: { headers?: Record<string, string>; status?: number },\n  ): void {\n    return this.pipeDataStreamToResponse(response, init);\n  }\n\n  pipeDataStreamToResponse(\n    response: ServerResponse,\n    init?: { headers?: Record<string, string>; status?: number },\n  ) {\n    response.writeHead(init?.status ?? 200, {\n      'Content-Type': 'text/plain; charset=utf-8',\n      ...init?.headers,\n    });\n\n    const reader = this.toAIStream().getReader();\n\n    const read = async () => {\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n          response.write(value);\n        }\n      } catch (error) {\n        throw error;\n      } finally {\n        response.end();\n      }\n    };\n\n    read();\n  }\n\n  pipeTextStreamToResponse(\n    response: ServerResponse,\n    init?: { headers?: Record<string, string>; status?: number },\n  ) {\n    response.writeHead(init?.status ?? 200, {\n      'Content-Type': 'text/plain; charset=utf-8',\n      ...init?.headers,\n    });\n\n    const reader = this.textStream\n      .pipeThrough(new TextEncoderStream())\n      .getReader();\n\n    const read = async () => {\n      try {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n          response.write(value);\n        }\n      } catch (error) {\n        throw error;\n      } finally {\n        response.end();\n      }\n    };\n\n    read();\n  }\n\n  toAIStreamResponse(\n    options?: ResponseInit | { init?: ResponseInit; data?: StreamData },\n  ): Response {\n    return this.toDataStreamResponse(options);\n  }\n\n  toDataStreamResponse(\n    options?: ResponseInit | { init?: ResponseInit; data?: StreamData },\n  ): Response {\n    const init: ResponseInit | undefined =\n      options == null\n        ? undefined\n        : 'init' in options\n        ? options.init\n        : {\n            headers: 'headers' in options ? options.headers : undefined,\n            status: 'status' in options ? options.status : undefined,\n            statusText:\n              'statusText' in options ? options.statusText : undefined,\n          };\n\n    const data: StreamData | undefined =\n      options == null\n        ? undefined\n        : 'data' in options\n        ? options.data\n        : undefined;\n\n    const stream = data\n      ? mergeStreams(data.stream, this.toAIStream())\n      : this.toAIStream();\n\n    return new Response(stream, {\n      status: init?.status ?? 200,\n      statusText: init?.statusText,\n      headers: prepareResponseHeaders(init, {\n        contentType: 'text/plain; charset=utf-8',\n        dataStreamVersion: 'v1',\n      }),\n    });\n  }\n\n  toTextStreamResponse(init?: ResponseInit): Response {\n    return new Response(this.textStream.pipeThrough(new TextEncoderStream()), {\n      status: init?.status ?? 200,\n      headers: prepareResponseHeaders(init, {\n        contentType: 'text/plain; charset=utf-8',\n      }),\n    });\n  }\n}\n\n/**\n * @deprecated Use `streamText` instead.\n */\nexport const experimental_streamText = streamText;\n", "import { Attachment } from '@ai-sdk/ui-utils';\nimport { ImagePart, TextPart } from './content-part';\nimport {\n  convertDataContentToUint8Array,\n  convertUint8ArrayToText,\n} from './data-content';\n\ntype ContentPart = TextPart | ImagePart;\n\n/**\n * Converts a list of attachments to a list of content parts\n * for consumption by ai/core functions.\n * Currently only supports images and text attachments.\n */\nexport function attachmentsToParts(attachments: Attachment[]): ContentPart[] {\n  const parts: ContentPart[] = [];\n\n  for (const attachment of attachments) {\n    let url;\n\n    try {\n      url = new URL(attachment.url);\n    } catch (error) {\n      throw new Error(`Invalid URL: ${attachment.url}`);\n    }\n\n    switch (url.protocol) {\n      case 'http:':\n      case 'https:': {\n        if (attachment.contentType?.startsWith('image/')) {\n          parts.push({ type: 'image', image: url });\n        }\n        break;\n      }\n\n      case 'data:': {\n        let header;\n        let base64Content;\n        let mimeType;\n\n        try {\n          [header, base64Content] = attachment.url.split(',');\n          mimeType = header.split(';')[0].split(':')[1];\n        } catch (error) {\n          throw new Error(`Error processing data URL: ${attachment.url}`);\n        }\n\n        if (mimeType == null || base64Content == null) {\n          throw new Error(`Invalid data URL format: ${attachment.url}`);\n        }\n\n        if (attachment.contentType?.startsWith('image/')) {\n          parts.push({\n            type: 'image',\n            image: convertDataContentToUint8Array(base64Content),\n          });\n        } else if (attachment.contentType?.startsWith('text/')) {\n          parts.push({\n            type: 'text',\n            text: convertUint8ArrayToText(\n              convertDataContentToUint8Array(base64Content),\n            ),\n          });\n        }\n\n        break;\n      }\n\n      default: {\n        throw new Error(`Unsupported URL protocol: ${url.protocol}`);\n      }\n    }\n  }\n\n  return parts;\n}\n", "import { Attachment } from '@ai-sdk/ui-utils';\nimport { ToolResult } from '../generate-text/tool-result';\nimport { CoreMessage } from '../prompt';\nimport { attachmentsToParts } from './attachments-to-parts';\n\n/**\nConverts an array of messages from useChat into an array of CoreMessages that can be used\nwith the AI core functions (e.g. `streamText`).\n */\nexport function convertToCoreMessages(\n  messages: Array<{\n    role: 'user' | 'assistant' | 'system';\n    content: string;\n    toolInvocations?: Array<ToolResult<string, unknown, unknown>>;\n    experimental_attachments?: Attachment[];\n  }>,\n) {\n  const coreMessages: CoreMessage[] = [];\n\n  for (const {\n    role,\n    content,\n    toolInvocations,\n    experimental_attachments,\n  } of messages) {\n    switch (role) {\n      case 'system': {\n        coreMessages.push({\n          role: 'system',\n          content,\n        });\n        break;\n      }\n\n      case 'user': {\n        coreMessages.push({\n          role: 'user',\n          content: experimental_attachments\n            ? [\n                { type: 'text', text: content },\n                ...attachmentsToParts(experimental_attachments),\n              ]\n            : content,\n        });\n        break;\n      }\n\n      case 'assistant': {\n        if (toolInvocations == null) {\n          coreMessages.push({ role: 'assistant', content });\n          break;\n        }\n\n        // assistant message with tool calls\n        coreMessages.push({\n          role: 'assistant',\n          content: [\n            { type: 'text', text: content },\n            ...toolInvocations.map(({ toolCallId, toolName, args }) => ({\n              type: 'tool-call' as const,\n              toolCallId,\n              toolName,\n              args,\n            })),\n          ],\n        });\n\n        // tool message with tool results\n        coreMessages.push({\n          role: 'tool',\n          content: toolInvocations.map(\n            ({ toolCallId, toolName, args, result }) => ({\n              type: 'tool-result' as const,\n              toolCallId,\n              toolName,\n              args,\n              result,\n            }),\n          ),\n        });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unhandled role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return coreMessages;\n}\n", "export class InvalidModelIdError extends Error {\n  readonly id: string;\n\n  constructor({\n    id,\n    message = `Invalid model id: ${id}`,\n  }: {\n    id: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_InvalidModelIdError';\n\n    this.id = id;\n  }\n\n  static isInvalidModelIdError(error: unknown): error is InvalidModelIdError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_InvalidModelIdError' &&\n      typeof (error as InvalidModelIdError).id === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      id: this.id,\n    };\n  }\n}\n", "export class NoSuchModelError extends Error {\n  readonly modelId: string;\n  readonly modelType: string;\n\n  constructor({\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    modelId: string;\n    modelType: string;\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_NoSuchModelError';\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isNoSuchModelError(error: unknown): error is NoSuchModelError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_NoSuchModelError' &&\n      typeof (error as NoSuchModelError).modelId === 'string' &&\n      typeof (error as NoSuchModelError).modelType === 'string'\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      modelId: this.modelId,\n      modelType: this.modelType,\n    };\n  }\n}\n", "export class NoSuchProviderError extends Error {\n  readonly providerId: string;\n  readonly availableProviders: string[];\n\n  constructor({\n    providerId,\n    availableProviders,\n    message = `No such provider: ${providerId} (available providers: ${availableProviders.join()})`,\n  }: {\n    providerId: string;\n    availableProviders: string[];\n    message?: string;\n  }) {\n    super(message);\n\n    this.name = 'AI_NoSuchProviderError';\n\n    this.providerId = providerId;\n    this.availableProviders = availableProviders;\n  }\n\n  static isNoSuchProviderError(error: unknown): error is NoSuchProviderError {\n    return (\n      error instanceof Error &&\n      error.name === 'AI_NoSuchProviderError' &&\n      typeof (error as NoSuchProviderError).providerId === 'string' &&\n      Array.isArray((error as NoSuchProviderError).availableProviders)\n    );\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      stack: this.stack,\n\n      providerId: this.providerId,\n      availableProviders: this.availableProviders,\n    };\n  }\n}\n", "import { EmbeddingModel, LanguageModel } from '../types';\nimport { InvalidModelIdError } from './invalid-model-id-error';\nimport { NoSuchModelError } from './no-such-model-error';\nimport { NoSuchProviderError } from './no-such-provider-error';\n\n/**\nRegistry for managing models. It enables getting a model with a string id.\n */\nexport type experimental_ProviderRegistry = {\n  /**\nReturns the language model with the given id in the format `providerId:modelId`.\nThe model id is then passed to the provider function to get the model.\n\n@param {string} id - The id of the model to return.\n\n@throws {NoSuchModelError} If no model with the given id exists.\n@throws {NoSuchProviderError} If no provider with the given id exists.\n\n@returns {LanguageModel} The language model associated with the id.\n   */\n  languageModel(id: string): LanguageModel;\n\n  /**\nReturns the text embedding model with the given id in the format `providerId:modelId`.\nThe model id is then passed to the provider function to get the model.\n\n@param {string} id - The id of the model to return.\n\n@throws {NoSuchModelError} If no model with the given id exists.\n@throws {NoSuchProviderError} If no provider with the given id exists.\n\n@returns {LanguageModel} The language model associated with the id.\n   */\n  textEmbeddingModel(id: string): EmbeddingModel<string>;\n};\n\n/**\n * @deprecated Use `experimental_ProviderRegistry` instead.\n */\nexport type experimental_ModelRegistry = experimental_ProviderRegistry;\n\n/**\n * Provider for language and text embedding models. Compatible with the\n * provider registry.\n */\ninterface Provider {\n  /**\n   * Returns a language model with the given id.\n   */\n  languageModel?: (modelId: string) => LanguageModel;\n\n  /**\n   * Returns a text embedding model with the given id.\n   */\n  textEmbedding?: (modelId: string) => EmbeddingModel<string>;\n}\n\n/**\n * Creates a registry for the given providers.\n */\nexport function experimental_createProviderRegistry(\n  providers: Record<string, Provider>,\n): experimental_ProviderRegistry {\n  const registry = new DefaultProviderRegistry();\n\n  for (const [id, provider] of Object.entries(providers)) {\n    registry.registerProvider({ id, provider });\n  }\n\n  return registry;\n}\n\n/**\n * @deprecated Use `experimental_createProviderRegistry` instead.\n */\nexport const experimental_createModelRegistry =\n  experimental_createProviderRegistry;\n\nclass DefaultProviderRegistry implements experimental_ProviderRegistry {\n  private providers: Record<string, Provider> = {};\n\n  registerProvider({ id, provider }: { id: string; provider: Provider }): void {\n    this.providers[id] = provider;\n  }\n\n  private getProvider(id: string): Provider {\n    const provider = this.providers[id];\n\n    if (provider == null) {\n      throw new NoSuchProviderError({\n        providerId: id,\n        availableProviders: Object.keys(this.providers),\n      });\n    }\n\n    return provider;\n  }\n\n  private splitId(id: string): [string, string] {\n    const index = id.indexOf(':');\n\n    if (index === -1) {\n      throw new InvalidModelIdError({ id });\n    }\n\n    return [id.slice(0, index), id.slice(index + 1)];\n  }\n\n  languageModel(id: string): LanguageModel {\n    const [providerId, modelId] = this.splitId(id);\n    const model = this.getProvider(providerId).languageModel?.(modelId);\n\n    if (model == null) {\n      throw new NoSuchModelError({ modelId: id, modelType: 'language model' });\n    }\n\n    return model;\n  }\n\n  textEmbeddingModel(id: string): EmbeddingModel<string> {\n    const [providerId, modelId] = this.splitId(id);\n    const model = this.getProvider(providerId).textEmbedding?.(modelId);\n\n    if (model == null) {\n      throw new NoSuchModelError({\n        modelId: id,\n        modelType: 'text embedding model',\n      });\n    }\n\n    return model;\n  }\n}\n", "import { z } from 'zod';\nimport { Schema } from '../util/schema';\n\ntype Parameters = z.ZodTypeAny | Schema<any>;\n\nexport type inferParameters<PARAMETERS extends Parameters> =\n  PARAMETERS extends Schema<any>\n    ? PARAMETERS['_type']\n    : PARAMETERS extends z.ZodTypeAny\n    ? z.infer<PARAMETERS>\n    : never;\n\n/**\nA tool contains the description and the schema of the input that the tool expects.\nThis enables the language model to generate the input.\n\nThe tool can also contain an optional execute function for the actual execution function of the tool.\n */\nexport interface CoreTool<PARAMETERS extends Parameters = any, RESULT = any> {\n  /**\nAn optional description of what the tool does. Will be used by the language model to decide whether to use the tool.\n   */\n  description?: string;\n\n  /**\nThe schema of the input that the tool expects. The language model will use this to generate the input.\nIt is also used to validate the output of the language model.\nUse descriptions to make the input understandable for the language model.\n   */\n  parameters: PARAMETERS;\n\n  /**\nAn async function that is called with the arguments from the tool call and produces a result.\nIf not provided, the tool will not be executed automatically.\n   */\n  execute?: (args: inferParameters<PARAMETERS>) => PromiseLike<RESULT>;\n}\n\n/**\nHelper function for inferring the execute args of a tool.\n */\n// Note: special type inference is needed for the execute function args to make sure they are inferred correctly.\nexport function tool<PARAMETERS extends Parameters, RESULT>(\n  tool: CoreTool<PARAMETERS, RESULT> & {\n    execute: (args: inferParameters<PARAMETERS>) => PromiseLike<RESULT>;\n  },\n): CoreTool<PARAMETERS, RESULT> & {\n  execute: (args: inferParameters<PARAMETERS>) => PromiseLike<RESULT>;\n};\nexport function tool<PARAMETERS extends Parameters, RESULT>(\n  tool: CoreTool<PARAMETERS, RESULT> & {\n    execute?: undefined;\n  },\n): CoreTool<PARAMETERS, RESULT> & {\n  execute: undefined;\n};\nexport function tool<PARAMETERS extends Parameters, RESULT = any>(\n  tool: CoreTool<PARAMETERS, RESULT>,\n): CoreTool<PARAMETERS, RESULT> {\n  return tool;\n}\n\n/**\n * @deprecated Use `CoreTool` instead.\n */\nexport type ExperimentalTool = CoreTool;\n", "export {\n  APICallError,\n  EmptyResponseBodyError,\n  InvalidArgumentError,\n  InvalidDataContentError,\n  InvalidPromptError,\n  InvalidResponseDataError,\n  InvalidToolArgumentsError,\n  JSONParseError,\n  LoadAPIKeyError,\n  NoObjectGeneratedError,\n  NoSuchToolError,\n  RetryError,\n  ToolCallParseError,\n  TypeValidationError,\n  UnsupportedFunctionalityError,\n  UnsupportedJSONSchemaError,\n} from '@ai-sdk/provider';\n", "/**\n * Calculates the cosine similarity between two vectors. This is a useful metric for\n * comparing the similarity of two vectors such as embeddings.\n *\n * @param vector1 - The first vector.\n * @param vector2 - The second vector.\n *\n * @returns The cosine similarity between vector1 and vector2.\n * @throws {Error} If the vectors do not have the same length.\n */\nexport function cosineSimilarity(vector1: number[], vector2: number[]) {\n  if (vector1.length !== vector2.length) {\n    throw new Error(\n      `Vectors must have the same length (vector1: ${vector1.length} elements, vector2: ${vector2.length} elements)`,\n    );\n  }\n\n  return (\n    dotProduct(vector1, vector2) / (magnitude(vector1) * magnitude(vector2))\n  );\n}\n\n/**\n * Calculates the dot product of two vectors.\n * @param vector1 - The first vector.\n * @param vector2 - The second vector.\n * @returns The dot product of vector1 and vector2.\n */\nfunction dotProduct(vector1: number[], vector2: number[]) {\n  return vector1.reduce(\n    (accumulator: number, value: number, index: number) =>\n      accumulator + value * vector2[index]!,\n    0,\n  );\n}\n\n/**\n * Calculates the magnitude of a vector.\n * @param vector - The vector.\n * @returns The magnitude of the vector.\n */\nfunction magnitude(vector: number[]) {\n  return Math.sqrt(dotProduct(vector, vector));\n}\n", "import {\n  createParser,\n  type EventSourceParser,\n  type ParsedEvent,\n  type ReconnectInterval,\n} from 'eventsource-parser';\nimport { OpenAIStreamCallbacks } from './openai-stream';\n\nexport interface FunctionCallPayload {\n  name: string;\n  arguments: Record<string, unknown>;\n}\nexport interface ToolCallPayload {\n  tools: {\n    id: string;\n    type: 'function';\n    func: {\n      name: string;\n      arguments: Record<string, unknown>;\n    };\n  }[];\n}\n\n/**\n * Configuration options and helper callback methods for AIStream stream lifecycle events.\n * @interface\n */\nexport interface AIStreamCallbacksAndOptions {\n  /** `onStart`: Called once when the stream is initialized. */\n  onStart?: () => Promise<void> | void;\n  /** `onCompletion`: Called for each tokenized message. */\n  onCompletion?: (completion: string) => Promise<void> | void;\n  /** `onFinal`: Called once when the stream is closed with the final completion message. */\n  onFinal?: (completion: string) => Promise<void> | void;\n  /** `onToken`: Called for each tokenized message. */\n  onToken?: (token: string) => Promise<void> | void;\n  /** `onText`: Called for each text chunk. */\n  onText?: (text: string) => Promise<void> | void;\n  /**\n   * @deprecated This flag is no longer used and only retained for backwards compatibility.\n   * You can remove it from your code.\n   */\n  experimental_streamData?: boolean;\n}\n\n/**\n * Options for the AIStreamParser.\n * @interface\n * @property {string} event - The event (type) from the server side event stream.\n */\nexport interface AIStreamParserOptions {\n  event?: string;\n}\n\n/**\n * Custom parser for AIStream data.\n * @interface\n * @param {string} data - The data to be parsed.\n * @param {AIStreamParserOptions} options - The options for the parser.\n * @returns {string | void} The parsed data or void.\n */\nexport interface AIStreamParser {\n  (data: string, options: AIStreamParserOptions):\n    | string\n    | void\n    | { isText: false; content: string };\n}\n\n/**\n * Creates a TransformStream that parses events from an EventSource stream using a custom parser.\n * @param {AIStreamParser} customParser - Function to handle event data.\n * @returns {TransformStream<Uint8Array, string>} TransformStream parsing events.\n */\nexport function createEventStreamTransformer(\n  customParser?: AIStreamParser,\n): TransformStream<Uint8Array, string | { isText: false; content: string }> {\n  const textDecoder = new TextDecoder();\n  let eventSourceParser: EventSourceParser;\n\n  return new TransformStream({\n    async start(controller): Promise<void> {\n      eventSourceParser = createParser(\n        (event: ParsedEvent | ReconnectInterval) => {\n          if (\n            ('data' in event &&\n              event.type === 'event' &&\n              event.data === '[DONE]') ||\n            // Replicate doesn't send [DONE] but does send a 'done' event\n            // @see https://replicate.com/docs/streaming\n            (event as any).event === 'done'\n          ) {\n            controller.terminate();\n            return;\n          }\n\n          if ('data' in event) {\n            const parsedMessage = customParser\n              ? customParser(event.data, {\n                  event: event.event,\n                })\n              : event.data;\n            if (parsedMessage) controller.enqueue(parsedMessage);\n          }\n        },\n      );\n    },\n\n    transform(chunk) {\n      eventSourceParser.feed(textDecoder.decode(chunk));\n    },\n  });\n}\n\n/**\n * Creates a transform stream that encodes input messages and invokes optional callback functions.\n * The transform stream uses the provided callbacks to execute custom logic at different stages of the stream's lifecycle.\n * - `onStart`: Called once when the stream is initialized.\n * - `onToken`: Called for each tokenized message.\n * - `onCompletion`: Called every time an AIStream completion message is received. This can occur multiple times when using e.g. OpenAI functions\n * - `onFinal`: Called once when the stream is closed with the final completion message.\n *\n * This function is useful when you want to process a stream of messages and perform specific actions during the stream's lifecycle.\n *\n * @param {AIStreamCallbacksAndOptions} [callbacks] - An object containing the callback functions.\n * @return {TransformStream<string, Uint8Array>} A transform stream that encodes input messages as Uint8Array and allows the execution of custom logic through callbacks.\n *\n * @example\n * const callbacks = {\n *   onStart: async () => console.log('Stream started'),\n *   onToken: async (token) => console.log(`Token: ${token}`),\n *   onCompletion: async (completion) => console.log(`Completion: ${completion}`)\n *   onFinal: async () => data.close()\n * };\n * const transformer = createCallbacksTransformer(callbacks);\n */\nexport function createCallbacksTransformer(\n  cb: AIStreamCallbacksAndOptions | OpenAIStreamCallbacks | undefined,\n): TransformStream<string | { isText: false; content: string }, Uint8Array> {\n  const textEncoder = new TextEncoder();\n  let aggregatedResponse = '';\n  const callbacks = cb || {};\n\n  return new TransformStream({\n    async start(): Promise<void> {\n      if (callbacks.onStart) await callbacks.onStart();\n    },\n\n    async transform(message, controller): Promise<void> {\n      const content = typeof message === 'string' ? message : message.content;\n\n      controller.enqueue(textEncoder.encode(content));\n\n      aggregatedResponse += content;\n\n      if (callbacks.onToken) await callbacks.onToken(content);\n      if (callbacks.onText && typeof message === 'string') {\n        await callbacks.onText(message);\n      }\n    },\n\n    async flush(): Promise<void> {\n      const isOpenAICallbacks = isOfTypeOpenAIStreamCallbacks(callbacks);\n      // If it's OpenAICallbacks, it has an experimental_onFunctionCall which means that the createFunctionCallTransformer\n      // will handle calling onComplete.\n      if (callbacks.onCompletion) {\n        await callbacks.onCompletion(aggregatedResponse);\n      }\n\n      if (callbacks.onFinal && !isOpenAICallbacks) {\n        await callbacks.onFinal(aggregatedResponse);\n      }\n    },\n  });\n}\n\nfunction isOfTypeOpenAIStreamCallbacks(\n  callbacks: AIStreamCallbacksAndOptions | OpenAIStreamCallbacks,\n): callbacks is OpenAIStreamCallbacks {\n  return 'experimental_onFunctionCall' in callbacks;\n}\n/**\n * Returns a stateful function that, when invoked, trims leading whitespace\n * from the input text. The trimming only occurs on the first invocation, ensuring that\n * subsequent calls do not alter the input text. This is particularly useful in scenarios\n * where a text stream is being processed and only the initial whitespace should be removed.\n *\n * @return {function(string): string} A function that takes a string as input and returns a string\n * with leading whitespace removed if it is the first invocation; otherwise, it returns the input unchanged.\n *\n * @example\n * const trimStart = trimStartOfStreamHelper();\n * const output1 = trimStart(\"   text\"); // \"text\"\n * const output2 = trimStart(\"   text\"); // \"   text\"\n *\n */\nexport function trimStartOfStreamHelper(): (text: string) => string {\n  let isStreamStart = true;\n\n  return (text: string): string => {\n    if (isStreamStart) {\n      text = text.trimStart();\n      if (text) isStreamStart = false;\n    }\n    return text;\n  };\n}\n\n/**\n * Returns a ReadableStream created from the response, parsed and handled with custom logic.\n * The stream goes through two transformation stages, first parsing the events and then\n * invoking the provided callbacks.\n *\n * For 2xx HTTP responses:\n * - The function continues with standard stream processing.\n *\n * For non-2xx HTTP responses:\n * - If the response body is defined, it asynchronously extracts and decodes the response body.\n * - It then creates a custom ReadableStream to propagate a detailed error message.\n *\n * @param {Response} response - The response.\n * @param {AIStreamParser} customParser - The custom parser function.\n * @param {AIStreamCallbacksAndOptions} callbacks - The callbacks.\n * @return {ReadableStream} The AIStream.\n * @throws Will throw an error if the response is not OK.\n */\nexport function AIStream(\n  response: Response,\n  customParser?: AIStreamParser,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream<Uint8Array> {\n  if (!response.ok) {\n    if (response.body) {\n      const reader = response.body.getReader();\n      return new ReadableStream({\n        async start(controller) {\n          const { done, value } = await reader.read();\n          if (!done) {\n            const errorText = new TextDecoder().decode(value);\n            controller.error(new Error(`Response error: ${errorText}`));\n          }\n        },\n      });\n    } else {\n      return new ReadableStream({\n        start(controller) {\n          controller.error(new Error('Response error: No response body'));\n        },\n      });\n    }\n  }\n\n  const responseBodyStream = response.body || createEmptyReadableStream();\n\n  return responseBodyStream\n    .pipeThrough(createEventStreamTransformer(customParser))\n    .pipeThrough(createCallbacksTransformer(callbacks));\n}\n\n// outputs lines like\n// 0: chunk\n// 0: more chunk\n// 1: a fct call\n// z: added data from Data\n\n/**\n * Creates an empty ReadableStream that immediately closes upon creation.\n * This function is used as a fallback for creating a ReadableStream when the response body is null or undefined,\n * ensuring that the subsequent pipeline processing doesn't fail due to a lack of a stream.\n *\n * @returns {ReadableStream} An empty and closed ReadableStream instance.\n */\nfunction createEmptyReadableStream(): ReadableStream {\n  return new ReadableStream({\n    start(controller) {\n      controller.close();\n    },\n  });\n}\n\n/**\n * Implements ReadableStream.from(asyncIterable), which isn't documented in MDN and isn't implemented in node.\n * https://github.com/whatwg/streams/commit/8d7a0bf26eb2cc23e884ddbaac7c1da4b91cf2bc\n */\nexport function readableFromAsyncIterable<T>(iterable: AsyncIterable<T>) {\n  let it = iterable[Symbol.asyncIterator]();\n  return new ReadableStream<T>({\n    async pull(controller) {\n      const { done, value } = await it.next();\n      if (done) controller.close();\n      else controller.enqueue(value);\n    },\n\n    async cancel(reason) {\n      await it.return?.(reason);\n    },\n  });\n}\n", "import { JSONValue, formatStreamPart } from '@ai-sdk/ui-utils';\n\n/**\n * A stream wrapper to send custom JSON-encoded data back to the client.\n */\nexport class StreamData {\n  private encoder = new TextEncoder();\n\n  private controller: ReadableStreamController<Uint8Array> | null = null;\n  public stream: ReadableStream<Uint8Array>;\n\n  private isClosed: boolean = false;\n  private warningTimeout: NodeJS.Timeout | null = null;\n\n  constructor() {\n    const self = this;\n\n    this.stream = new ReadableStream({\n      start: async controller => {\n        self.controller = controller;\n\n        // Set a timeout to show a warning if the stream is not closed within 3 seconds\n        if (process.env.NODE_ENV === 'development') {\n          self.warningTimeout = setTimeout(() => {\n            console.warn(\n              'The data stream is hanging. Did you forget to close it with `data.close()`?',\n            );\n          }, 3000);\n        }\n      },\n      pull: controller => {\n        // No-op: we don't need to do anything special on pull\n      },\n      cancel: reason => {\n        this.isClosed = true;\n      },\n    });\n  }\n\n  async close(): Promise<void> {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.close();\n    this.isClosed = true;\n\n    // Clear the warning timeout if the stream is closed\n    if (this.warningTimeout) {\n      clearTimeout(this.warningTimeout);\n    }\n  }\n\n  append(value: JSONValue): void {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.enqueue(\n      this.encoder.encode(formatStreamPart('data', [value])),\n    );\n  }\n\n  appendMessageAnnotation(value: JSONValue): void {\n    if (this.isClosed) {\n      throw new Error('Data Stream has already been closed.');\n    }\n\n    if (!this.controller) {\n      throw new Error('Stream controller is not initialized.');\n    }\n\n    this.controller.enqueue(\n      this.encoder.encode(formatStreamPart('message_annotations', [value])),\n    );\n  }\n}\n\n/**\n * A TransformStream for LLMs that do not have their own transform stream handlers managing encoding (e.g. OpenAIStream has one for function call handling).\n * This assumes every chunk is a 'text' chunk.\n */\nexport function createStreamDataTransformer() {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder();\n  return new TransformStream({\n    transform: async (chunk, controller) => {\n      const message = decoder.decode(chunk);\n      controller.enqueue(encoder.encode(formatStreamPart('text', message)));\n    },\n  });\n}\n\n/**\n@deprecated Use `StreamData` instead.\n */\nexport class experimental_StreamData extends StreamData {}\n", "import {\n  AIStream,\n  readableFromAsyncIterable,\n  type AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\n// from anthropic sdk (Completion)\ninterface CompletionChunk {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * The resulting completion up to and excluding the stop sequences.\n   */\n  completion: string;\n\n  /**\n   * The model that handled the request.\n   */\n  model: string;\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"stop_sequence\"`: we reached a stop sequence — either provided by you via the\n   *   `stop_sequences` parameter, or a stop sequence built into the model\n   * - `\"max_tokens\"`: we exceeded `max_tokens_to_sample` or the model's maximum\n   */\n  stop_reason: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Text Completions, this is always `\"completion\"`.\n   */\n  type: 'completion';\n}\n\ninterface StreamError {\n  error: {\n    type: string;\n    message: string;\n  };\n}\n\ninterface StreamPing {}\n\ntype StreamData = CompletionChunk | StreamError | StreamPing;\n\ninterface Message {\n  id: string;\n  content: Array<ContentBlock>;\n  model: string;\n  role: 'assistant';\n  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | null;\n  stop_sequence: string | null;\n  type: 'message';\n}\n\ninterface ContentBlock {\n  text: string;\n  type: 'text';\n}\n\ninterface TextDelta {\n  text: string;\n  type: 'text_delta';\n}\n\ninterface ContentBlockDeltaEvent {\n  delta: TextDelta;\n  index: number;\n  type: 'content_block_delta';\n}\n\ninterface ContentBlockStartEvent {\n  content_block: ContentBlock;\n  index: number;\n  type: 'content_block_start';\n}\n\ninterface ContentBlockStopEvent {\n  index: number;\n  type: 'content_block_stop';\n}\n\ninterface MessageDeltaEventDelta {\n  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | null;\n  stop_sequence: string | null;\n}\n\ninterface MessageDeltaEvent {\n  delta: MessageDeltaEventDelta;\n  type: 'message_delta';\n}\n\ntype MessageStreamEvent =\n  | MessageStartEvent\n  | MessageDeltaEvent\n  | MessageStopEvent\n  | ContentBlockStartEvent\n  | ContentBlockDeltaEvent\n  | ContentBlockStopEvent;\n\ninterface MessageStartEvent {\n  message: Message;\n  type: 'message_start';\n}\n\ninterface MessageStopEvent {\n  type: 'message_stop';\n}\n\nfunction parseAnthropicStream(): (data: string) => string | void {\n  let previous = '';\n\n  return data => {\n    const json = JSON.parse(data as string) as StreamData;\n\n    // error event\n    if ('error' in json) {\n      throw new Error(`${json.error.type}: ${json.error.message}`);\n    }\n\n    // ping event\n    if (!('completion' in json)) {\n      return;\n    }\n\n    // On API versions older than 2023-06-01,\n    // Anthropic's `completion` field is cumulative unlike OpenAI's\n    // deltas. In order to compute the delta, we must slice out the text\n    // we previously received.\n    const text = json.completion;\n    if (\n      !previous ||\n      (text.length > previous.length && text.startsWith(previous))\n    ) {\n      const delta = text.slice(previous.length);\n      previous = text;\n\n      return delta;\n    }\n\n    return text;\n  };\n}\n\nasync function* streamable(\n  stream: AsyncIterable<CompletionChunk> | AsyncIterable<MessageStreamEvent>,\n) {\n  for await (const chunk of stream) {\n    if ('completion' in chunk) {\n      // completion stream\n      const text = chunk.completion;\n      if (text) yield text;\n    } else if ('delta' in chunk) {\n      // messge stream\n      const { delta } = chunk;\n      if ('text' in delta) {\n        const text = delta.text;\n        if (text) yield text;\n      }\n    }\n  }\n}\n\n/**\n * Accepts either a fetch Response from the Anthropic `POST /v1/complete` endpoint,\n * or the return value of `await client.completions.create({ stream: true })`\n * from the `@anthropic-ai/sdk` package.\n *\n * @deprecated Use the [Anthropic provider](https://sdk.vercel.ai/providers/ai-sdk-providers/anthropic) instead.\n */\nexport function AnthropicStream(\n  res:\n    | Response\n    | AsyncIterable<CompletionChunk>\n    | AsyncIterable<MessageStreamEvent>,\n  cb?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  if (Symbol.asyncIterator in res) {\n    return readableFromAsyncIterable(streamable(res))\n      .pipeThrough(createCallbacksTransformer(cb))\n      .pipeThrough(createStreamDataTransformer());\n  } else {\n    return AIStream(res, parseAnthropicStream(), cb).pipeThrough(\n      createStreamDataTransformer(),\n    );\n  }\n}\n", "import {\n  AssistantMessage,\n  DataMessage,\n  formatStreamPart,\n} from '@ai-sdk/ui-utils';\nimport { type AssistantStream } from 'openai/lib/AssistantStream';\nimport { Run } from 'openai/resources/beta/threads/runs/runs';\n\n/**\nYou can pass the thread and the latest message into the `AssistantResponse`. This establishes the context for the response.\n */\ntype AssistantResponseSettings = {\n  /**\nThe thread ID that the response is associated with.\n   */\n  threadId: string;\n\n  /**\nThe ID of the latest message that the response is associated with.\n */\n  messageId: string;\n};\n\n/**\nThe process parameter is a callback in which you can run the assistant on threads, and send messages and data messages to the client.\n */\ntype AssistantResponseCallback = (options: {\n  /**\n@deprecated use variable from outer scope instead.\n   */\n  threadId: string;\n\n  /**\n@deprecated use variable from outer scope instead.\n   */\n  messageId: string;\n\n  /**\nForwards an assistant message (non-streaming) to the client.\n   */\n  sendMessage: (message: AssistantMessage) => void;\n\n  /**\nSend a data message to the client. You can use this to provide information for rendering custom UIs while the assistant is processing the thread.\n */\n  sendDataMessage: (message: DataMessage) => void;\n\n  /**\nForwards the assistant response stream to the client. Returns the `Run` object after it completes, or when it requires an action.\n   */\n  forwardStream: (stream: AssistantStream) => Promise<Run | undefined>;\n}) => Promise<void>;\n\n/**\nThe `AssistantResponse` allows you to send a stream of assistant update to `useAssistant`.\nIt is designed to facilitate streaming assistant responses to the `useAssistant` hook.\nIt receives an assistant thread and a current message, and can send messages and data messages to the client.\n */\nexport function AssistantResponse(\n  { threadId, messageId }: AssistantResponseSettings,\n  process: AssistantResponseCallback,\n): Response {\n  const stream = new ReadableStream({\n    async start(controller) {\n      const textEncoder = new TextEncoder();\n\n      const sendMessage = (message: AssistantMessage) => {\n        controller.enqueue(\n          textEncoder.encode(formatStreamPart('assistant_message', message)),\n        );\n      };\n\n      const sendDataMessage = (message: DataMessage) => {\n        controller.enqueue(\n          textEncoder.encode(formatStreamPart('data_message', message)),\n        );\n      };\n\n      const sendError = (errorMessage: string) => {\n        controller.enqueue(\n          textEncoder.encode(formatStreamPart('error', errorMessage)),\n        );\n      };\n\n      const forwardStream = async (stream: AssistantStream) => {\n        let result: Run | undefined = undefined;\n\n        for await (const value of stream) {\n          switch (value.event) {\n            case 'thread.message.created': {\n              controller.enqueue(\n                textEncoder.encode(\n                  formatStreamPart('assistant_message', {\n                    id: value.data.id,\n                    role: 'assistant',\n                    content: [{ type: 'text', text: { value: '' } }],\n                  }),\n                ),\n              );\n              break;\n            }\n\n            case 'thread.message.delta': {\n              const content = value.data.delta.content?.[0];\n\n              if (content?.type === 'text' && content.text?.value != null) {\n                controller.enqueue(\n                  textEncoder.encode(\n                    formatStreamPart('text', content.text.value),\n                  ),\n                );\n              }\n\n              break;\n            }\n\n            case 'thread.run.completed':\n            case 'thread.run.requires_action': {\n              result = value.data;\n              break;\n            }\n          }\n        }\n\n        return result;\n      };\n\n      // send the threadId and messageId as the first message:\n      controller.enqueue(\n        textEncoder.encode(\n          formatStreamPart('assistant_control_data', {\n            threadId,\n            messageId,\n          }),\n        ),\n      );\n\n      try {\n        await process({\n          threadId,\n          messageId,\n          sendMessage,\n          sendDataMessage,\n          forwardStream,\n        });\n      } catch (error) {\n        sendError((error as any).message ?? `${error}`);\n      } finally {\n        controller.close();\n      }\n    },\n    pull(controller) {},\n    cancel() {},\n  });\n\n  return new Response(stream, {\n    status: 200,\n    headers: {\n      'Content-Type': 'text/plain; charset=utf-8',\n    },\n  });\n}\n\n/**\n@deprecated Use `AssistantResponse` instead.\n */\nexport const experimental_AssistantResponse = AssistantResponse;\n", "import {\n  AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\ninterface AWSBedrockResponse {\n  body?: AsyncIterable<{\n    chunk?: { bytes?: Uint8Array };\n  }>;\n}\n\nasync function* asDeltaIterable(\n  response: AWSBedrockResponse,\n  extractTextDeltaFromChunk: (chunk: any) => string,\n) {\n  const decoder = new TextDecoder();\n  for await (const chunk of response.body ?? []) {\n    const bytes = chunk.chunk?.bytes;\n\n    if (bytes != null) {\n      const chunkText = decoder.decode(bytes);\n      const chunkJSON = JSON.parse(chunkText);\n      const delta = extractTextDeltaFromChunk(chunkJSON);\n\n      if (delta != null) {\n        yield delta;\n      }\n    }\n  }\n}\n\nexport function AWSBedrockAnthropicMessagesStream(\n  response: AWSBedrockResponse,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return AWSBedrockStream(response, callbacks, chunk => chunk.delta?.text);\n}\n\nexport function AWSBedrockAnthropicStream(\n  response: AWSBedrockResponse,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return AWSBedrockStream(response, callbacks, chunk => chunk.completion);\n}\n\nexport function AWSBedrockCohereStream(\n  response: AWSBedrockResponse,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return AWSBedrockStream(response, callbacks, chunk => chunk?.text);\n}\n\nexport function AWSBedrockLlama2Stream(\n  response: AWSBedrockResponse,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return AWSBedrockStream(response, callbacks, chunk => chunk.generation);\n}\n\nexport function AWSBedrockStream(\n  response: AWSBedrockResponse,\n  callbacks: AIStreamCallbacksAndOptions | undefined,\n  extractTextDeltaFromChunk: (chunk: any) => string,\n) {\n  return readableFromAsyncIterable(\n    asDeltaIterable(response, extractTextDeltaFromChunk),\n  )\n    .pipeThrough(createCallbacksTransformer(callbacks))\n    .pipeThrough(createStreamDataTransformer());\n}\n", "import {\n  type AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\nconst utf8Decoder = new TextDecoder('utf-8');\n\n// Full types\n// @see: https://github.com/cohere-ai/cohere-typescript/blob/c2eceb4a845098240ba0bc44e3787ccf75e268e8/src/api/types/StreamedChatResponse.ts\ninterface StreamChunk {\n  text?: string;\n  eventType:\n    | 'stream-start'\n    | 'search-queries-generation'\n    | 'search-results'\n    | 'text-generation'\n    | 'citation-generation'\n    | 'stream-end';\n}\n\nasync function processLines(\n  lines: string[],\n  controller: ReadableStreamDefaultController<string>,\n) {\n  for (const line of lines) {\n    const { text, is_finished } = JSON.parse(line);\n\n    // closing the reader is handed in readAndProcessLines\n    if (!is_finished) {\n      controller.enqueue(text);\n    }\n  }\n}\n\nasync function readAndProcessLines(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  controller: ReadableStreamDefaultController<string>,\n) {\n  let segment = '';\n\n  while (true) {\n    const { value: chunk, done } = await reader.read();\n    if (done) {\n      break;\n    }\n\n    segment += utf8Decoder.decode(chunk, { stream: true });\n\n    const linesArray = segment.split(/\\r\\n|\\n|\\r/g);\n    segment = linesArray.pop() || '';\n\n    await processLines(linesArray, controller);\n  }\n\n  if (segment) {\n    const linesArray = [segment];\n    await processLines(linesArray, controller);\n  }\n\n  controller.close();\n}\n\nfunction createParser(res: Response) {\n  const reader = res.body?.getReader();\n\n  return new ReadableStream<string>({\n    async start(controller): Promise<void> {\n      if (!reader) {\n        controller.close();\n        return;\n      }\n\n      await readAndProcessLines(reader, controller);\n    },\n  });\n}\n\nasync function* streamable(stream: AsyncIterable<StreamChunk>) {\n  for await (const chunk of stream) {\n    if (chunk.eventType === 'text-generation') {\n      const text = chunk.text;\n      if (text) yield text;\n    }\n  }\n}\n\nexport function CohereStream(\n  reader: Response | AsyncIterable<StreamChunk>,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  if (Symbol.asyncIterator in reader) {\n    return readableFromAsyncIterable(streamable(reader))\n      .pipeThrough(createCallbacksTransformer(callbacks))\n      .pipeThrough(createStreamDataTransformer());\n  } else {\n    return createParser(reader)\n      .pipeThrough(createCallbacksTransformer(callbacks))\n      .pipeThrough(createStreamDataTransformer());\n  }\n}\n", "import {\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n  type AIStreamCallbacksAndOptions,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\ninterface GenerateContentResponse {\n  candidates?: GenerateContentCandidate[];\n}\n\ninterface GenerateContentCandidate {\n  index: number;\n  content: Content;\n}\n\ninterface Content {\n  role: string;\n  parts: Part[];\n}\n\ntype Part = TextPart | InlineDataPart;\n\ninterface InlineDataPart {\n  text?: never;\n}\n\ninterface TextPart {\n  text: string;\n  inlineData?: never;\n}\n\nasync function* streamable(response: {\n  stream: AsyncIterable<GenerateContentResponse>;\n}) {\n  for await (const chunk of response.stream) {\n    const parts = chunk.candidates?.[0]?.content?.parts;\n\n    if (parts === undefined) {\n      continue;\n    }\n\n    const firstPart = parts[0];\n\n    if (typeof firstPart.text === 'string') {\n      yield firstPart.text;\n    }\n  }\n}\n\n/**\n * @deprecated Use the [Google Generative AI provider](https://sdk.vercel.ai/providers/ai-sdk-providers/google-generative-ai) instead.\n */\nexport function GoogleGenerativeAIStream(\n  response: {\n    stream: AsyncIterable<GenerateContentResponse>;\n  },\n  cb?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return readableFromAsyncIterable(streamable(response))\n    .pipeThrough(createCallbacksTransformer(cb))\n    .pipeThrough(createStreamDataTransformer());\n}\n", "import {\n  type AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n  trimStartOfStreamHelper,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\nfunction createParser(res: AsyncGenerator<any>) {\n  const trimStartOfStream = trimStartOfStreamHelper();\n  return new ReadableStream<string>({\n    async pull(controller): Promise<void> {\n      const { value, done } = await res.next();\n\n      if (done) {\n        controller.close();\n        return;\n      }\n\n      const text = trimStartOfStream(value.token?.text ?? '');\n      if (!text) return;\n\n      // some HF models return generated_text instead of a real ending token\n      if (value.generated_text != null && value.generated_text.length > 0) {\n        return;\n      }\n\n      // <|endoftext|> is for https://huggingface.co/OpenAssistant/oasst-sft-4-pythia-12b-epoch-3.5\n      // <|end|> is for https://huggingface.co/HuggingFaceH4/starchat-beta\n      // </s> is also often last token in the stream depending on the model\n      if (text === '</s>' || text === '<|endoftext|>' || text === '<|end|>') {\n        return;\n      }\n\n      controller.enqueue(text);\n    },\n  });\n}\n\nexport function HuggingFaceStream(\n  res: AsyncGenerator<any>,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  return createParser(res)\n    .pipeThrough(createCallbacksTransformer(callbacks))\n    .pipeThrough(createStreamDataTransformer());\n}\n", "// packages/core/streams/inkeep-stream.ts\nimport {\n  AIStream,\n  type AIStreamCallbacksAndOptions,\n  AIStreamParser,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\nexport type InkeepOnFinalMetadata = {\n  chat_session_id: string;\n  records_cited: any;\n};\n\nexport type InkeepChatResultCallbacks = {\n  onFinal?: (\n    completion: string,\n    metadata?: InkeepOnFinalMetadata,\n  ) => Promise<void> | void;\n  onRecordsCited?: (\n    records_cited: InkeepOnFinalMetadata['records_cited'],\n  ) => void;\n};\n\nexport type InkeepAIStreamCallbacksAndOptions = AIStreamCallbacksAndOptions &\n  InkeepChatResultCallbacks;\n\nexport function InkeepStream(\n  res: Response,\n  callbacks?: InkeepAIStreamCallbacksAndOptions,\n): ReadableStream {\n  if (!res.body) {\n    throw new Error('Response body is null');\n  }\n\n  let chat_session_id = '';\n  let records_cited: any;\n\n  const inkeepEventParser: AIStreamParser = (data: string, options) => {\n    const { event } = options;\n\n    if (event === 'records_cited') {\n      records_cited = JSON.parse(data) as any;\n      callbacks?.onRecordsCited?.(records_cited);\n    }\n\n    if (event === 'message_chunk') {\n      const inkeepMessageChunk = JSON.parse(data);\n      chat_session_id = inkeepMessageChunk.chat_session_id ?? chat_session_id;\n      return inkeepMessageChunk.content_chunk;\n    }\n    return;\n  };\n\n  let { onRecordsCited, ...passThroughCallbacks } = callbacks || {};\n\n  // extend onFinal callback with Inkeep specific metadata\n  passThroughCallbacks = {\n    ...passThroughCallbacks,\n    onFinal: completion => {\n      const inkeepOnFinalMetadata: InkeepOnFinalMetadata = {\n        chat_session_id,\n        records_cited,\n      };\n      callbacks?.onFinal?.(completion, inkeepOnFinalMetadata);\n    },\n  };\n\n  return AIStream(res, inkeepEventParser, passThroughCallbacks).pipeThrough(\n    createStreamDataTransformer(),\n  );\n}\n", "import { mergeStreams } from '../core/util/merge-streams';\nimport { prepareResponseHeaders } from '../core/util/prepare-response-headers';\nimport {\n  AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n} from './ai-stream';\nimport { createStreamDataTransformer, StreamData } from './stream-data';\n\ntype LangChainImageDetail = 'auto' | 'low' | 'high';\n\ntype LangChainMessageContentText = {\n  type: 'text';\n  text: string;\n};\n\ntype LangChainMessageContentImageUrl = {\n  type: 'image_url';\n  image_url:\n    | string\n    | {\n        url: string;\n        detail?: LangChainImageDetail;\n      };\n};\n\ntype LangChainMessageContentComplex =\n  | LangChainMessageContentText\n  | LangChainMessageContentImageUrl\n  | (Record<string, any> & {\n      type?: 'text' | 'image_url' | string;\n    })\n  | (Record<string, any> & {\n      type?: never;\n    });\n\ntype LangChainMessageContent = string | LangChainMessageContentComplex[];\n\ntype LangChainAIMessageChunk = {\n  content: LangChainMessageContent;\n};\n\n// LC stream event v2\ntype LangChainStreamEvent = {\n  event: string;\n  data: any;\n};\n\n/**\nConverts LangChain output streams to AIStream.\n\nThe following streams are supported:\n- `LangChainAIMessageChunk` streams (LangChain `model.stream` output)\n- `string` streams (LangChain `StringOutputParser` output)\n\n@deprecated Use `toDataStream` instead.\n */\nexport function toAIStream(\n  stream:\n    | ReadableStream<LangChainStreamEvent>\n    | ReadableStream<LangChainAIMessageChunk>\n    | ReadableStream<string>,\n  callbacks?: AIStreamCallbacksAndOptions,\n) {\n  return toDataStream(stream, callbacks);\n}\n\n/**\nConverts LangChain output streams to AIStream.\n\nThe following streams are supported:\n- `LangChainAIMessageChunk` streams (LangChain `model.stream` output)\n- `string` streams (LangChain `StringOutputParser` output)\n */\nexport function toDataStream(\n  stream:\n    | ReadableStream<LangChainStreamEvent>\n    | ReadableStream<LangChainAIMessageChunk>\n    | ReadableStream<string>,\n  callbacks?: AIStreamCallbacksAndOptions,\n) {\n  return stream\n    .pipeThrough(\n      new TransformStream<\n        LangChainStreamEvent | LangChainAIMessageChunk | string\n      >({\n        transform: async (value, controller) => {\n          // text stream:\n          if (typeof value === 'string') {\n            controller.enqueue(value);\n            return;\n          }\n\n          // LC stream events v2:\n          if ('event' in value) {\n            // chunk is AIMessage Chunk for on_chat_model_stream event:\n            if (value.event === 'on_chat_model_stream') {\n              forwardAIMessageChunk(\n                value.data?.chunk as LangChainAIMessageChunk,\n                controller,\n              );\n            }\n            return;\n          }\n\n          // AI Message chunk stream:\n          forwardAIMessageChunk(value, controller);\n        },\n      }),\n    )\n    .pipeThrough(createCallbacksTransformer(callbacks))\n    .pipeThrough(createStreamDataTransformer());\n}\n\nexport function toDataStreamResponse(\n  stream:\n    | ReadableStream<LangChainStreamEvent>\n    | ReadableStream<LangChainAIMessageChunk>\n    | ReadableStream<string>,\n  options?: {\n    init?: ResponseInit;\n    data?: StreamData;\n    callbacks?: AIStreamCallbacksAndOptions;\n  },\n) {\n  const dataStream = toDataStream(stream, options?.callbacks);\n  const data = options?.data;\n  const init = options?.init;\n\n  const responseStream = data\n    ? mergeStreams(data.stream, dataStream)\n    : dataStream;\n\n  return new Response(responseStream, {\n    status: init?.status ?? 200,\n    statusText: init?.statusText,\n    headers: prepareResponseHeaders(init, {\n      contentType: 'text/plain; charset=utf-8',\n      dataStreamVersion: 'v1',\n    }),\n  });\n}\n\nfunction forwardAIMessageChunk(\n  chunk: LangChainAIMessageChunk,\n  controller: TransformStreamDefaultController<any>,\n) {\n  if (typeof chunk.content === 'string') {\n    controller.enqueue(chunk.content);\n  } else {\n    const content: LangChainMessageContentComplex[] = chunk.content;\n    for (const item of content) {\n      if (item.type === 'text') {\n        controller.enqueue(item.text);\n      }\n    }\n  }\n}\n", "import {\n  type AIStreamCallbacksAndOptions,\n  createCallbacksTransformer,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\n/**\n * @deprecated Use [LangChainAdapter](https://sdk.vercel.ai/providers/adapters/langchain) instead.\n */\nexport function LangChainStream(callbacks?: AIStreamCallbacksAndOptions) {\n  const stream = new TransformStream();\n  const writer = stream.writable.getWriter();\n\n  const runs = new Set();\n\n  const handleError = async (e: Error, runId: string) => {\n    runs.delete(runId);\n    await writer.ready;\n    await writer.abort(e);\n  };\n\n  const handleStart = async (runId: string) => {\n    runs.add(runId);\n  };\n\n  const handleEnd = async (runId: string) => {\n    runs.delete(runId);\n\n    if (runs.size === 0) {\n      await writer.ready;\n      await writer.close();\n    }\n  };\n\n  return {\n    stream: stream.readable\n      .pipeThrough(createCallbacksTransformer(callbacks))\n      .pipeThrough(createStreamDataTransformer()),\n    writer,\n    handlers: {\n      handleLLMNewToken: async (token: string) => {\n        await writer.ready;\n        await writer.write(token);\n      },\n      handleLLMStart: async (_llm: any, _prompts: string[], runId: string) => {\n        handleStart(runId);\n      },\n      handleLLMEnd: async (_output: any, runId: string) => {\n        await handleEnd(runId);\n      },\n      handleLLMError: async (e: Error, runId: string) => {\n        await handleError(e, runId);\n      },\n      handleChainStart: async (_chain: any, _inputs: any, runId: string) => {\n        handleStart(runId);\n      },\n      handleChainEnd: async (_outputs: any, runId: string) => {\n        await handleEnd(runId);\n      },\n      handleChainError: async (e: Error, runId: string) => {\n        await handleError(e, runId);\n      },\n      handleToolStart: async (_tool: any, _input: string, runId: string) => {\n        handleStart(runId);\n      },\n      handleToolEnd: async (_output: string, runId: string) => {\n        await handleEnd(runId);\n      },\n      handleToolError: async (e: Error, runId: string) => {\n        await handleError(e, runId);\n      },\n    },\n  };\n}\n", "import {\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n  type AIStreamCallbacksAndOptions,\n} from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\ninterface ChatCompletionResponseChunk {\n  id: string;\n  object: 'chat.completion.chunk';\n  created: number;\n  model: string;\n  choices: ChatCompletionResponseChunkChoice[];\n}\n\ninterface ChatCompletionResponseChunkChoice {\n  index: number;\n  delta: {\n    role?: string;\n    content?: string;\n    tool_calls?: ToolCalls[];\n  };\n  finish_reason: string;\n}\n\ninterface FunctionCall {\n  name: string;\n  arguments: string;\n}\n\ninterface ToolCalls {\n  id: 'null';\n  type: 'function';\n  function: FunctionCall;\n}\n\nasync function* streamable(stream: AsyncIterable<ChatCompletionResponseChunk>) {\n  for await (const chunk of stream) {\n    const content = chunk.choices[0]?.delta?.content;\n\n    if (content === undefined || content === '') {\n      continue;\n    }\n\n    yield content;\n  }\n}\n\n/*\n * @deprecated Use the [Mistral provider](https://sdk.vercel.ai/providers/ai-sdk-providers/mistral) instead.\n */\nexport function MistralStream(\n  response: AsyncGenerator<ChatCompletionResponseChunk, void, unknown>,\n  callbacks?: AIStreamCallbacksAndOptions,\n): ReadableStream {\n  const stream = readableFromAsyncIterable(streamable(response));\n  return stream\n    .pipeThrough(createCallbacksTransformer(callbacks))\n    .pipeThrough(createStreamDataTransformer());\n}\n", "import {\n  Create<PERSON>essage,\n  Function<PERSON>all,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ue,\n  Tool<PERSON>all,\n  createChunkDecoder,\n  formatStreamPart,\n} from '@ai-sdk/ui-utils';\nimport {\n  AIStream,\n  FunctionCallPayload,\n  ToolCallPayload,\n  createCallbacksTransformer,\n  readableFromAsyncIterable,\n  trimStartOfStreamHelper,\n  type AIStreamCallbacksAndOptions,\n} from './ai-stream';\nimport { AzureChatCompletions } from './azure-openai-types';\nimport { createStreamDataTransformer } from './stream-data';\n\nexport type OpenAIStreamCallbacks = AIStreamCallbacksAndOptions & {\n  /**\n   * @example\n   * ```js\n   * const response = await openai.chat.completions.create({\n   *   model: 'gpt-3.5-turbo-0613',\n   *   stream: true,\n   *   messages,\n   *   functions,\n   * })\n   *\n   * const stream = OpenAIStream(response, {\n   *   experimental_onFunctionCall: async (functionCallPayload, createFunctionCallMessages) => {\n   *     // ... run your custom logic here\n   *     const result = await myFunction(functionCallPayload)\n   *\n   *     // Ask for another completion, or return a string to send to the client as an assistant message.\n   *     return await openai.chat.completions.create({\n   *       model: 'gpt-3.5-turbo-0613',\n   *       stream: true,\n   *       // Append the relevant \"assistant\" and \"function\" call messages\n   *       messages: [...messages, ...createFunctionCallMessages(result)],\n   *       functions,\n   *     })\n   *   }\n   * })\n   * ```\n   */\n  experimental_onFunctionCall?: (\n    functionCallPayload: FunctionCallPayload,\n    createFunctionCallMessages: (\n      functionCallResult: JSONValue,\n    ) => CreateMessage[],\n  ) => Promise<\n    Response | undefined | void | string | AsyncIterableOpenAIStreamReturnTypes\n  >;\n  /**\n   * @example\n   * ```js\n   * const response = await openai.chat.completions.create({\n   *   model: 'gpt-3.5-turbo-1106', // or gpt-4-1106-preview\n   *   stream: true,\n   *   messages,\n   *   tools,\n   *   tool_choice: \"auto\", // auto is default, but we'll be explicit\n   * })\n   *\n   * const stream = OpenAIStream(response, {\n   *   experimental_onToolCall: async (toolCallPayload, appendToolCallMessages) => {\n   *    let messages: CreateMessage[] = []\n   *    //   There might be multiple tool calls, so we need to iterate through them\n   *    for (const tool of toolCallPayload.tools) {\n   *     // ... run your custom logic here\n   *     const result = await myFunction(tool.function)\n   *    // Append the relevant \"assistant\" and \"tool\" call messages\n   *     appendToolCallMessage({tool_call_id:tool.id, function_name:tool.function.name, tool_call_result:result})\n   *    }\n   *     // Ask for another completion, or return a string to send to the client as an assistant message.\n   *     return await openai.chat.completions.create({\n   *       model: 'gpt-3.5-turbo-1106', // or gpt-4-1106-preview\n   *       stream: true,\n   *       // Append the results messages, calling appendToolCallMessage without\n   *       // any arguments will jsut return the accumulated messages\n   *       messages: [...messages, ...appendToolCallMessage()],\n   *       tools,\n   *        tool_choice: \"auto\", // auto is default, but we'll be explicit\n   *     })\n   *   }\n   * })\n   * ```\n   */\n  experimental_onToolCall?: (\n    toolCallPayload: ToolCallPayload,\n    appendToolCallMessage: (result?: {\n      tool_call_id: string;\n      function_name: string;\n      tool_call_result: JSONValue;\n    }) => CreateMessage[],\n  ) => Promise<\n    Response | undefined | void | string | AsyncIterableOpenAIStreamReturnTypes\n  >;\n};\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L28-L40\ninterface ChatCompletionChunk {\n  id: string;\n  choices: Array<ChatCompletionChunkChoice>;\n  created: number;\n  model: string;\n  object: string;\n}\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L43-L49\n// Updated for https://github.com/openai/openai-node/commit/f10c757d831d90407ba47b4659d9cd34b1a35b1d\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ChatCompletionChunkChoice {\n  delta: ChoiceDelta;\n  finish_reason:\n    | 'stop'\n    | 'length'\n    | 'tool_calls'\n    | 'content_filter'\n    | 'function_call'\n    | null;\n  index: number;\n}\n\n// https://github.com/openai/openai-node/blob/07b3504e1c40fd929f4aae1651b83afc19e3baf8/src/resources/chat/completions.ts#L123-L139\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ChoiceDelta {\n  /**\n   * The contents of the chunk message.\n   */\n  content?: string | null;\n\n  /**\n   * The name and arguments of a function that should be called, as generated by the\n   * model.\n   */\n  function_call?: FunctionCall;\n\n  /**\n   * The role of the author of this message.\n   */\n  role?: 'system' | 'user' | 'assistant' | 'tool';\n\n  tool_calls?: Array<DeltaToolCall>;\n}\n\n// From https://github.com/openai/openai-node/blob/master/src/resources/chat/completions.ts\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface DeltaToolCall {\n  index: number;\n\n  /**\n   * The ID of the tool call.\n   */\n  id?: string;\n\n  /**\n   * The function that the model called.\n   */\n  function?: ToolCallFunction;\n\n  /**\n   * The type of the tool. Currently, only `function` is supported.\n   */\n  type?: 'function';\n}\n\n// From https://github.com/openai/openai-node/blob/master/src/resources/chat/completions.ts\n// Updated to https://github.com/openai/openai-node/commit/84b43280089eacdf18f171723591856811beddce\ninterface ToolCallFunction {\n  /**\n   * The arguments to call the function with, as generated by the model in JSON\n   * format. Note that the model does not always generate valid JSON, and may\n   * hallucinate parameters not defined by your function schema. Validate the\n   * arguments in your code before calling your function.\n   */\n  arguments?: string;\n\n  /**\n   * The name of the function to call.\n   */\n  name?: string;\n}\n\n/**\n * https://github.com/openai/openai-node/blob/3ec43ee790a2eb6a0ccdd5f25faa23251b0f9b8e/src/resources/completions.ts#L28C1-L64C1\n * Completions API. Streamed and non-streamed responses are the same.\n */\ninterface Completion {\n  /**\n   * A unique identifier for the completion.\n   */\n  id: string;\n\n  /**\n   * The list of completion choices the model generated for the input prompt.\n   */\n  choices: Array<CompletionChoice>;\n\n  /**\n   * The Unix timestamp of when the completion was created.\n   */\n  created: number;\n\n  /**\n   * The model used for completion.\n   */\n  model: string;\n\n  /**\n   * The object type, which is always \"text_completion\"\n   */\n  object: string;\n\n  /**\n   * Usage statistics for the completion request.\n   */\n  usage?: CompletionUsage;\n}\n\ninterface CompletionChoice {\n  /**\n   * The reason the model stopped generating tokens. This will be `stop` if the model\n   * hit a natural stop point or a provided stop sequence, or `length` if the maximum\n   * number of tokens specified in the request was reached.\n   */\n  finish_reason: 'stop' | 'length' | 'content_filter';\n\n  index: number;\n\n  // edited: Removed CompletionChoice.logProbs and replaced with any\n  logprobs: any | null;\n\n  text: string;\n}\n\nexport interface CompletionUsage {\n  /**\n   * Usage statistics for the completion request.\n   */\n\n  /**\n   * Number of tokens in the generated completion.\n   */\n  completion_tokens: number;\n\n  /**\n   * Number of tokens in the prompt.\n   */\n  prompt_tokens: number;\n\n  /**\n   * Total number of tokens used in the request (prompt + completion).\n   */\n  total_tokens: number;\n}\n\n/**\n * Creates a parser function for processing the OpenAI stream data.\n * The parser extracts and trims text content from the JSON data. This parser\n * can handle data for chat or completion models.\n *\n * @return {(data: string) => string | void| { isText: false; content: string }}\n * A parser function that takes a JSON string as input and returns the extracted text content,\n * a complex object with isText: false for function/tool calls, or nothing.\n */\nfunction parseOpenAIStream(): (\n  data: string,\n) => string | void | { isText: false; content: string } {\n  const extract = chunkToText();\n  return data => extract(JSON.parse(data) as OpenAIStreamReturnTypes);\n}\n\n/**\n * Reads chunks from OpenAI's new Streamable interface, which is essentially\n * the same as the old Response body interface with an included SSE parser\n * doing the parsing for us.\n */\nasync function* streamable(stream: AsyncIterableOpenAIStreamReturnTypes) {\n  const extract = chunkToText();\n\n  for await (let chunk of stream) {\n    // convert chunk if it is an Azure chat completion. Azure does not expose all\n    // properties in the interfaces, and also uses camelCase instead of snake_case\n    if ('promptFilterResults' in chunk) {\n      chunk = {\n        id: chunk.id,\n        created: chunk.created.getDate(),\n        object: (chunk as any).object, // not exposed by Azure API\n        model: (chunk as any).model, // not exposed by Azure API\n        choices: chunk.choices.map(choice => ({\n          delta: {\n            content: choice.delta?.content,\n            function_call: choice.delta?.functionCall,\n            role: choice.delta?.role as any,\n            tool_calls: choice.delta?.toolCalls?.length\n              ? choice.delta?.toolCalls?.map((toolCall, index) => ({\n                  index,\n                  id: toolCall.id,\n                  function: toolCall.function,\n                  type: toolCall.type,\n                }))\n              : undefined,\n          },\n          finish_reason: choice.finishReason as any,\n          index: choice.index,\n        })),\n      } satisfies ChatCompletionChunk;\n    }\n\n    const text = extract(chunk);\n\n    if (text) yield text;\n  }\n}\n\nfunction chunkToText(): (\n  chunk: OpenAIStreamReturnTypes,\n) => string | { isText: false; content: string } | void {\n  const trimStartOfStream = trimStartOfStreamHelper();\n  let isFunctionStreamingIn: boolean;\n  return json => {\n    if (isChatCompletionChunk(json)) {\n      const delta = json.choices[0]?.delta;\n      if (delta.function_call?.name) {\n        isFunctionStreamingIn = true;\n        return {\n          isText: false,\n          content: `{\"function_call\": {\"name\": \"${delta.function_call.name}\", \"arguments\": \"`,\n        };\n      } else if (delta.tool_calls?.[0]?.function?.name) {\n        isFunctionStreamingIn = true;\n        const toolCall = delta.tool_calls[0];\n        if (toolCall.index === 0) {\n          return {\n            isText: false,\n            content: `{\"tool_calls\":[ {\"id\": \"${toolCall.id}\", \"type\": \"function\", \"function\": {\"name\": \"${toolCall.function?.name}\", \"arguments\": \"`,\n          };\n        } else {\n          return {\n            isText: false,\n            content: `\"}}, {\"id\": \"${toolCall.id}\", \"type\": \"function\", \"function\": {\"name\": \"${toolCall.function?.name}\", \"arguments\": \"`,\n          };\n        }\n      } else if (delta.function_call?.arguments) {\n        return {\n          isText: false,\n          content: cleanupArguments(delta.function_call?.arguments),\n        };\n      } else if (delta.tool_calls?.[0]?.function?.arguments) {\n        return {\n          isText: false,\n          content: cleanupArguments(delta.tool_calls?.[0]?.function?.arguments),\n        };\n      } else if (\n        isFunctionStreamingIn &&\n        (json.choices[0]?.finish_reason === 'function_call' ||\n          json.choices[0]?.finish_reason === 'stop')\n      ) {\n        isFunctionStreamingIn = false; // Reset the flag\n        return {\n          isText: false,\n          content: '\"}}',\n        };\n      } else if (\n        isFunctionStreamingIn &&\n        json.choices[0]?.finish_reason === 'tool_calls'\n      ) {\n        isFunctionStreamingIn = false; // Reset the flag\n        return {\n          isText: false,\n          content: '\"}}]}',\n        };\n      }\n    }\n\n    const text = trimStartOfStream(\n      isChatCompletionChunk(json) && json.choices[0].delta.content\n        ? json.choices[0].delta.content\n        : isCompletion(json)\n        ? json.choices[0].text\n        : '',\n    );\n\n    return text;\n  };\n\n  function cleanupArguments(argumentChunk: string) {\n    let escapedPartialJson = argumentChunk\n      .replace(/\\\\/g, '\\\\\\\\') // Replace backslashes first to prevent double escaping\n      .replace(/\\//g, '\\\\/') // Escape slashes\n      .replace(/\"/g, '\\\\\"') // Escape double quotes\n      .replace(/\\n/g, '\\\\n') // Escape new lines\n      .replace(/\\r/g, '\\\\r') // Escape carriage returns\n      .replace(/\\t/g, '\\\\t') // Escape tabs\n      .replace(/\\f/g, '\\\\f'); // Escape form feeds\n\n    return `${escapedPartialJson}`;\n  }\n}\n\nconst __internal__OpenAIFnMessagesSymbol = Symbol(\n  'internal_openai_fn_messages',\n);\n\ntype AsyncIterableOpenAIStreamReturnTypes =\n  | AsyncIterable<ChatCompletionChunk>\n  | AsyncIterable<Completion>\n  | AsyncIterable<AzureChatCompletions>;\n\ntype ExtractType<T> = T extends AsyncIterable<infer U> ? U : never;\n\ntype OpenAIStreamReturnTypes =\n  ExtractType<AsyncIterableOpenAIStreamReturnTypes>;\n\nfunction isChatCompletionChunk(\n  data: OpenAIStreamReturnTypes,\n): data is ChatCompletionChunk {\n  return (\n    'choices' in data &&\n    data.choices &&\n    data.choices[0] &&\n    'delta' in data.choices[0]\n  );\n}\n\nfunction isCompletion(data: OpenAIStreamReturnTypes): data is Completion {\n  return (\n    'choices' in data &&\n    data.choices &&\n    data.choices[0] &&\n    'text' in data.choices[0]\n  );\n}\n\n/**\n * @deprecated Use the [OpenAI provider](https://sdk.vercel.ai/providers/ai-sdk-providers/openai) instead.\n */\nexport function OpenAIStream(\n  res: Response | AsyncIterableOpenAIStreamReturnTypes,\n  callbacks?: OpenAIStreamCallbacks,\n): ReadableStream {\n  // Annotate the internal `messages` property for recursive function calls\n  const cb:\n    | undefined\n    | (OpenAIStreamCallbacks & {\n        [__internal__OpenAIFnMessagesSymbol]?: CreateMessage[];\n      }) = callbacks;\n\n  let stream: ReadableStream<Uint8Array>;\n  if (Symbol.asyncIterator in res) {\n    stream = readableFromAsyncIterable(streamable(res)).pipeThrough(\n      createCallbacksTransformer(\n        cb?.experimental_onFunctionCall || cb?.experimental_onToolCall\n          ? {\n              ...cb,\n              onFinal: undefined,\n            }\n          : {\n              ...cb,\n            },\n      ),\n    );\n  } else {\n    stream = AIStream(\n      res,\n      parseOpenAIStream(),\n      cb?.experimental_onFunctionCall || cb?.experimental_onToolCall\n        ? {\n            ...cb,\n            onFinal: undefined,\n          }\n        : {\n            ...cb,\n          },\n    );\n  }\n\n  if (cb && (cb.experimental_onFunctionCall || cb.experimental_onToolCall)) {\n    const functionCallTransformer = createFunctionCallTransformer(cb);\n    return stream.pipeThrough(functionCallTransformer);\n  } else {\n    return stream.pipeThrough(createStreamDataTransformer());\n  }\n}\n\nfunction createFunctionCallTransformer(\n  callbacks: OpenAIStreamCallbacks & {\n    [__internal__OpenAIFnMessagesSymbol]?: CreateMessage[];\n  },\n): TransformStream<Uint8Array, Uint8Array> {\n  const textEncoder = new TextEncoder();\n  let isFirstChunk = true;\n  let aggregatedResponse = '';\n  let aggregatedFinalCompletionResponse = '';\n  let isFunctionStreamingIn = false;\n\n  let functionCallMessages: CreateMessage[] =\n    callbacks[__internal__OpenAIFnMessagesSymbol] || [];\n\n  const decode = createChunkDecoder();\n\n  return new TransformStream({\n    async transform(chunk, controller): Promise<void> {\n      const message = decode(chunk);\n      aggregatedFinalCompletionResponse += message;\n\n      const shouldHandleAsFunction =\n        isFirstChunk &&\n        (message.startsWith('{\"function_call\":') ||\n          message.startsWith('{\"tool_calls\":'));\n\n      if (shouldHandleAsFunction) {\n        isFunctionStreamingIn = true;\n        aggregatedResponse += message;\n        isFirstChunk = false;\n        return;\n      }\n\n      // Stream as normal\n      if (!isFunctionStreamingIn) {\n        controller.enqueue(\n          textEncoder.encode(formatStreamPart('text', message)),\n        );\n        return;\n      } else {\n        aggregatedResponse += message;\n      }\n    },\n    async flush(controller): Promise<void> {\n      try {\n        if (\n          !isFirstChunk &&\n          isFunctionStreamingIn &&\n          (callbacks.experimental_onFunctionCall ||\n            callbacks.experimental_onToolCall)\n        ) {\n          isFunctionStreamingIn = false;\n          const payload = JSON.parse(aggregatedResponse);\n          // Append the function call message to the list\n          let newFunctionCallMessages: CreateMessage[] = [\n            ...functionCallMessages,\n          ];\n\n          let functionResponse:\n            | Response\n            | undefined\n            | void\n            | string\n            | AsyncIterableOpenAIStreamReturnTypes\n            | undefined = undefined;\n          // This callbacks.experimental_onFunctionCall check should not be necessary but TS complains\n          if (callbacks.experimental_onFunctionCall) {\n            // If the user is using the experimental_onFunctionCall callback, they should not be using tools\n            // if payload.function_call is not defined by time we get here we must have gotten a tool response\n            // and the user had defined experimental_onToolCall\n            if (payload.function_call === undefined) {\n              console.warn(\n                'experimental_onFunctionCall should not be defined when using tools',\n              );\n            }\n\n            const argumentsPayload = JSON.parse(\n              payload.function_call.arguments,\n            );\n\n            functionResponse = await callbacks.experimental_onFunctionCall(\n              {\n                name: payload.function_call.name,\n                arguments: argumentsPayload,\n              },\n              result => {\n                // Append the function call request and result messages to the list\n                newFunctionCallMessages = [\n                  ...functionCallMessages,\n                  {\n                    role: 'assistant',\n                    content: '',\n                    function_call: payload.function_call,\n                  },\n                  {\n                    role: 'function',\n                    name: payload.function_call.name,\n                    content: JSON.stringify(result),\n                  },\n                ];\n                // Return it to the user\n                return newFunctionCallMessages;\n              },\n            );\n          }\n          if (callbacks.experimental_onToolCall) {\n            const toolCalls: ToolCallPayload = {\n              tools: [],\n            };\n            for (const tool of payload.tool_calls) {\n              toolCalls.tools.push({\n                id: tool.id,\n                type: 'function',\n                func: {\n                  name: tool.function.name,\n                  arguments: JSON.parse(tool.function.arguments),\n                },\n              });\n            }\n            let responseIndex = 0;\n            try {\n              functionResponse = await callbacks.experimental_onToolCall(\n                toolCalls,\n                result => {\n                  if (result) {\n                    const { tool_call_id, function_name, tool_call_result } =\n                      result;\n                    // Append the function call request and result messages to the list\n                    newFunctionCallMessages = [\n                      ...newFunctionCallMessages,\n                      // Only append the assistant message if it's the first response\n                      ...(responseIndex === 0\n                        ? [\n                            {\n                              role: 'assistant' as const,\n                              content: '',\n                              tool_calls: payload.tool_calls.map(\n                                (tc: ToolCall) => ({\n                                  id: tc.id,\n                                  type: 'function',\n                                  function: {\n                                    name: tc.function.name,\n                                    // we send the arguments an object to the user, but as the API expects a string, we need to stringify it\n                                    arguments: JSON.stringify(\n                                      tc.function.arguments,\n                                    ),\n                                  },\n                                }),\n                              ),\n                            },\n                          ]\n                        : []),\n                      // Append the function call result message\n                      {\n                        role: 'tool',\n                        tool_call_id,\n                        name: function_name,\n                        content: JSON.stringify(tool_call_result),\n                      },\n                    ];\n                    responseIndex++;\n                  }\n                  // Return it to the user\n                  return newFunctionCallMessages;\n                },\n              );\n            } catch (e) {\n              console.error('Error calling experimental_onToolCall:', e);\n            }\n          }\n\n          if (!functionResponse) {\n            // The user didn't do anything with the function call on the server and wants\n            // to either do nothing or run it on the client\n            // so we just return the function call as a message\n            controller.enqueue(\n              textEncoder.encode(\n                formatStreamPart(\n                  payload.function_call ? 'function_call' : 'tool_calls',\n                  // parse to prevent double-encoding:\n                  JSON.parse(aggregatedResponse),\n                ),\n              ),\n            );\n            return;\n          } else if (typeof functionResponse === 'string') {\n            // The user returned a string, so we just return it as a message\n            controller.enqueue(\n              textEncoder.encode(formatStreamPart('text', functionResponse)),\n            );\n            aggregatedFinalCompletionResponse = functionResponse;\n            return;\n          }\n\n          // Recursively:\n\n          // We don't want to trigger onStart or onComplete recursively\n          // so we remove them from the callbacks\n          // see https://github.com/vercel/ai/issues/351\n          const filteredCallbacks: OpenAIStreamCallbacks = {\n            ...callbacks,\n            onStart: undefined,\n          };\n          // We only want onFinal to be called the _last_ time\n          callbacks.onFinal = undefined;\n\n          const openAIStream = OpenAIStream(functionResponse, {\n            ...filteredCallbacks,\n            [__internal__OpenAIFnMessagesSymbol]: newFunctionCallMessages,\n          } as AIStreamCallbacksAndOptions);\n\n          const reader = openAIStream.getReader();\n\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n              break;\n            }\n            controller.enqueue(value);\n          }\n        }\n      } finally {\n        if (callbacks.onFinal && aggregatedFinalCompletionResponse) {\n          await callbacks.onFinal(aggregatedFinalCompletionResponse);\n        }\n      }\n    },\n  });\n}\n", "import { AIStream, type AIStreamCallbacksAndOptions } from './ai-stream';\nimport { createStreamDataTransformer } from './stream-data';\n\n// from replicate SDK\ninterface Prediction {\n  id: string;\n  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';\n  version: string;\n  input: object;\n  output?: any;\n  source: 'api' | 'web';\n  error?: any;\n  logs?: string;\n  metrics?: {\n    predict_time?: number;\n  };\n  webhook?: string;\n  webhook_events_filter?: ('start' | 'output' | 'logs' | 'completed')[];\n  created_at: string;\n  updated_at?: string;\n  completed_at?: string;\n  urls: {\n    get: string;\n    cancel: string;\n    stream?: string;\n  };\n}\n\n/**\n * Stream predictions from Replicate.\n * Only certain models are supported and you must pass `stream: true` to\n * replicate.predictions.create().\n * @see https://github.com/replicate/replicate-javascript#streaming\n *\n * @example\n * const response = await replicate.predictions.create({\n *  stream: true,\n *  input: {\n *    prompt: messages.join('\\n')\n *  },\n *  version: '2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1'\n * })\n *\n * const stream = await ReplicateStream(response)\n * return new StreamingTextResponse(stream)\n *\n */\nexport async function ReplicateStream(\n  res: Prediction,\n  cb?: AIStreamCallbacksAndOptions,\n  options?: {\n    headers?: Record<string, string>;\n  },\n): Promise<ReadableStream> {\n  const url = res.urls?.stream;\n\n  if (!url) {\n    if (res.error) throw new Error(res.error);\n    else throw new Error('Missing stream URL in Replicate response');\n  }\n\n  const eventStream = await fetch(url, {\n    method: 'GET',\n    headers: {\n      Accept: 'text/event-stream',\n      ...options?.headers,\n    },\n  });\n\n  return AIStream(eventStream, undefined, cb).pipeThrough(\n    createStreamDataTransformer(),\n  );\n}\n", "import type { ServerResponse } from 'node:http';\nimport { StreamData } from './stream-data';\nimport { mergeStreams } from '../core/util/merge-streams';\n\n/**\n * A utility function to stream a ReadableStream to a Node.js response-like object.\n */\nexport function streamToResponse(\n  res: ReadableStream,\n  response: ServerResponse,\n  init?: { headers?: Record<string, string>; status?: number },\n  data?: StreamData,\n) {\n  response.writeHead(init?.status ?? 200, {\n    'Content-Type': 'text/plain; charset=utf-8',\n    ...init?.headers,\n  });\n\n  let processedStream = res;\n\n  if (data) {\n    processedStream = mergeStreams(data.stream, res);\n  }\n\n  const reader = processedStream.getReader();\n  function read() {\n    reader.read().then(({ done, value }: { done: boolean; value?: any }) => {\n      if (done) {\n        response.end();\n        return;\n      }\n      response.write(value);\n      read();\n    });\n  }\n  read();\n}\n", "import { mergeStreams } from '../core/util/merge-streams';\nimport { prepareResponseHeaders } from '../core/util/prepare-response-headers';\nimport { StreamData } from './stream-data';\n\n/**\n * A utility class for streaming text responses.\n *\n * @deprecated Use `streamText.toDataStreamResponse()` instead.\n */\nexport class StreamingTextResponse extends Response {\n  constructor(res: ReadableStream, init?: ResponseInit, data?: StreamData) {\n    let processedStream = res;\n\n    if (data) {\n      processedStream = mergeStreams(data.stream, res);\n    }\n\n    super(processedStream as any, {\n      ...init,\n      status: 200,\n      headers: prepareResponseHeaders(init, {\n        contentType: 'text/plain; charset=utf-8',\n      }),\n    });\n  }\n}\n"], "mappings": ";;;;;;;AACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAwBP,SAAS,cAAc,sBAAsB;;;AC5BtC,SAAS,sBAAsB;AAAA,EACpC;AAAA,EACA;AACF,GAGG;AACD,SAAO;AAAA,IACL,kBAAkB,GAAG,aAAa,IAChC,uCAAW,eAAc,OAAO,IAAI,UAAU,UAAU,KAAK,EAC/D;AAAA,EACF;AACF;;;ACVO,SAAS,2BAA2B;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKe;AAdf;AAeE,SAAO;AAAA,IACL,qBAAqB,MAAM;AAAA,IAC3B,eAAe,MAAM;AAAA;AAAA,IAGrB,GAAG,OAAO,QAAQ,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,KAAK,MAAM;AAC/D,iBAAW,eAAe,GAAG,EAAE,IAAI;AACnC,aAAO;AAAA,IACT,GAAG,CAAC,CAAe;AAAA;AAAA,IAGnB,iBAAiB,uCAAW;AAAA,IAC5B,2BAA2B,uCAAW;AAAA;AAAA,IAGtC,GAAG,OAAO,SAAQ,4CAAW,aAAX,YAAuB,CAAC,CAAC,EAAE;AAAA,MAC3C,CAAC,YAAY,CAAC,KAAK,KAAK,MAAM;AAC5B,mBAAW,yBAAyB,GAAG,EAAE,IAAI;AAC7C,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AAAA;AAAA,IAGA,GAAG,OAAO,QAAQ,4BAAW,CAAC,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,KAAK,MAAM;AACpE,UAAI,UAAU,QAAW;AACvB,mBAAW,sBAAsB,GAAG,EAAE,IAAI;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAe;AAAA,EACrB;AACF;;;AC9CA,SAAiB,aAAa;;;ACKvB,IAAM,aAAqB;AAAA,EAChC,YAAkB;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,gBACE,MACA,MACA,MACA,MACiB;AACjB,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,KAAK,QAAQ;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,KAAK,QAAQ;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AACF;AAEA,IAAM,WAAiB;AAAA,EACrB,cAAc;AACZ,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO;AAAA,EACT;AAAA,EACA,YAAY;AACV,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EACA,MAAM;AACJ,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,WAAO;AAAA,EACT;AACF;AAEA,IAAM,kBAA+B;AAAA,EACnC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY;AACd;;;AD9DA,IAAI,aAAiC;AAM9B,SAAS,UAAU,EAAE,UAAU,GAAmC;AACvE,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAEA,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,UAAU,IAAI;AAC7B;;;AEtBA,SAAmC,sBAAsB;AAElD,SAAS,WAAc;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAChB,GAMG;AACD,SAAO,OAAO,gBAAgB,MAAM,EAAE,WAAW,GAAG,OAAM,SAAQ;AAChE,QAAI;AACF,YAAM,SAAS,MAAM,GAAG,IAAI;AAE5B,UAAI,aAAa;AACf,aAAK,IAAI;AAAA,MACX;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI;AACF,YAAI,iBAAiB,OAAO;AAC1B,eAAK,gBAAgB;AAAA,YACnB,MAAM,MAAM;AAAA,YACZ,SAAS,MAAM;AAAA,YACf,OAAO,MAAM;AAAA,UACf,CAAC;AACD,eAAK,UAAU;AAAA,YACb,MAAM,eAAe;AAAA,YACrB,SAAS,MAAM;AAAA,UACjB,CAAC;AAAA,QACH,OAAO;AACL,eAAK,UAAU,EAAE,MAAM,eAAe,MAAM,CAAC;AAAA,QAC/C;AAAA,MACF,UAAE;AAEA,aAAK,IAAI;AAAA,MACX;AAEA,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AACH;;;AC5CO,SAAS,0BAA0B;AAAA,EACxC;AAAA,EACA;AACF,GASe;AACb,SAAO,OAAO,QAAQ,UAAU,EAAE,OAAO,CAACA,aAAY,CAAC,KAAK,KAAK,MAAM;AACrE,QAAI,UAAU,QAAW;AACvB,aAAOA;AAAA,IACT;AAGA,QACE,OAAO,UAAU,YACjB,WAAW,SACX,OAAO,MAAM,UAAU,YACvB;AAEA,WAAI,uCAAW,kBAAiB,OAAO;AACrC,eAAOA;AAAA,MACT;AAEA,YAAM,SAAS,MAAM,MAAM;AAE3B,aAAO,WAAW,SACdA,cACA,EAAE,GAAGA,aAAY,CAAC,GAAG,GAAG,OAAO;AAAA,IACrC;AAGA,QACE,OAAO,UAAU,YACjB,YAAY,SACZ,OAAO,MAAM,WAAW,YACxB;AAEA,WAAI,uCAAW,mBAAkB,OAAO;AACtC,eAAOA;AAAA,MACT;AAEA,YAAM,SAAS,MAAM,OAAO;AAE5B,aAAO,WAAW,SACdA,cACA,EAAE,GAAGA,aAAY,CAAC,GAAG,GAAG,OAAO;AAAA,IACrC;AAGA,WAAO,EAAE,GAAGA,aAAY,CAAC,GAAG,GAAG,MAAM;AAAA,EACvC,GAAG,CAAC,CAAC;AACP;;;AC5DA,SAAS,cAAc,kBAAkB;AACzC,SAAS,iBAAiB,oBAAoB;;;ACD9C,eAAsB,MAAM,WAAkC;AAC5D,SAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,SAAS,CAAC;AAC9D;;;ADUO,IAAM,8BACX,CAAC;AAAA,EACC,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,gBAAgB;AAClB,IAAI,CAAC,MACL,OAAe,MACb,6BAA6B,GAAG;AAAA,EAC9B;AAAA,EACA,WAAW;AAAA,EACX;AACF,CAAC;AAEL,eAAe,6BACb,GACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GACA,SAAoB,CAAC,GACJ;AACjB,MAAI;AACF,WAAO,MAAM,EAAE;AAAA,EACjB,SAAS,OAAO;AACd,QAAI,aAAa,KAAK,GAAG;AACvB,YAAM;AAAA,IACR;AAEA,QAAI,eAAe,GAAG;AACpB,YAAM;AAAA,IACR;AAEA,UAAM,eAAe,gBAAgB,KAAK;AAC1C,UAAM,YAAY,CAAC,GAAG,QAAQ,KAAK;AACnC,UAAM,YAAY,UAAU;AAE5B,QAAI,YAAY,YAAY;AAC1B,YAAM,IAAI,WAAW;AAAA,QACnB,SAAS,gBAAgB,SAAS,0BAA0B,YAAY;AAAA,QACxE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,QACE,iBAAiB,SACjB,aAAa,eAAe,KAAK,KACjC,MAAM,gBAAgB,QACtB,aAAa,YACb;AACA,YAAM,MAAM,SAAS;AACrB,aAAO;AAAA,QACL;AAAA,QACA,EAAE,YAAY,WAAW,gBAAgB,WAAW,cAAc;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAEA,QAAI,cAAc,GAAG;AACnB,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,WAAW;AAAA,MACnB,SAAS,gBAAgB,SAAS,wCAAwC,YAAY;AAAA,MACtF,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AE3DA,eAAsB,MAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAC1B,GAiCgC;AA9DhC;AA+DE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,WAAW;AAAA,EACzB,CAAC;AAED,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AAErE,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB,EAAE,eAAe,YAAY,UAAU,CAAC;AAAA,QACjE,GAAG;AAAA,QACH,YAAY,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,EAAE;AAAA,MACnD;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,IAAI,OAAM,SAAQ;AAChB,YAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AAExD,YAAM,EAAE,WAAW,OAAO,YAAY,IAAI,MAAM;AAAA,QAAM;AAAA;AAAA,UAEpD,WAAW;AAAA,YACT,MAAM;AAAA,YACN,YAAY,0BAA0B;AAAA,cACpC;AAAA,cACA,YAAY;AAAA,gBACV,GAAG,sBAAsB;AAAA,kBACvB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,gBACD,GAAG;AAAA;AAAA,gBAEH,aAAa,EAAE,OAAO,MAAM,CAAC,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,cACtD;AAAA,YACF,CAAC;AAAA,YACD;AAAA,YACA,IAAI,OAAM,gBAAe;AAvGnC,kBAAAC;AAwGY,oBAAM,gBAAgB,MAAM,MAAM,QAAQ;AAAA,gBACxC,QAAQ,CAAC,KAAK;AAAA,gBACd;AAAA,gBACA;AAAA,cACF,CAAC;AAED,oBAAMC,aAAY,cAAc,WAAW,CAAC;AAC5C,oBAAMC,UAAQF,MAAA,cAAc,UAAd,OAAAA,MAAuB,EAAE,QAAQ,IAAI;AAEnD,0BAAY;AAAA,gBACV,0BAA0B;AAAA,kBACxB;AAAA,kBACA,YAAY;AAAA,oBACV,iBAAiB;AAAA,sBACf,QAAQ,MACN,cAAc,WAAW;AAAA,wBAAI,CAAAC,eAC3B,KAAK,UAAUA,UAAS;AAAA,sBAC1B;AAAA,oBACJ;AAAA,oBACA,mBAAmBC,OAAM;AAAA,kBAC3B;AAAA,gBACF,CAAC;AAAA,cACH;AAEA,qBAAO;AAAA,gBACL,WAAAD;AAAA,gBACA,OAAAC;AAAA,gBACA,aAAa,cAAc;AAAA,cAC7B;AAAA,YACF;AAAA,UACF,CAAC;AAAA;AAAA,MACH;AAEA,WAAK;AAAA,QACH,0BAA0B;AAAA,UACxB;AAAA,UACA,YAAY;AAAA,YACV,gBAAgB,EAAE,QAAQ,MAAM,KAAK,UAAU,SAAS,EAAE;AAAA,YAC1D,mBAAmB,MAAM;AAAA,UAC3B;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,mBAAmB,EAAE,OAAO,WAAW,OAAO,YAAY,CAAC;AAAA,IACxE;AAAA,EACF,CAAC;AACH;AAEA,IAAM,qBAAN,MAA8D;AAAA,EAM5D,YAAY,SAKT;AACD,SAAK,QAAQ,QAAQ;AACrB,SAAK,YAAY,QAAQ;AACzB,SAAK,QAAQ,QAAQ;AACrB,SAAK,cAAc,QAAQ;AAAA,EAC7B;AACF;;;ACjKO,SAAS,WAAc,OAAY,WAA0B;AAClE,MAAI,aAAa,GAAG;AAClB,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AAEA,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,WAAW;AAChD,WAAO,KAAK,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC;AAAA,EAC3C;AAEA,SAAO;AACT;;;ACQA,eAAsB,UAAiB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAC1B,GAiCoC;AAnEpC;AAoEE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,WAAW;AAAA,EACzB,CAAC;AAED,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AAErE,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB,EAAE,eAAe,gBAAgB,UAAU,CAAC;AAAA,QACrE,GAAG;AAAA;AAAA,QAEH,aAAa;AAAA,UACX,OAAO,MAAM,OAAO,IAAI,WAAS,KAAK,UAAU,KAAK,CAAC;AAAA,QACxD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,IAAI,OAAM,SAAQ;AAChB,YAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AACxD,YAAM,uBAAuB,MAAM;AAInC,UAAI,wBAAwB,MAAM;AAChC,cAAM,EAAE,YAAAC,aAAY,MAAM,IAAI,MAAM,MAAM,MAAM;AAE9C,iBAAO,WAAW;AAAA,YAChB,MAAM;AAAA,YACN,YAAY,0BAA0B;AAAA,cACpC;AAAA,cACA,YAAY;AAAA,gBACV,GAAG,sBAAsB;AAAA,kBACvB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,gBACD,GAAG;AAAA;AAAA,gBAEH,aAAa;AAAA,kBACX,OAAO,MAAM,OAAO,IAAI,WAAS,KAAK,UAAU,KAAK,CAAC;AAAA,gBACxD;AAAA,cACF;AAAA,YACF,CAAC;AAAA,YACD;AAAA,YACA,IAAI,OAAM,gBAAe;AArHrC,kBAAAC;AAsHc,oBAAM,gBAAgB,MAAM,MAAM,QAAQ;AAAA,gBACxC;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC;AAED,oBAAMD,cAAa,cAAc;AACjC,oBAAME,UAAQD,MAAA,cAAc,UAAd,OAAAA,MAAuB,EAAE,QAAQ,IAAI;AAEnD,0BAAY;AAAA,gBACV,0BAA0B;AAAA,kBACxB;AAAA,kBACA,YAAY;AAAA,oBACV,iBAAiB;AAAA,sBACf,QAAQ,MACND,YAAW,IAAI,eAAa,KAAK,UAAU,SAAS,CAAC;AAAA,oBACzD;AAAA,oBACA,mBAAmBE,OAAM;AAAA,kBAC3B;AAAA,gBACF,CAAC;AAAA,cACH;AAEA,qBAAO,EAAE,YAAAF,aAAY,OAAAE,OAAM;AAAA,YAC7B;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,aAAK;AAAA,UACH,0BAA0B;AAAA,YACxB;AAAA,YACA,YAAY;AAAA,cACV,iBAAiB;AAAA,gBACf,QAAQ,MACNF,YAAW,IAAI,eAAa,KAAK,UAAU,SAAS,CAAC;AAAA,cACzD;AAAA,cACA,mBAAmB,MAAM;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,QACH;AAEA,eAAO,IAAI,uBAAuB,EAAE,QAAQ,YAAAA,aAAY,MAAM,CAAC;AAAA,MACjE;AAGA,YAAM,cAAc,WAAW,QAAQ,oBAAoB;AAG3D,YAAM,aAA+B,CAAC;AACtC,UAAI,SAAS;AAEb,iBAAW,SAAS,aAAa;AAC/B,cAAM,EAAE,YAAY,oBAAoB,MAAM,IAAI,MAAM,MAAM,MAAM;AAElE,iBAAO,WAAW;AAAA,YAChB,MAAM;AAAA,YACN,YAAY,0BAA0B;AAAA,cACpC;AAAA,cACA,YAAY;AAAA,gBACV,GAAG,sBAAsB;AAAA,kBACvB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,gBACD,GAAG;AAAA;AAAA,gBAEH,aAAa;AAAA,kBACX,OAAO,MAAM,MAAM,IAAI,WAAS,KAAK,UAAU,KAAK,CAAC;AAAA,gBACvD;AAAA,cACF;AAAA,YACF,CAAC;AAAA,YACD;AAAA,YACA,IAAI,OAAM,gBAAe;AA5LrC,kBAAAC;AA6Lc,oBAAM,gBAAgB,MAAM,MAAM,QAAQ;AAAA,gBACxC,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cACF,CAAC;AAED,oBAAMD,cAAa,cAAc;AACjC,oBAAME,UAAQD,MAAA,cAAc,UAAd,OAAAA,MAAuB,EAAE,QAAQ,IAAI;AAEnD,0BAAY;AAAA,gBACV,0BAA0B;AAAA,kBACxB;AAAA,kBACA,YAAY;AAAA,oBACV,iBAAiB;AAAA,sBACf,QAAQ,MACND,YAAW,IAAI,eAAa,KAAK,UAAU,SAAS,CAAC;AAAA,oBACzD;AAAA,oBACA,mBAAmBE,OAAM;AAAA,kBAC3B;AAAA,gBACF,CAAC;AAAA,cACH;AAEA,qBAAO,EAAE,YAAAF,aAAY,OAAAE,OAAM;AAAA,YAC7B;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,mBAAW,KAAK,GAAG,kBAAkB;AACrC,kBAAU,MAAM;AAAA,MAClB;AAEA,WAAK;AAAA,QACH,0BAA0B;AAAA,UACxB;AAAA,UACA,YAAY;AAAA,YACV,iBAAiB;AAAA,cACf,QAAQ,MACN,WAAW,IAAI,eAAa,KAAK,UAAU,SAAS,CAAC;AAAA,YACzD;AAAA,YACA,mBAAmB;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,uBAAuB;AAAA,QAChC;AAAA,QACA;AAAA,QACA,OAAO,EAAE,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,IAAM,yBAAN,MAAsE;AAAA,EAKpE,YAAY,SAIT;AACD,SAAK,SAAS,QAAQ;AACtB,SAAK,aAAa,QAAQ;AAC1B,SAAK,QAAQ,QAAQ;AAAA,EACvB;AACF;;;AChQA,SAAS,8BAA8B;AACvC,SAAS,qBAAqB;;;ACK9B,SAAS,mBAAAC,wBAAuB;;;ACNhC,IAAM,qBAAqB;AAAA,EACzB,EAAE,UAAU,aAAsB,OAAO,CAAC,IAAM,IAAM,EAAI,EAAE;AAAA,EAC5D,EAAE,UAAU,aAAsB,OAAO,CAAC,KAAM,IAAM,IAAM,EAAI,EAAE;AAAA,EAClE,EAAE,UAAU,cAAuB,OAAO,CAAC,KAAM,GAAI,EAAE;AAAA,EACvD,EAAE,UAAU,cAAuB,OAAO,CAAC,IAAM,IAAM,IAAM,EAAI,EAAE;AACrE;AAEO,SAAS,oBACd,OACqE;AACrE,aAAW,EAAE,OAAO,SAAS,KAAK,oBAAoB;AACpD,QACE,MAAM,UAAU,MAAM,UACtB,MAAM,MAAM,CAAC,MAAM,UAAU,MAAM,KAAK,MAAM,IAAI,GAClD;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACpBA,SAAS,qBAAqB;AAE9B,eAAsB,SAAS;AAAA,EAC7B;AAAA,EACA,sBAAsB;AACxB,GAMG;AAXH;AAYE,QAAM,UAAU,IAAI,SAAS;AAC7B,MAAI;AACF,UAAM,WAAW,MAAM,oBAAoB,OAAO;AAElD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,cAAc;AAAA,QACtB,KAAK;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,SAAS;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,MACL,MAAM,IAAI,WAAW,MAAM,SAAS,YAAY,CAAC;AAAA,MACjD,WAAU,cAAS,QAAQ,IAAI,cAAc,MAAnC,YAAwC;AAAA,IACpD;AAAA,EACF,SAAS,OAAO;AACd,QAAI,cAAc,gBAAgB,KAAK,GAAG;AACxC,YAAM;AAAA,IACR;AAEA,UAAM,IAAI,cAAc,EAAE,KAAK,SAAS,OAAO,MAAM,CAAC;AAAA,EACxD;AACF;;;ACnCA,SAAS,+BAA+B;AACxC;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAaA,SAAS,iCAAiC,SAA8B;AAC7E,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,aAAa;AAClC,WAAO,0BAA0B,IAAI,WAAW,OAAO,CAAC;AAAA,EAC1D;AAEA,SAAO,0BAA0B,OAAO;AAC1C;AAQO,SAAS,+BACd,SACY;AACZ,MAAI,mBAAmB,YAAY;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,aAAO,0BAA0B,OAAO;AAAA,IAC1C,SAAS,OAAO;AACd,YAAM,IAAI,wBAAwB;AAAA,QAChC,SACE;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,aAAa;AAClC,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAEA,QAAM,IAAI,wBAAwB,EAAE,QAAQ,CAAC;AAC/C;AAQO,SAAS,wBAAwB,YAAgC;AACtE,MAAI;AACF,WAAO,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,EAC5C,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACrD;AACF;;;AC1EO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAGjD,YAAY;AAAA,IACV;AAAA,IACA,UAAU,0BAA0B,IAAI;AAAA,EAC1C,GAGG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,0BACL,OACkC;AAClC,WACE,iBAAiB,SACjB,MAAM,SAAS,gCACf,OAAQ,MAAkC,SAAS;AAAA,EAEvD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;;;AJrBA,eAAsB,6BAA6B;AAAA,EACjD;AAAA,EACA,yBAAyB;AAAA,EACzB,yBAAyB;AAC3B,GAImC;AACjC,QAAM,wBAA+C,CAAC;AAEtD,MAAI,OAAO,UAAU,MAAM;AACzB,0BAAsB,KAAK,EAAE,MAAM,UAAU,SAAS,OAAO,OAAO,CAAC;AAAA,EACvE;AAEA,QAAM,mBACJ,0BAA0B,OAAO,YAAY,OACzC,OACA,MAAM,eAAe,OAAO,UAAU,sBAAsB;AAElE,QAAM,aAAa,OAAO;AAC1B,UAAQ,YAAY;AAAA,IAClB,KAAK,UAAU;AACb,4BAAsB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,OAAO,CAAC;AAAA,MACjD,CAAC;AACD;AAAA,IACF;AAAA,IAEA,KAAK,YAAY;AACf,4BAAsB;AAAA,QACpB,GAAG,OAAO,SAAS;AAAA,UACjB,CAAC,YACC,8BAA8B,SAAS,gBAAgB;AAAA,QAC3D;AAAA,MACF;AACA;AAAA,IACF;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,MAAM,4BAA4B,gBAAgB,EAAE;AAAA,IAChE;AAAA,EACF;AAEA,SAAO;AACT;AASO,SAAS,8BACd,SACA,kBAIwB;AACxB,QAAM,OAAO,QAAQ;AACrB,UAAQ,MAAM;AAAA,IACZ,KAAK,UAAU;AACb,aAAO,EAAE,MAAM,UAAU,SAAS,QAAQ,QAAQ;AAAA,IACpD;AAAA,IAEA,KAAK,QAAQ;AACX,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ;AAAA,UACvB,CAAC,SAA6D;AA/FxE;AAgGY,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO;AAAA,cACT;AAAA,cAEA,KAAK,SAAS;AACZ,oBAAI,KAAK,iBAAiB,KAAK;AAC7B,sBAAI,oBAAoB,MAAM;AAC5B,2BAAO;AAAA,sBACL,MAAM;AAAA,sBACN,OAAO,KAAK;AAAA,sBACZ,UAAU,KAAK;AAAA,oBACjB;AAAA,kBACF,OAAO;AACL,0BAAM,kBACJ,iBAAiB,KAAK,MAAM,SAAS,CAAC;AACxC,2BAAO;AAAA,sBACL,MAAM;AAAA,sBACN,OAAO,gBAAgB;AAAA,sBACvB,WAAU,UAAK,aAAL,YAAiB,gBAAgB;AAAA,oBAC7C;AAAA,kBACF;AAAA,gBACF;AAGA,oBAAI,OAAO,KAAK,UAAU,UAAU;AAClC,sBAAI;AACF,0BAAM,MAAM,IAAI,IAAI,KAAK,KAAK;AAE9B,4BAAQ,IAAI,UAAU;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK,UAAU;AACb,4BAAI,oBAAoB,MAAM;AAC5B,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OAAO;AAAA,4BACP,UAAU,KAAK;AAAA,0BACjB;AAAA,wBACF,OAAO;AACL,gCAAM,kBAAkB,iBAAiB,KAAK,KAAK;AACnD,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OAAO,gBAAgB;AAAA,4BACvB,WAAU,UAAK,aAAL,YAAiB,gBAAgB;AAAA,0BAC7C;AAAA,wBACF;AAAA,sBACF;AAAA,sBACA,KAAK,SAAS;AACZ,4BAAI;AACF,gCAAM,CAAC,QAAQ,aAAa,IAAI,KAAK,MAAM,MAAM,GAAG;AACpD,gCAAM,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAElD,8BAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,kCAAM,IAAI,MAAM,yBAAyB;AAAA,0BAC3C;AAEA,iCAAO;AAAA,4BACL,MAAM;AAAA,4BACN,OACE,+BAA+B,aAAa;AAAA,4BAC9C;AAAA,0BACF;AAAA,wBACF,SAAS,OAAO;AACd,gCAAM,IAAI;AAAA,4BACR,8BAA8BC;AAAA,8BAC5B;AAAA,4BACF,CAAC;AAAA,0BACH;AAAA,wBACF;AAAA,sBACF;AAAA,sBACA,SAAS;AACP,8BAAM,IAAI;AAAA,0BACR,6BAA6B,IAAI,QAAQ;AAAA,wBAC3C;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,SAAS,UAAU;AAAA,kBAEnB;AAAA,gBACF;AAEA,sBAAM,aAAa,+BAA+B,KAAK,KAAK;AAE5D,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,WAAU,UAAK,aAAL,YAAiB,oBAAoB,UAAU;AAAA,gBAC3D;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,KAAK,aAAa;AAChB,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,QACnD;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,QAAQ,QAAQ;AAAA;AAAA,UAEvB,UAAQ,KAAK,SAAS,UAAU,KAAK,SAAS;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,aAAO;AAAA,IACT;AAAA,IAEA,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,eAAe,eACb,UACA,wBAC6E;AAC7E,QAAM,OAAO,SACV,OAAO,aAAW,QAAQ,SAAS,MAAM,EACzC,IAAI,aAAW,QAAQ,OAAO,EAC9B;AAAA,IAAO,CAAC,YACP,MAAM,QAAQ,OAAO;AAAA,EACvB,EACC,KAAK,EACL,OAAO,CAAC,SAA4B,KAAK,SAAS,OAAO,EACzD,IAAI,UAAQ,KAAK,KAAK,EACtB;AAAA,IAAI;AAAA;AAAA,MAEH,OAAO,SAAS,aACf,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,QAAQ,KACjD,IAAI,IAAI,IAAI,IACZ;AAAA;AAAA,EACN,EACC,OAAO,CAAC,UAAwB,iBAAiB,GAAG;AAGvD,QAAM,mBAAmB,MAAM,QAAQ;AAAA,IACrC,KAAK,IAAI,OAAM,SAAQ;AAAA,MACrB;AAAA,MACA,MAAM,MAAM,uBAAuB,EAAE,IAAI,CAAC;AAAA,IAC5C,EAAE;AAAA,EACJ;AAEA,SAAO,OAAO;AAAA,IACZ,iBAAiB,IAAI,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,IAAI,SAAS,GAAG,IAAI,CAAC;AAAA,EAChE;AACF;;;AK5PA,SAAS,0BAA0B;AAkB5B,SAAS,mBAAmB,QAAiC;AAClE,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,UAAU,QAAQ,OAAO,YAAY,MAAM;AACpD,UAAM,IAAI,mBAAmB;AAAA,MAC3B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,YAAY,MAAM;AAC3B,eAAW,WAAW,OAAO,UAAU;AACrC,UAAI,QAAQ,SAAS,YAAY,OAAO,QAAQ,YAAY,UAAU;AACpE,cAAM,IAAI,mBAAmB;AAAA,UAC3B;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,OAAO,UAAU,OACpB;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,OAAO;AAAA,IACf,UAAU;AAAA,IACV,QAAQ,OAAO;AAAA,EACjB,IACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,OAAO;AAAA;AAAA,IACjB,QAAQ,OAAO;AAAA,EACjB;AACN;;;ACzDA,SAAS,4BAA4B;AAM9B,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA+B;AAC7B,MAAI,aAAa,MAAM;AACrB,QAAI,CAAC,OAAO,UAAU,SAAS,GAAG;AAChC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,YAAY,GAAG;AACjB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,eAAe,MAAM;AACvB,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,OAAO,oBAAoB,UAAU;AACvC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,oBAAoB,MAAM;AAC5B,QAAI,OAAO,qBAAqB,UAAU;AACxC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,QAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,cAAc,MAAM;AACtB,QAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AACjC,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,aAAa,GAAG;AAClB,YAAM,IAAI,qBAAqB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,oCAAe;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA,eACE,iBAAiB,QAAQ,cAAc,SAAS,IAC5C,gBACA;AAAA,IACN;AAAA,IACA,YAAY,kCAAc;AAAA,EAC5B;AACF;;;ACrFO,SAAS,8BAA8B,OAGrB;AACvB,SAAO;AAAA,IACL,cAAc,MAAM;AAAA,IACpB,kBAAkB,MAAM;AAAA,IACxB,aAAa,MAAM,eAAe,MAAM;AAAA,EAC1C;AACF;;;ACvCO,SAAS,uBACd,MACA;AAAA,EACE;AAAA,EACA;AACF,GACA;AANF;AAOE,QAAM,UAAU,IAAI,SAAQ,kCAAM,YAAN,YAAiB,CAAC,CAAC;AAE/C,MAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AAChC,YAAQ,IAAI,gBAAgB,WAAW;AAAA,EACzC;AAEA,MAAI,sBAAsB,QAAW;AACnC,YAAQ,IAAI,2BAA2B,iBAAiB;AAAA,EAC1D;AAEA,SAAO;AACT;;;AClBA,SAAoB,uBAAuB;AAG3C,OAAO,qBAAqB;AAK5B,IAAM,eAAe,OAAO,kBAAkB;AAyBvC,SAAS,WACdC,aACA;AAAA,EACE;AACF,IAII,CAAC,GACW;AAChB,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,IAChB,OAAO;AAAA;AAAA,IACP,CAAC,eAAe,GAAG;AAAA,IACnB,YAAAA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAiC;AACjD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,MAAM,YAAY,MAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,QACgB;AAChB,SAAO,SAAS,MAAM,IAAI,SAAS,UAAU,MAAM;AACrD;AAEO,SAAS,UAAkBC,YAA6C;AAC7E,SAAO;AAAA;AAAA,IAEL,gBAAgBA,UAAS;AAAA,IACzB;AAAA,MACE,UAAU,WAAS;AACjB,cAAM,SAASA,WAAU,UAAU,KAAK;AACxC,eAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;;;AChFA,IAAM,wBAAwB;AAC9B,IAAM,wBACJ;AAEK,SAAS,2BAA2B;AAAA,EACzC;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,eAAe;AACjB,GAKW;AACT,SAAO;AAAA,IACL;AAAA,IACA,UAAU,OAAO,KAAK;AAAA;AAAA,IACtB;AAAA,IACA,KAAK,UAAU,MAAM;AAAA,IACrB;AAAA,EACF,EACG,OAAO,UAAQ,QAAQ,IAAI,EAC3B,KAAK,IAAI;AACd;;;AXoCA,eAAsB,eAAkB;AAAA,EACtC;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB,GAAG;AACL,GA+B8C;AAzG9C;AA0GE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,GAAG,UAAU,WAAW;AAAA,EACtC,CAAC;AAED,QAAM,SAAS,SAAS,WAAW;AAEnC,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AACrE,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB;AAAA,UACvB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,QACD,GAAG;AAAA;AAAA,QAEH,aAAa;AAAA,UACX,OAAO,MAAM,KAAK,UAAU,EAAE,QAAQ,QAAQ,SAAS,CAAC;AAAA,QAC1D;AAAA,QACA,aAAa;AAAA,UACX,OAAO,MAAM,KAAK,UAAU,OAAO,UAAU;AAAA,QAC/C;AAAA,QACA,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,IAAI,OAAM,SAAQ;AAChB,YAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AAGxD,UAAI,SAAS,UAAU,QAAQ,MAAM;AACnC,eAAO,MAAM;AAAA,MACf;AAEA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,cAAQ,MAAM;AAAA,QACZ,KAAK,QAAQ;AACX,gBAAM,kBAAkB,mBAAmB;AAAA,YACzC,QAAQ,2BAA2B;AAAA,cACjC;AAAA,cACA,QAAQ,OAAO;AAAA,YACjB,CAAC;AAAA,YACD;AAAA,YACA;AAAA,UACF,CAAC;AAED,gBAAM,iBAAiB,MAAM,6BAA6B;AAAA,YACxD,QAAQ;AAAA,YACR,wBAAwB,MAAM;AAAA,UAChC,CAAC;AAED,gBAAM,cAAc,gBAAgB;AAEpC,gBAAM,iBAAiB,MAAM;AAAA,YAAM,MACjC,WAAW;AAAA,cACT,MAAM;AAAA,cACN,YAAY,0BAA0B;AAAA,gBACpC;AAAA,gBACA,YAAY;AAAA,kBACV,GAAG,sBAAsB;AAAA,oBACvB,eAAe;AAAA,oBACf;AAAA,kBACF,CAAC;AAAA,kBACD,GAAG;AAAA,kBACH,oBAAoB;AAAA,oBAClB,OAAO,MAAM;AAAA,kBACf;AAAA,kBACA,sBAAsB;AAAA,oBACpB,OAAO,MAAM,KAAK,UAAU,cAAc;AAAA,kBAC5C;AAAA,kBACA,oBAAoB;AAAA;AAAA,kBAGpB,wBAAwB,MAAM;AAAA,kBAC9B,iBAAiB,MAAM;AAAA,kBACvB,6BAA6B,SAAS;AAAA,kBACtC,8BAA8B,SAAS;AAAA,kBACvC,wBAAwB,SAAS;AAAA,gBACnC;AAAA,cACF,CAAC;AAAA,cACD;AAAA,cACA,IAAI,OAAMC,UAAQ;AAChB,sBAAMC,UAAS,MAAM,MAAM,WAAW;AAAA,kBACpC,MAAM,EAAE,MAAM,cAAc;AAAA,kBAC5B,GAAG,oBAAoB,QAAQ;AAAA,kBAC/B;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,kBACA;AAAA,gBACF,CAAC;AAED,oBAAIA,QAAO,SAAS,QAAW;AAC7B,wBAAM,IAAI,uBAAuB;AAAA,gBACnC;AAGA,gBAAAD,MAAK;AAAA,kBACH,0BAA0B;AAAA,oBACxB;AAAA,oBACA,YAAY;AAAA,sBACV,mBAAmBC,QAAO;AAAA,sBAC1B,yBAAyBA,QAAO,MAAM;AAAA,sBACtC,6BACEA,QAAO,MAAM;AAAA,sBACf,oBAAoB,EAAE,QAAQ,MAAMA,QAAO,KAAK;AAAA;AAAA,sBAGhD,kCAAkC,CAACA,QAAO,YAAY;AAAA,sBACtD,8BAA8BA,QAAO,MAAM;AAAA,sBAC3C,kCACEA,QAAO,MAAM;AAAA,oBACjB;AAAA,kBACF,CAAC;AAAA,gBACH;AAEA,uBAAO,EAAE,GAAGA,SAAQ,YAAYA,QAAO,KAAK;AAAA,cAC9C;AAAA,YACF,CAAC;AAAA,UACH;AAEA,mBAAS,eAAe;AACxB,yBAAe,eAAe;AAC9B,kBAAQ,eAAe;AACvB,qBAAW,eAAe;AAC1B,wBAAc,eAAe;AAC7B,qBAAW,eAAe;AAE1B;AAAA,QACF;AAAA,QAEA,KAAK,QAAQ;AACX,gBAAM,kBAAkB,mBAAmB;AAAA,YACzC;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAED,gBAAM,iBAAiB,MAAM,6BAA6B;AAAA,YACxD,QAAQ;AAAA,YACR,wBAAwB,MAAM;AAAA,UAChC,CAAC;AACD,gBAAM,cAAc,gBAAgB;AAEpC,gBAAM,iBAAiB,MAAM;AAAA,YAAM,MACjC,WAAW;AAAA,cACT,MAAM;AAAA,cACN,YAAY,0BAA0B;AAAA,gBACpC;AAAA,gBACA,YAAY;AAAA,kBACV,GAAG,sBAAsB;AAAA,oBACvB,eAAe;AAAA,oBACf;AAAA,kBACF,CAAC;AAAA,kBACD,GAAG;AAAA,kBACH,oBAAoB;AAAA,oBAClB,OAAO,MAAM;AAAA,kBACf;AAAA,kBACA,sBAAsB;AAAA,oBACpB,OAAO,MAAM,KAAK,UAAU,cAAc;AAAA,kBAC5C;AAAA,kBACA,oBAAoB;AAAA;AAAA,kBAGpB,wBAAwB,MAAM;AAAA,kBAC9B,iBAAiB,MAAM;AAAA,kBACvB,6BAA6B,SAAS;AAAA,kBACtC,8BAA8B,SAAS;AAAA,kBACvC,wBAAwB,SAAS;AAAA,gBACnC;AAAA,cACF,CAAC;AAAA,cACD;AAAA,cACA,IAAI,OAAMD,UAAQ;AAhShC,oBAAAE,KAAA;AAiSgB,sBAAMD,UAAS,MAAM,MAAM,WAAW;AAAA,kBACpC,MAAM;AAAA,oBACJ,MAAM;AAAA,oBACN,MAAM;AAAA,sBACJ,MAAM;AAAA,sBACN,MAAM;AAAA,sBACN,aAAa;AAAA,sBACb,YAAY,OAAO;AAAA,oBACrB;AAAA,kBACF;AAAA,kBACA,GAAG,oBAAoB,QAAQ;AAAA,kBAC/B;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,kBACA;AAAA,gBACF,CAAC;AAED,sBAAM,cAAa,MAAAC,MAAAD,QAAO,cAAP,gBAAAC,IAAmB,OAAnB,mBAAuB;AAE1C,oBAAI,eAAe,QAAW;AAC5B,wBAAM,IAAI,uBAAuB;AAAA,gBACnC;AAGA,gBAAAF,MAAK;AAAA,kBACH,0BAA0B;AAAA,oBACxB;AAAA,oBACA,YAAY;AAAA,sBACV,mBAAmBC,QAAO;AAAA,sBAC1B,yBAAyBA,QAAO,MAAM;AAAA,sBACtC,6BACEA,QAAO,MAAM;AAAA,sBACf,oBAAoB,EAAE,QAAQ,MAAM,WAAW;AAAA;AAAA,sBAG/C,kCAAkC,CAACA,QAAO,YAAY;AAAA,sBACtD,8BAA8BA,QAAO,MAAM;AAAA,sBAC3C,kCACEA,QAAO,MAAM;AAAA,oBACjB;AAAA,kBACF,CAAC;AAAA,gBACH;AAEA,uBAAO,EAAE,GAAGA,SAAQ,WAAW;AAAA,cACjC;AAAA,YACF,CAAC;AAAA,UACH;AAEA,mBAAS,eAAe;AACxB,yBAAe,eAAe;AAC9B,kBAAQ,eAAe;AACvB,qBAAW,eAAe;AAC1B,wBAAc,eAAe;AAC7B,qBAAW,eAAe;AAE1B;AAAA,QACF;AAAA,QAEA,KAAK,QAAW;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QAEA,SAAS;AACP,gBAAM,mBAA0B;AAChC,gBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,QACzD;AAAA,MACF;AAEA,YAAM,cAAc,cAAc,EAAE,MAAM,QAAQ,OAAO,CAAC;AAE1D,UAAI,CAAC,YAAY,SAAS;AACxB,cAAM,YAAY;AAAA,MACpB;AAGA,WAAK;AAAA,QACH,0BAA0B;AAAA,UACxB;AAAA,UACA,YAAY;AAAA,YACV,mBAAmB;AAAA,YACnB,yBAAyB,MAAM;AAAA,YAC/B,6BAA6B,MAAM;AAAA,YACnC,oBAAoB;AAAA,cAClB,QAAQ,MAAM,KAAK,UAAU,YAAY,KAAK;AAAA,YAChD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,4BAA4B;AAAA,QACrC,QAAQ,YAAY;AAAA,QACpB;AAAA,QACA,OAAO,8BAA8B,KAAK;AAAA,QAC1C;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,IAAM,8BAAN,MAAwE;AAAA,EAQtE,YAAY,SAOT;AACD,SAAK,SAAS,QAAQ;AACtB,SAAK,eAAe,QAAQ;AAC5B,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,QAAQ;AACxB,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,QAAQ;AAAA,EAC1B;AAAA,EAEA,eAAe,MAA+B;AAhahD;AAiaI,WAAO,IAAI,SAAS,KAAK,UAAU,KAAK,MAAM,GAAG;AAAA,MAC/C,SAAQ,kCAAM,WAAN,YAAgB;AAAA,MACxB,SAAS,uBAAuB,MAAM;AAAA,QACpC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAKO,IAAM,8BAA8B;;;AYxa3C,SAAS,yBAAyB;AAClC;AAAA,EAEE;AAAA,EACA;AAAA,OACK;;;ACRA,SAAS,0BACd,QACA,aACwB;AACxB,QAAM,oBAAyB,OAAO;AAAA,IACpC,IAAI,gBAAgB,WAAW;AAAA,EACjC;AAEA,oBAAkB,OAAO,aAAa,IAAI,MAAM;AAC9C,UAAM,SAAS,kBAAkB,UAAU;AAC3C,WAAO;AAAA,MACL,MAAM,OAAwC;AAC5C,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,eAAO,OAAO,EAAE,MAAM,MAAM,OAAO,OAAU,IAAI,EAAE,MAAM,OAAO,MAAM;AAAA,MACxE;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AChBO,IAAM,iBAAN,MAAwB;AAAA,EAAxB;AACL,SAAQ,SAGmC,EAAE,MAAM,UAAU;AAE7D,SAAQ,WAA6C;AACrD,SAAQ,UAAkD;AAAA;AAAA,EAE1D,IAAI,QAAoB;AACtB,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,UAAU,IAAI,QAAW,CAAC,SAAS,WAAW;AACjD,UAAI,KAAK,OAAO,SAAS,YAAY;AACnC,gBAAQ,KAAK,OAAO,KAAK;AAAA,MAC3B,WAAW,KAAK,OAAO,SAAS,YAAY;AAC1C,eAAO,KAAK,OAAO,KAAK;AAAA,MAC1B;AAEA,WAAK,WAAW;AAChB,WAAK,UAAU;AAAA,IACjB,CAAC;AAED,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,QAAQ,OAAgB;AAjC1B;AAkCI,SAAK,SAAS,EAAE,MAAM,YAAY,MAAM;AAExC,QAAI,KAAK,SAAS;AAChB,iBAAK,aAAL,8BAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,OAAO,OAAsB;AAzC/B;AA0CI,SAAK,SAAS,EAAE,MAAM,YAAY,MAAM;AAExC,QAAI,KAAK,SAAS;AAChB,iBAAK,YAAL,8BAAe;AAAA,IACjB;AAAA,EACF;AACF;;;AFqCA,eAAsB,aAAgB;AAAA,EACpC;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB;AAAA,EACA,GAAG;AACL,GAkE4C;AApK5C;AAqKE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,GAAG,UAAU,WAAW;AAAA,EACtC,CAAC;AAED,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AAErE,QAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AAExD,QAAM,SAAS,SAAS,WAAW;AAEnC,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB;AAAA,UACvB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,QACD,GAAG;AAAA;AAAA,QAEH,aAAa;AAAA,UACX,OAAO,MAAM,KAAK,UAAU,EAAE,QAAQ,QAAQ,SAAS,CAAC;AAAA,QAC1D;AAAA,QACA,aAAa,EAAE,OAAO,MAAM,KAAK,UAAU,OAAO,UAAU,EAAE;AAAA,QAC9D,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,IAAI,OAAM,aAAY;AAEpB,UAAI,SAAS,UAAU,QAAQ,MAAM;AACnC,eAAO,MAAM;AAAA,MACf;AAEA,UAAI;AACJ,UAAI;AAKJ,cAAQ,MAAM;AAAA,QACZ,KAAK,QAAQ;AACX,gBAAM,kBAAkB,mBAAmB;AAAA,YACzC,QAAQ,2BAA2B;AAAA,cACjC;AAAA,cACA,QAAQ,OAAO;AAAA,YACjB,CAAC;AAAA,YACD;AAAA,YACA;AAAA,UACF,CAAC;AAED,wBAAc;AAAA,YACZ,MAAM,EAAE,MAAM,cAAc;AAAA,YAC5B,GAAG,oBAAoB,QAAQ;AAAA,YAC/B,aAAa,gBAAgB;AAAA,YAC7B,QAAQ,MAAM,6BAA6B;AAAA,cACzC,QAAQ;AAAA,cACR,wBAAwB,MAAM;AAAA,YAChC,CAAC;AAAA,YACD;AAAA,YACA;AAAA,UACF;AAEA,wBAAc;AAAA,YACZ,WAAW,CAAC,OAAO,eAAe;AAChC,sBAAQ,MAAM,MAAM;AAAA,gBAClB,KAAK;AACH,6BAAW,QAAQ,MAAM,SAAS;AAClC;AAAA,gBACF,KAAK;AAAA,gBACL,KAAK;AACH,6BAAW,QAAQ,KAAK;AACxB;AAAA,cACJ;AAAA,YACF;AAAA,UACF;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,QAAQ;AACX,gBAAM,kBAAkB,mBAAmB;AAAA,YACzC;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAED,wBAAc;AAAA,YACZ,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,YAAY,OAAO;AAAA,cACrB;AAAA,YACF;AAAA,YACA,GAAG,oBAAoB,QAAQ;AAAA,YAC/B,aAAa,gBAAgB;AAAA,YAC7B,QAAQ,MAAM,6BAA6B;AAAA,cACzC,QAAQ;AAAA,cACR,wBAAwB,MAAM;AAAA,YAChC,CAAC;AAAA,YACD;AAAA,YACA;AAAA,UACF;AAEA,wBAAc;AAAA,YACZ,UAAU,OAAO,YAAY;AAC3B,sBAAQ,MAAM,MAAM;AAAA,gBAClB,KAAK;AACH,6BAAW,QAAQ,MAAM,aAAa;AACtC;AAAA,gBACF,KAAK;AAAA,gBACL,KAAK;AACH,6BAAW,QAAQ,KAAK;AACxB;AAAA,cACJ;AAAA,YACF;AAAA,UACF;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,QAAW;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QAEA,SAAS;AACP,gBAAM,mBAA0B;AAChC,gBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,QACzD;AAAA,MACF;AAGA,YAAM;AAAA,QACJ,QAAQ,EAAE,QAAQ,UAAU,YAAY;AAAA,QACxC;AAAA,MACF,IAAI,MAAM;AAAA,QAAM,MACd,WAAW;AAAA,UACT,MAAM;AAAA,UACN,YAAY,0BAA0B;AAAA,YACpC;AAAA,YACA,YAAY;AAAA,cACV,GAAG,sBAAsB;AAAA,gBACvB,eAAe;AAAA,gBACf;AAAA,cACF,CAAC;AAAA,cACD,GAAG;AAAA,cACH,oBAAoB;AAAA,gBAClB,OAAO,MAAM,YAAY;AAAA,cAC3B;AAAA,cACA,sBAAsB;AAAA,gBACpB,OAAO,MAAM,KAAK,UAAU,YAAY,MAAM;AAAA,cAChD;AAAA,cACA,oBAAoB;AAAA;AAAA,cAGpB,wBAAwB,MAAM;AAAA,cAC9B,iBAAiB,MAAM;AAAA,cACvB,6BAA6B,SAAS;AAAA,cACtC,8BAA8B,SAAS;AAAA,cACvC,wBAAwB,SAAS;AAAA,YACnC;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UACA,aAAa;AAAA,UACb,IAAI,OAAME,mBAAiB;AAAA,YACzB,QAAQ,MAAM,MAAM,SAAS,WAAW;AAAA,YACxC,cAAAA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,0BAA0B;AAAA,QACnC,QAAQ,OAAO,YAAY,IAAI,gBAAgB,WAAW,CAAC;AAAA,QAC3D;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,IAAM,4BAAN,MAAoE;AAAA,EAQlE,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAWG;AACD,SAAK,WAAW;AAChB,SAAK,cAAc;AAGnB,SAAK,gBAAgB,IAAI,eAAkB;AAG3C,QAAI;AAGJ,SAAK,QAAQ,IAAI,QAA8B,aAAW;AACxD,qBAAe;AAAA,IACjB,CAAC;AAGD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAGJ,QAAI,kBAAkB;AACtB,QAAI,QAAQ;AACZ,QAAI,eAA2C;AAC/C,QAAI,aAAa;AAEjB,UAAM,OAAO;AACb,SAAK,iBAAiB,OAAO;AAAA,MAC3B,IAAI,gBAAqE;AAAA,QACvE,MAAM,UAAU,OAAO,YAA2B;AAEhD,cAAI,YAAY;AACd,yBAAa;AACb,yBAAa,SAAS,sBAAsB;AAAA,UAC9C;AAGA,cAAI,OAAO,UAAU,UAAU;AAC7B,+BAAmB;AACnB,qBAAS;AAET,kBAAM,gBAAgB;AAAA,cACpB;AAAA,YACF;AAEA,gBAAI,CAAC,gBAAgB,cAAc,aAAa,GAAG;AACjD,6BAAe;AAEf,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,QAAQ;AAAA,cACV,CAAC;AAED,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW;AAAA,cACb,CAAC;AAED,sBAAQ;AAAA,YACV;AAEA;AAAA,UACF;AAEA,kBAAQ,MAAM,MAAM;AAAA,YAClB,KAAK,UAAU;AAEb,kBAAI,UAAU,IAAI;AAChB,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,WAAW;AAAA,gBACb,CAAC;AAAA,cACH;AAGA,6BAAe,MAAM;AAGrB,sBAAQ,8BAA8B,MAAM,KAAK;AAEjD,yBAAW,QAAQ,EAAE,GAAG,OAAO,MAAM,CAAC;AAGtC,2BAAa,KAAK;AAGlB,oBAAM,mBAAmB,kBAAkB;AAAA,gBACzC,OAAO;AAAA,gBACP;AAAA,cACF,CAAC;AAED,kBAAI,iBAAiB,SAAS;AAC5B,yBAAS,iBAAiB;AAC1B,qBAAK,cAAc,QAAQ,MAAM;AAAA,cACnC,OAAO;AACL,wBAAQ,iBAAiB;AACzB,qBAAK,cAAc,OAAO,KAAK;AAAA,cACjC;AAEA;AAAA,YACF;AAAA,YAEA,SAAS;AACP,yBAAW,QAAQ,KAAK;AACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAGA,MAAM,MAAM,YAAY;AACtB,cAAI;AACF,kBAAM,aAAa,wBAAS;AAAA,cAC1B,cAAc;AAAA,cACd,kBAAkB;AAAA,cAClB,aAAa;AAAA,YACf;AAEA,yBAAa;AAAA,cACX,0BAA0B;AAAA,gBACxB;AAAA,gBACA,YAAY;AAAA,kBACV,mBAAmB;AAAA,kBACnB,yBAAyB,WAAW;AAAA,kBACpC,6BAA6B,WAAW;AAAA,kBACxC,oBAAoB;AAAA,oBAClB,QAAQ,MAAM,KAAK,UAAU,MAAM;AAAA,kBACrC;AAAA;AAAA,kBAGA,8BAA8B,WAAW;AAAA,kBACzC,kCAAkC,WAAW;AAAA,kBAC7C,kCAAkC,CAAC,YAAY;AAAA,gBACjD;AAAA,cACF,CAAC;AAAA,YACH;AAGA,yBAAa,IAAI;AAGjB,qBAAS;AAAA,cACP,0BAA0B;AAAA,gBACxB;AAAA,gBACA,YAAY;AAAA,kBACV,yBAAyB,WAAW;AAAA,kBACpC,6BAA6B,WAAW;AAAA,kBACxC,oBAAoB;AAAA,oBAClB,QAAQ,MAAM,KAAK,UAAU,MAAM;AAAA,kBACrC;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAGA,mBAAM,qCAAW;AAAA,cACf,OAAO;AAAA,cACP;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF,SAASC,QAAO;AACd,uBAAW,MAAMA,MAAK;AAAA,UACxB,UAAE;AACA,qBAAS,IAAI;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,IAAI,SAAqB;AACvB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EAEA,IAAI,sBAA2D;AAC7D,WAAO,0BAA0B,KAAK,gBAAgB;AAAA,MACpD,UAAU,OAAO,YAAY;AAC3B,gBAAQ,MAAM,MAAM;AAAA,UAClB,KAAK;AACH,uBAAW,QAAQ,MAAM,MAAM;AAC/B;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH;AAAA,UAEF,KAAK;AACH,uBAAW,MAAM,MAAM,KAAK;AAC5B;AAAA,UAEF,SAAS;AACP,kBAAM,mBAA0B;AAChC,kBAAM,IAAI,MAAM,2BAA2B,gBAAgB,EAAE;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,IAAI,aAA0C;AAC5C,WAAO,0BAA0B,KAAK,gBAAgB;AAAA,MACpD,UAAU,OAAO,YAAY;AAC3B,gBAAQ,MAAM,MAAM;AAAA,UAClB,KAAK;AACH,uBAAW,QAAQ,MAAM,SAAS;AAClC;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH;AAAA,UAEF,KAAK;AACH,uBAAW,MAAM,MAAM,KAAK;AAC5B;AAAA,UAEF,SAAS;AACP,kBAAM,mBAA0B;AAChC,kBAAM,IAAI,MAAM,2BAA2B,gBAAgB,EAAE;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,IAAI,aAAuD;AACzD,WAAO,0BAA0B,KAAK,gBAAgB;AAAA,MACpD,UAAU,OAAO,YAAY;AAC3B,mBAAW,QAAQ,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,yBACE,UACA,MACA;AAlnBJ;AAmnBI,aAAS,WAAU,kCAAM,WAAN,YAAgB,KAAK;AAAA,MACtC,gBAAgB;AAAA,MAChB,GAAG,6BAAM;AAAA,IACX,CAAC;AAED,UAAM,SAAS,KAAK,WACjB,YAAY,IAAI,kBAAkB,CAAC,EACnC,UAAU;AAEb,UAAM,OAAO,YAAY;AACvB,UAAI;AACF,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI;AAAM;AACV,mBAAS,MAAM,KAAK;AAAA,QACtB;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,MACR,UAAE;AACA,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAEA,SAAK;AAAA,EACP;AAAA,EAEA,qBAAqB,MAA+B;AA7oBtD;AA8oBI,WAAO,IAAI,SAAS,KAAK,WAAW,YAAY,IAAI,kBAAkB,CAAC,GAAG;AAAA,MACxE,SAAQ,kCAAM,WAAN,YAAgB;AAAA,MACxB,SAAS,uBAAuB,MAAM;AAAA,QACpC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAKO,IAAM,4BAA4B;;;AG1pBlC,SAAS,iBACd,QACmC;AACnC,SAAO,UAAU,QAAQ,OAAO,KAAK,MAAM,EAAE,SAAS;AACxD;;;ACKO,SAAS,0BAEd;AAAA,EACA;AAAA,EACA;AACF,GAME;AACA,MAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,WAAO;AAAA,MACL,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,MAAMC,KAAI,OAAO;AAAA,MAClD,MAAM;AAAA,MACN;AAAA,MACA,aAAaA,MAAK;AAAA,MAClB,YAAY,SAASA,MAAK,UAAU,EAAE;AAAA,IACxC,EAAE;AAAA,IACF,YACE,cAAc,OACV,EAAE,MAAM,OAAO,IACf,OAAO,eAAe,WACtB,EAAE,MAAM,WAAW,IACnB,EAAE,MAAM,QAAiB,UAAU,WAAW,SAAmB;AAAA,EACzE;AACF;;;AC1CA;AAAA,EACE;AAAA,EAEA;AAAA,OACK;AACP,SAAS,iBAAAC,sBAAqB;AAyCvB,SAAS,cAAsD;AAAA,EACpE;AAAA,EACA;AACF,GAGsB;AACpB,QAAM,WAAW,SAAS;AAE1B,MAAI,SAAS,MAAM;AACjB,UAAM,IAAI,gBAAgB,EAAE,UAAU,SAAS,SAAS,CAAC;AAAA,EAC3D;AAEA,QAAMC,QAAO,MAAM,QAAQ;AAE3B,MAAIA,SAAQ,MAAM;AAChB,UAAM,IAAI,gBAAgB;AAAA,MACxB,UAAU,SAAS;AAAA,MACnB,gBAAgB,OAAO,KAAK,KAAK;AAAA,IACnC,CAAC;AAAA,EACH;AAEA,QAAM,cAAcC,eAAc;AAAA,IAChC,MAAM,SAAS;AAAA,IACf,QAAQ,SAASD,MAAK,UAAU;AAAA,EAGlC,CAAC;AAED,MAAI,YAAY,YAAY,OAAO;AACjC,UAAM,IAAI,0BAA0B;AAAA,MAClC;AAAA,MACA,UAAU,SAAS;AAAA,MACnB,OAAO,YAAY;AAAA,IACrB,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY,SAAS;AAAA,IACrB;AAAA,IACA,MAAM,YAAY;AAAA,EACpB;AACF;;;ACjBA,eAAsB,aAAqD;AAAA,EACzE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,GAAG;AACL,GAwCyC;AA9HzC;AA+HE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,GAAG,UAAU,WAAW;AAAA,EACtC,CAAC;AAED,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AACrE,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB;AAAA,UACvB,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,QACD,GAAG;AAAA;AAAA,QAEH,aAAa;AAAA,UACX,OAAO,MAAM,KAAK,UAAU,EAAE,QAAQ,QAAQ,SAAS,CAAC;AAAA,QAC1D;AAAA,QACA,iCAAiC;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,IAAI,OAAM,SAAQ;AAzJtB,UAAAE,KAAA;AA0JM,YAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AACxD,YAAM,kBAAkB,mBAAmB;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,YAAM,OAAO;AAAA,QACX,MAAM;AAAA,QACN,GAAG,0BAA0B,EAAE,OAAO,WAAW,CAAC;AAAA,MACpD;AACA,YAAM,eAAe,oBAAoB,QAAQ;AACjD,YAAM,iBAAiB,MAAM,6BAA6B;AAAA,QACxD,QAAQ;AAAA,QACR,wBAAwB,MAAM;AAAA,MAChC,CAAC;AAED,UAAI;AAGJ,UAAI,mBAA2C,CAAC;AAChD,UAAI,qBAA+C,CAAC;AACpD,UAAI,iBAAiB;AACrB,YAAM,mBACJ,CAAC;AACH,YAAM,aAAsD,CAAC;AAC7D,YAAM,QAA8B;AAAA,QAClC,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAEA,SAAG;AAED,cAAM,qBACJ,mBAAmB,IAAI,gBAAgB,OAAO;AAEhD,+BAAuB,MAAM;AAAA,UAAM,MACjC,WAAW;AAAA,YACT,MAAM;AAAA,YACN,YAAY,0BAA0B;AAAA,cACpC;AAAA,cACA,YAAY;AAAA,gBACV,GAAG,sBAAsB;AAAA,kBACvB,eAAe;AAAA,kBACf;AAAA,gBACF,CAAC;AAAA,gBACD,GAAG;AAAA,gBACH,oBAAoB,EAAE,OAAO,MAAM,mBAAmB;AAAA,gBACtD,sBAAsB;AAAA,kBACpB,OAAO,MAAM,KAAK,UAAU,cAAc;AAAA,gBAC5C;AAAA;AAAA,gBAGA,wBAAwB,MAAM;AAAA,gBAC9B,iBAAiB,MAAM;AAAA,gBACvB,6BAA6B,SAAS;AAAA,gBACtC,8BAA8B,SAAS;AAAA,gBACvC,wBAAwB,SAAS;AAAA,cACnC;AAAA,YACF,CAAC;AAAA,YACD;AAAA,YACA,IAAI,OAAMC,UAAQ;AAChB,oBAAM,SAAS,MAAM,MAAM,WAAW;AAAA,gBACpC;AAAA,gBACA,GAAG;AAAA,gBACH,aAAa;AAAA,gBACb,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cACF,CAAC;AAGD,cAAAA,MAAK;AAAA,gBACH,0BAA0B;AAAA,kBACxB;AAAA,kBACA,YAAY;AAAA,oBACV,mBAAmB,OAAO;AAAA,oBAC1B,yBAAyB,OAAO,MAAM;AAAA,oBACtC,6BAA6B,OAAO,MAAM;AAAA,oBAC1C,kBAAkB;AAAA,sBAChB,QAAQ,MAAM,OAAO;AAAA,oBACvB;AAAA,oBACA,uBAAuB;AAAA,sBACrB,QAAQ,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,oBAC/C;AAAA;AAAA,oBAGA,kCAAkC,CAAC,OAAO,YAAY;AAAA,oBACtD,8BAA8B,OAAO,MAAM;AAAA,oBAC3C,kCACE,OAAO,MAAM;AAAA,kBACjB;AAAA,gBACF,CAAC;AAAA,cACH;AAEA,qBAAO;AAAA,YACT;AAAA,UACF,CAAC;AAAA,QACH;AAGA,6BAAoBD,MAAA,qBAAqB,cAArB,OAAAA,MAAkC,CAAC,GAAG;AAAA,UACxD,mBAAiB,cAAc,EAAE,UAAU,eAAe,MAAM,CAAC;AAAA,QACnE;AAGA,6BACE,SAAS,OACL,CAAC,IACD,MAAM,aAAa;AAAA,UACjB,WAAW;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAGP,cAAM,eAAe;AAAA,UACnB,qBAAqB;AAAA,QACvB;AACA,cAAM,oBAAoB,aAAa;AACvC,cAAM,gBAAgB,aAAa;AACnC,cAAM,eAAe,aAAa;AAGlC,mBAAW,KAAK;AAAA,UACd,OAAM,0BAAqB,SAArB,YAA6B;AAAA,UACnC,WAAW;AAAA,UACX,aAAa;AAAA,UACb,cAAc,qBAAqB;AAAA,UACnC,OAAO;AAAA,UACP,UAAU,qBAAqB;AAAA,UAC/B,UAAU,qBAAqB;AAAA,QACjC,CAAC;AAGD,cAAM,sBAAsB,mBAAmB;AAAA,UAC7C,OAAM,0BAAqB,SAArB,YAA6B;AAAA,UACnC,WAAW;AAAA,UACX,aAAa;AAAA,QACf,CAAC;AACD,yBAAiB,KAAK,GAAG,mBAAmB;AAC5C,uBAAe;AAAA,UACb,GAAG,oBAAoB;AAAA,YAAI,aACzB,8BAA8B,SAAS,IAAI;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAAA;AAAA,QAEE,iBAAiB,SAAS;AAAA,QAE1B,mBAAmB,WAAW,iBAAiB;AAAA,QAE/C,mBAAmB;AAAA;AAIrB,WAAK;AAAA,QACH,0BAA0B;AAAA,UACxB;AAAA,UACA,YAAY;AAAA,YACV,mBAAmB,qBAAqB;AAAA,YACxC,yBAAyB,qBAAqB,MAAM;AAAA,YACpD,6BACE,qBAAqB,MAAM;AAAA,YAC7B,kBAAkB;AAAA,cAChB,QAAQ,MAAM,qBAAqB;AAAA,YACrC;AAAA,YACA,uBAAuB;AAAA,cACrB,QAAQ,MAAM,KAAK,UAAU,qBAAqB,SAAS;AAAA,YAC7D;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,0BAA0B;AAAA;AAAA;AAAA;AAAA,QAInC,OAAM,0BAAqB,SAArB,YAA6B;AAAA,QACnC,WAAW;AAAA,QACX,aAAa;AAAA,QACb,cAAc,qBAAqB;AAAA,QACnC;AAAA,QACA,UAAU,qBAAqB;AAAA,QAC/B,aAAa,qBAAqB;AAAA,QAClC,UAAU,qBAAqB;AAAA,QAC/B;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,eAAe,aAAqD;AAAA,EAClE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKsC;AACpC,QAAM,cAAc,MAAM,QAAQ;AAAA,IAChC,UAAU,IAAI,OAAM,aAAY;AAC9B,YAAME,QAAO,MAAM,SAAS,QAAQ;AAEpC,WAAIA,SAAA,gBAAAA,MAAM,YAAW,MAAM;AACzB,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,MAAM,WAAW;AAAA,QAC9B,MAAM;AAAA,QACN,YAAY,0BAA0B;AAAA,UACpC;AAAA,UACA,YAAY;AAAA,YACV,GAAG,sBAAsB;AAAA,cACvB,eAAe;AAAA,cACf;AAAA,YACF,CAAC;AAAA,YACD,oBAAoB,SAAS;AAAA,YAC7B,kBAAkB,SAAS;AAAA,YAC3B,oBAAoB;AAAA,cAClB,QAAQ,MAAM,KAAK,UAAU,SAAS,IAAI;AAAA,YAC5C;AAAA,UACF;AAAA,QACF,CAAC;AAAA,QACD;AAAA,QACA,IAAI,OAAM,SAAQ;AAChB,gBAAMC,UAAS,MAAMD,MAAK,QAAS,SAAS,IAAI;AAEhD,cAAI;AACF,iBAAK;AAAA,cACH,0BAA0B;AAAA,gBACxB;AAAA,gBACA,YAAY;AAAA,kBACV,sBAAsB;AAAA,oBACpB,QAAQ,MAAM,KAAK,UAAUC,OAAM;AAAA,kBACrC;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,SAAS,SAAS;AAAA,UAKlB;AAEA,iBAAOA;AAAA,QACT;AAAA,MACF,CAAC;AAED,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,UAAU,SAAS;AAAA,QACnB,MAAM,SAAS;AAAA,QACf;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,YAAY;AAAA,IACjB,CAAC,WAAiD,UAAU;AAAA,EAC9D;AACF;AAEA,IAAM,4BAAN,MAEA;AAAA,EAYE,YAAY,SAWT;AACD,SAAK,OAAO,QAAQ;AACpB,SAAK,YAAY,QAAQ;AACzB,SAAK,cAAc,QAAQ;AAC3B,SAAK,eAAe,QAAQ;AAC5B,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,QAAQ;AACxB,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,QAAQ;AACxB,SAAK,mBAAmB,QAAQ;AAChC,SAAK,aAAa,QAAQ;AAAA,EAC5B;AACF;AAKA,SAAS,mBAA2D;AAAA,EAClE;AAAA,EACA;AAAA,EACA;AACF,GAIkD;AAChD,QAAM,mBAAkE,CAAC;AAEzE,mBAAiB,KAAK;AAAA,IACpB,MAAM;AAAA,IACN,SAAS,CAAC,EAAE,MAAM,QAAQ,KAAK,GAAG,GAAG,SAAS;AAAA,EAChD,CAAC;AAED,MAAI,YAAY,SAAS,GAAG;AAC1B,qBAAiB,KAAK;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,YAAY,IAAI,aAAW;AAAA,QAClC,MAAM;AAAA,QACN,YAAY,OAAO;AAAA,QACnB,UAAU,OAAO;AAAA,QACjB,QAAQ,OAAO;AAAA,MACjB,EAAE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAKO,IAAM,4BAA4B;;;ACvelC,SAAS,aACd,SACA,SACiC;AACjC,QAAM,UAAU,QAAQ,UAAU;AAClC,QAAM,UAAU,QAAQ,UAAU;AAElC,MAAI,YACF;AACF,MAAI,YACF;AAEF,MAAI,cAAc;AAClB,MAAI,cAAc;AAGlB,iBAAe,YACb,YACA;AACA,QAAI;AACF,UAAI,aAAa,MAAM;AACrB,oBAAY,QAAQ,KAAK;AAAA,MAC3B;AAEA,YAAM,SAAS,MAAM;AACrB,kBAAY;AAEZ,UAAI,CAAC,OAAO,MAAM;AAChB,mBAAW,QAAQ,OAAO,KAAK;AAAA,MACjC,OAAO;AACL,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,SAAS,OAAO;AACd,iBAAW,MAAM,KAAK;AAAA,IACxB;AAAA,EACF;AAGA,iBAAe,YACb,YACA;AACA,QAAI;AACF,UAAI,aAAa,MAAM;AACrB,oBAAY,QAAQ,KAAK;AAAA,MAC3B;AAEA,YAAM,SAAS,MAAM;AACrB,kBAAY;AAEZ,UAAI,CAAC,OAAO,MAAM;AAChB,mBAAW,QAAQ,OAAO,KAAK;AAAA,MACjC,OAAO;AACL,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,SAAS,OAAO;AACd,iBAAW,MAAM,KAAK;AAAA,IACxB;AAAA,EACF;AAEA,SAAO,IAAI,eAAgC;AAAA,IACzC,MAAM,KAAK,YAAY;AACrB,UAAI;AAEF,YAAI,aAAa;AACf,gBAAM,YAAY,UAAU;AAC5B;AAAA,QACF;AAGA,YAAI,aAAa;AACf,gBAAM,YAAY,UAAU;AAC5B;AAAA,QACF;AAGA,YAAI,aAAa,MAAM;AACrB,sBAAY,QAAQ,KAAK;AAAA,QAC3B;AACA,YAAI,aAAa,MAAM;AACrB,sBAAY,QAAQ,KAAK;AAAA,QAC3B;AAKA,cAAM,EAAE,QAAQ,OAAO,IAAI,MAAM,QAAQ,KAAK;AAAA,UAC5C,UAAU,KAAK,CAAAC,aAAW,EAAE,QAAAA,SAAQ,QAAQ,QAAQ,EAAE;AAAA,UACtD,UAAU,KAAK,CAAAA,aAAW,EAAE,QAAAA,SAAQ,QAAQ,QAAQ,EAAE;AAAA,QACxD,CAAC;AAED,YAAI,CAAC,OAAO,MAAM;AAChB,qBAAW,QAAQ,OAAO,KAAK;AAAA,QACjC;AAEA,YAAI,WAAW,SAAS;AACtB,sBAAY;AACZ,cAAI,OAAO,MAAM;AAEf,kBAAM,YAAY,UAAU;AAC5B,0BAAc;AAAA,UAChB;AAAA,QACF,OAAO;AACL,sBAAY;AAEZ,cAAI,OAAO,MAAM;AACf,0BAAc;AACd,kBAAM,YAAY,UAAU;AAAA,UAC9B;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,mBAAW,MAAM,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,IACA,SAAS;AACP,cAAQ,OAAO;AACf,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF,CAAC;AACH;;;ACnIA,SAAoC,mBAAAC,wBAAuB;AAC3D,SAAS,kBAAkB;AAWpB,SAAS,uBAA+D;AAAA,EAC7E;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAM0C;AACxC,MAAI,WAAW;AACf,QAAM,uBAAuB,oBAAI,IAAY;AAG7C,MAAI,8BAEO;AACX,QAAM,oBAAoB,IAAI,eAAsC;AAAA,IAClE,MAAM,YAAY;AAChB,oCAA8B;AAAA,IAChC;AAAA,EACF,CAAC;AAGD,QAAM,kBAA2C,CAAC;AAGlD,QAAM,gBAAgB,IAAI,gBAGxB;AAAA,IACA,UACE,OACA,YACA;AACA,YAAM,YAAY,MAAM;AAExB,cAAQ,WAAW;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK,SAAS;AACZ,qBAAW,QAAQ,KAAK;AACxB;AAAA,QACF;AAAA,QAGA,KAAK,mBAAmB;AACtB,cAAI,mBAAmB;AACrB,gBAAI,CAAC,gBAAgB,MAAM,UAAU,GAAG;AACtC,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,YAAY,MAAM;AAAA,gBAClB,UAAU,MAAM;AAAA,cAClB,CAAC;AAED,8BAAgB,MAAM,UAAU,IAAI;AAAA,YACtC;AAEA,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN,YAAY,MAAM;AAAA,cAClB,UAAU,MAAM;AAAA,cAChB,eAAe,MAAM;AAAA,YACvB,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA,QAGA,KAAK,aAAa;AAChB,gBAAM,WAAW,MAAM;AAEvB,cAAI,SAAS,MAAM;AACjB,wCAA6B,QAAQ;AAAA,cACnC,MAAM;AAAA,cACN,OAAO,IAAIC,iBAAgB,EAAE,UAAU,MAAM,SAAS,CAAC;AAAA,YACzD,CAAC;AACD;AAAA,UACF;AAEA,gBAAMC,QAAO,MAAM,QAAQ;AAE3B,cAAIA,SAAQ,MAAM;AAChB,wCAA6B,QAAQ;AAAA,cACnC,MAAM;AAAA,cACN,OAAO,IAAID,iBAAgB;AAAA,gBACzB,UAAU,MAAM;AAAA,gBAChB,gBAAgB,OAAO,KAAK,KAAK;AAAA,cACnC,CAAC;AAAA,YACH,CAAC;AAED;AAAA,UACF;AAEA,cAAI;AACF,kBAAM,WAAW,cAAc;AAAA,cAC7B,UAAU;AAAA,cACV;AAAA,YACF,CAAC;AAED,uBAAW,QAAQ,QAAQ;AAE3B,gBAAIC,MAAK,WAAW,MAAM;AACxB,oBAAM,kBAAkB,WAAW;AACnC,mCAAqB,IAAI,eAAe;AAKxC,yBAAW;AAAA,gBACT,MAAM;AAAA,gBACN,YAAY,0BAA0B;AAAA,kBACpC;AAAA,kBACA,YAAY;AAAA,oBACV,GAAG,sBAAsB;AAAA,sBACvB,eAAe;AAAA,sBACf;AAAA,oBACF,CAAC;AAAA,oBACD,oBAAoB,SAAS;AAAA,oBAC7B,kBAAkB,SAAS;AAAA,oBAC3B,oBAAoB;AAAA,sBAClB,QAAQ,MAAM,KAAK,UAAU,SAAS,IAAI;AAAA,oBAC5C;AAAA,kBACF;AAAA,gBACF,CAAC;AAAA,gBACD;AAAA,gBACA,IAAI,OAAM,SACRA,MAAK,QAAS,SAAS,IAAI,EAAE;AAAA,kBAC3B,CAAC,WAAgB;AACf,gDAA6B,QAAQ;AAAA,sBACnC,GAAG;AAAA,sBACH,MAAM;AAAA,sBACN;AAAA,oBACF,CAAQ;AAER,yCAAqB,OAAO,eAAe;AAG3C,wBAAI,YAAY,qBAAqB,SAAS,GAAG;AAC/C,kDAA6B,MAAM;AAAA,oBACrC;AAGA,wBAAI;AACF,2BAAK;AAAA,wBACH,0BAA0B;AAAA,0BACxB;AAAA,0BACA,YAAY;AAAA,4BACV,sBAAsB;AAAA,8BACpB,QAAQ,MAAM,KAAK,UAAU,MAAM;AAAA,4BACrC;AAAA,0BACF;AAAA,wBACF,CAAC;AAAA,sBACH;AAAA,oBACF,SAAS,SAAS;AAAA,oBAKlB;AAAA,kBACF;AAAA,kBACA,CAAC,UAAe;AACd,gDAA6B,QAAQ;AAAA,sBACnC,MAAM;AAAA,sBACN;AAAA,oBACF,CAAC;AAED,yCAAqB,OAAO,eAAe;AAG3C,wBAAI,YAAY,qBAAqB,SAAS,GAAG;AAC/C,kDAA6B,MAAM;AAAA,oBACrC;AAAA,kBACF;AAAA,gBACF;AAAA,cACJ,CAAC;AAAA,YACH;AAAA,UACF,SAAS,OAAO;AACd,wCAA6B,QAAQ;AAAA,cACnC,MAAM;AAAA,cACN;AAAA,YACF,CAAC;AAAA,UACH;AAEA;AAAA,QACF;AAAA,QAGA,KAAK,UAAU;AACb,qBAAW,QAAQ;AAAA,YACjB,MAAM;AAAA,YACN,cAAc,MAAM;AAAA,YACpB,UAAU,MAAM;AAAA,YAChB,OAAO,8BAA8B,MAAM,KAAK;AAAA,UAClD,CAAC;AACD;AAAA,QACF;AAAA,QAEA,SAAS;AACP,gBAAM,mBAA0B;AAChC,gBAAM,IAAI,MAAM,yBAAyB,gBAAgB,EAAE;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA,IAEA,QAAQ;AACN,iBAAW;AAEX,UAAI,qBAAqB,SAAS,GAAG;AACnC,oCAA6B,MAAM;AAAA,MACrC;AAAA,IACF;AAAA,EACF,CAAC;AAGD,SAAO,IAAI,eAAsC;AAAA,IAC/C,MAAM,MAAM,YAAY;AAGtB,aAAO,QAAQ,IAAI;AAAA,QACjB,gBAAgB,YAAY,aAAa,EAAE;AAAA,UACzC,IAAI,eAAe;AAAA,YACjB,MAAM,OAAO;AACX,yBAAW,QAAQ,KAAK;AAAA,YAC1B;AAAA,YACA,QAAQ;AAAA,YAER;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,kBAAkB;AAAA,UAChB,IAAI,eAAe;AAAA,YACjB,MAAM,OAAO;AACX,yBAAW,QAAQ,KAAK;AAAA,YAC1B;AAAA,YACA,QAAQ;AACN,yBAAW,MAAM;AAAA,YACnB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;AC/KA,eAAsB,WAAmD;AAAA,EACvE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB,gCAAgC,oBAAoB;AAAA,EACpD;AAAA,EACA,GAAG;AACL,GAwE8C;AAzK9C;AA0KE,QAAM,0BAA0B,2BAA2B;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,EAAE,GAAG,UAAU,WAAW;AAAA,EACtC,CAAC;AAED,QAAM,SAAS,UAAU,EAAE,YAAW,4CAAW,cAAX,YAAwB,MAAM,CAAC;AAErE,SAAO,WAAW;AAAA,IAChB,MAAM;AAAA,IACN,YAAY,0BAA0B;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,QACV,GAAG,sBAAsB,EAAE,eAAe,iBAAiB,UAAU,CAAC;AAAA,QACtE,GAAG;AAAA;AAAA,QAEH,aAAa;AAAA,UACX,OAAO,MAAM,KAAK,UAAU,EAAE,QAAQ,QAAQ,SAAS,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA,aAAa;AAAA,IACb,IAAI,OAAM,aAAY;AACpB,YAAM,QAAQ,4BAA4B,EAAE,WAAW,CAAC;AACxD,YAAM,kBAAkB,mBAAmB,EAAE,QAAQ,QAAQ,SAAS,CAAC;AACvE,YAAM,iBAAiB,MAAM,6BAA6B;AAAA,QACxD,QAAQ;AAAA,QACR,wBAAwB,MAAM;AAAA,MAChC,CAAC;AAED,YAAM;AAAA,QACJ,QAAQ,EAAE,QAAQ,UAAU,YAAY;AAAA,QACxC;AAAA,MACF,IAAI,MAAM;AAAA,QAAM,MACd,WAAW;AAAA,UACT,MAAM;AAAA,UACN,YAAY,0BAA0B;AAAA,YACpC;AAAA,YACA,YAAY;AAAA,cACV,GAAG,sBAAsB;AAAA,gBACvB,eAAe;AAAA,gBACf;AAAA,cACF,CAAC;AAAA,cACD,GAAG;AAAA,cACH,oBAAoB;AAAA,gBAClB,OAAO,MAAM,gBAAgB;AAAA,cAC/B;AAAA,cACA,sBAAsB;AAAA,gBACpB,OAAO,MAAM,KAAK,UAAU,cAAc;AAAA,cAC5C;AAAA;AAAA,cAGA,wBAAwB,MAAM;AAAA,cAC9B,iBAAiB,MAAM;AAAA,cACvB,6BAA6B,SAAS;AAAA,cACtC,8BAA8B,SAAS;AAAA,cACvC,wBAAwB,SAAS;AAAA,YACnC;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UACA,aAAa;AAAA,UACb,IAAI,OAAMC,kBAAgB;AACxB,mBAAO;AAAA,cACL,QAAQ,MAAM,MAAM,SAAS;AAAA,gBAC3B,MAAM;AAAA,kBACJ,MAAM;AAAA,kBACN,GAAG,0BAA0B,EAAE,OAAO,WAAW,CAAC;AAAA,gBACpD;AAAA,gBACA,GAAG,oBAAoB,QAAQ;AAAA,gBAC/B,aAAa,gBAAgB;AAAA,gBAC7B,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,cAAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,IAAI,wBAAwB;AAAA,QACjC,QAAQ,uBAAuB;AAAA,UAC7B;AAAA,UACA,iBAAiB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAEA,IAAM,0BAAN,MAEA;AAAA,EAYE,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAQG;AACD,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,WAAW;AAGhB,QAAI;AAGJ,SAAK,QAAQ,IAAI,QAA8B,aAAW;AACxD,qBAAe;AAAA,IACjB,CAAC;AAGD,QAAI;AAGJ,SAAK,eAAe,IAAI,QAAsB,aAAW;AACvD,4BAAsB;AAAA,IACxB,CAAC;AAGD,QAAI;AACJ,SAAK,OAAO,IAAI,QAAgB,aAAW;AACzC,oBAAc;AAAA,IAChB,CAAC;AAGD,QAAI;AAGJ,SAAK,YAAY,IAAI,QAA6B,aAAW;AAC3D,yBAAmB;AAAA,IACrB,CAAC;AAGD,QAAI;AAGJ,SAAK,cAAc,IAAI,QAA+B,aAAW;AAC/D,2BAAqB;AAAA,IACvB,CAAC;AAGD,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO;AACX,UAAM,YAAiC,CAAC;AACxC,UAAM,cAAqC,CAAC;AAC5C,QAAI,aAAa;AAGjB,UAAM,OAAO;AACb,SAAK,iBAAiB,OAAO;AAAA,MAC3B,IAAI,gBAA8D;AAAA,QAChE,MAAM,UAAU,OAAO,YAA2B;AAChD,qBAAW,QAAQ,KAAK;AAGxB,cAAI,YAAY;AACd,yBAAa;AACb,yBAAa,SAAS,sBAAsB;AAAA,UAC9C;AAEA,gBAAM,YAAY,MAAM;AACxB,kBAAQ,WAAW;AAAA,YACjB,KAAK;AAEH,sBAAQ,MAAM;AACd;AAAA,YAEF,KAAK;AAEH,wBAAU,KAAK,KAAK;AACpB;AAAA,YAEF,KAAK;AAEH,0BAAY,KAAK,KAAK;AACtB;AAAA,YAEF,KAAK;AAGH,sBAAQ,MAAM;AACd,6BAAe,MAAM;AAGrB,2BAAa,KAAK;AAClB,kCAAoB,YAAY;AAChC,0BAAY,IAAI;AAChB,+BAAiB,SAAS;AAC1B;AAAA,YAEF,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAEH;AAAA,YAEF,SAAS;AACP,oBAAM,kBAAyB;AAC/B,oBAAM,IAAI,MAAM,uBAAuB,eAAe,EAAE;AAAA,YAC1D;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAGA,MAAM,MAAM,YAAY;AAzZhC;AA0ZU,cAAI;AACF,kBAAM,aAAa,wBAAS;AAAA,cAC1B,cAAc;AAAA,cACd,kBAAkB;AAAA,cAClB,aAAa;AAAA,YACf;AACA,kBAAM,oBAAoB,sCAAgB;AAC1C,kBAAM,qBACJ,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,IAAI;AAErD,yBAAa;AAAA,cACX,0BAA0B;AAAA,gBACxB;AAAA,gBACA,YAAY;AAAA,kBACV,mBAAmB;AAAA,kBACnB,yBAAyB,WAAW;AAAA,kBACpC,6BAA6B,WAAW;AAAA,kBACxC,kBAAkB,EAAE,QAAQ,MAAM,KAAK;AAAA,kBACvC,uBAAuB,EAAE,QAAQ,MAAM,mBAAmB;AAAA;AAAA,kBAG1D,kCAAkC,CAAC,iBAAiB;AAAA,kBACpD,8BAA8B,WAAW;AAAA,kBACzC,kCAAkC,WAAW;AAAA,gBAC/C;AAAA,cACF,CAAC;AAAA,YACH;AAGA,yBAAa,IAAI;AAGjB,qBAAS;AAAA,cACP,0BAA0B;AAAA,gBACxB;AAAA,gBACA,YAAY;AAAA,kBACV,mBAAmB;AAAA,kBACnB,yBAAyB,WAAW;AAAA,kBACpC,6BAA6B,WAAW;AAAA,kBACxC,kBAAkB,EAAE,QAAQ,MAAM,KAAK;AAAA,kBACvC,uBAAuB,EAAE,QAAQ,MAAM,mBAAmB;AAAA,gBAC5D;AAAA,cACF,CAAC;AAAA,YACH;AAGA,+BAAmB,WAAW;AAG9B,oBAAM,UAAK,aAAL,8BAAgB;AAAA,cACpB,cAAc;AAAA,cACd,OAAO;AAAA,cACP;AAAA,cACA;AAAA;AAAA;AAAA;AAAA;AAAA,cAKA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF,SAAS,OAAO;AACd,uBAAW,MAAM,KAAK;AAAA,UACxB,UAAE;AACA,qBAAS,IAAI;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,YAAY;AAClB,UAAM,CAAC,SAAS,OAAO,IAAI,KAAK,eAAe,IAAI;AACnD,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,aAA0C;AAC5C,WAAO,0BAA0B,KAAK,UAAU,GAAG;AAAA,MACjD,UAAU,OAAO,YAAY;AAC3B,YAAI,MAAM,SAAS,cAAc;AAE/B,cAAI,MAAM,UAAU,SAAS,GAAG;AAC9B,uBAAW,QAAQ,MAAM,SAAS;AAAA,UACpC;AAAA,QACF,WAAW,MAAM,SAAS,SAAS;AACjC,qBAAW,MAAM,MAAM,KAAK;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,IAAI,aAAyD;AAC3D,WAAO,0BAA0B,KAAK,UAAU,GAAG;AAAA,MACjD,UAAU,OAAO,YAAY;AAC3B,YAAI,MAAM,SAAS,cAAc;AAE/B,cAAI,MAAM,UAAU,SAAS,GAAG;AAC9B,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,qBAAW,QAAQ,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,WAAW,YAAyC,CAAC,GAAG;AACtD,QAAI,qBAAqB;AAEzB,UAAM,sBAAsB,IAAI,gBAG9B;AAAA,MACA,MAAM,QAAuB;AAC3B,YAAI,UAAU;AAAS,gBAAM,UAAU,QAAQ;AAAA,MACjD;AAAA,MAEA,MAAM,UAAU,OAAO,YAA2B;AAChD,mBAAW,QAAQ,KAAK;AAExB,YAAI,MAAM,SAAS,cAAc;AAC/B,gBAAM,YAAY,MAAM;AAExB,gCAAsB;AAEtB,cAAI,UAAU;AAAS,kBAAM,UAAU,QAAQ,SAAS;AACxD,cAAI,UAAU;AAAQ,kBAAM,UAAU,OAAO,SAAS;AAAA,QACxD;AAAA,MACF;AAAA,MAEA,MAAM,QAAuB;AAC3B,YAAI,UAAU;AACZ,gBAAM,UAAU,aAAa,kBAAkB;AACjD,YAAI,UAAU;AAAS,gBAAM,UAAU,QAAQ,kBAAkB;AAAA,MACnE;AAAA,IACF,CAAC;AAED,UAAM,yBAAyB,IAAI,gBAGjC;AAAA,MACA,WAAW,OAAO,OAAO,eAAe;AACtC,cAAM,YAAY,MAAM;AACxB,gBAAQ,WAAW;AAAA,UACjB,KAAK;AACH,uBAAW,QAAQ,iBAAiB,QAAQ,MAAM,SAAS,CAAC;AAC5D;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,6BAA6B;AAAA,gBAC5C,YAAY,MAAM;AAAA,gBAClB,UAAU,MAAM;AAAA,cAClB,CAAC;AAAA,YACH;AACA;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,mBAAmB;AAAA,gBAClC,YAAY,MAAM;AAAA,gBAClB,eAAe,MAAM;AAAA,cACvB,CAAC;AAAA,YACH;AACA;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,aAAa;AAAA,gBAC5B,YAAY,MAAM;AAAA,gBAClB,UAAU,MAAM;AAAA,gBAChB,MAAM,MAAM;AAAA,cACd,CAAC;AAAA,YACH;AACA;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,eAAe;AAAA,gBAC9B,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAChB,CAAC;AAAA,YACH;AACA;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,SAAS,KAAK,UAAU,MAAM,KAAK,CAAC;AAAA,YACvD;AACA;AAAA,UACF,KAAK;AACH,uBAAW;AAAA,cACT,iBAAiB,kBAAkB;AAAA,gBACjC,cAAc,MAAM;AAAA,gBACpB,OAAO;AAAA,kBACL,cAAc,MAAM,MAAM;AAAA,kBAC1B,kBAAkB,MAAM,MAAM;AAAA,gBAChC;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF,SAAS;AACP,kBAAM,kBAAyB;AAC/B,kBAAM,IAAI,MAAM,uBAAuB,eAAe,EAAE;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO,KAAK,WACT,YAAY,mBAAmB,EAC/B,YAAY,sBAAsB,EAClC,YAAY,IAAI,kBAAkB,CAAC;AAAA,EACxC;AAAA,EAEA,uBACE,UACA,MACM;AACN,WAAO,KAAK,yBAAyB,UAAU,IAAI;AAAA,EACrD;AAAA,EAEA,yBACE,UACA,MACA;AAhoBJ;AAioBI,aAAS,WAAU,kCAAM,WAAN,YAAgB,KAAK;AAAA,MACtC,gBAAgB;AAAA,MAChB,GAAG,6BAAM;AAAA,IACX,CAAC;AAED,UAAM,SAAS,KAAK,WAAW,EAAE,UAAU;AAE3C,UAAM,OAAO,YAAY;AACvB,UAAI;AACF,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI;AAAM;AACV,mBAAS,MAAM,KAAK;AAAA,QACtB;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,MACR,UAAE;AACA,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAEA,SAAK;AAAA,EACP;AAAA,EAEA,yBACE,UACA,MACA;AA5pBJ;AA6pBI,aAAS,WAAU,kCAAM,WAAN,YAAgB,KAAK;AAAA,MACtC,gBAAgB;AAAA,MAChB,GAAG,6BAAM;AAAA,IACX,CAAC;AAED,UAAM,SAAS,KAAK,WACjB,YAAY,IAAI,kBAAkB,CAAC,EACnC,UAAU;AAEb,UAAM,OAAO,YAAY;AACvB,UAAI;AACF,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI;AAAM;AACV,mBAAS,MAAM,KAAK;AAAA,QACtB;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,MACR,UAAE;AACA,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAEA,SAAK;AAAA,EACP;AAAA,EAEA,mBACE,SACU;AACV,WAAO,KAAK,qBAAqB,OAAO;AAAA,EAC1C;AAAA,EAEA,qBACE,SACU;AA/rBd;AAgsBI,UAAM,OACJ,WAAW,OACP,SACA,UAAU,UACV,QAAQ,OACR;AAAA,MACE,SAAS,aAAa,UAAU,QAAQ,UAAU;AAAA,MAClD,QAAQ,YAAY,UAAU,QAAQ,SAAS;AAAA,MAC/C,YACE,gBAAgB,UAAU,QAAQ,aAAa;AAAA,IACnD;AAEN,UAAM,OACJ,WAAW,OACP,SACA,UAAU,UACV,QAAQ,OACR;AAEN,UAAM,SAAS,OACX,aAAa,KAAK,QAAQ,KAAK,WAAW,CAAC,IAC3C,KAAK,WAAW;AAEpB,WAAO,IAAI,SAAS,QAAQ;AAAA,MAC1B,SAAQ,kCAAM,WAAN,YAAgB;AAAA,MACxB,YAAY,6BAAM;AAAA,MAClB,SAAS,uBAAuB,MAAM;AAAA,QACpC,aAAa;AAAA,QACb,mBAAmB;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,qBAAqB,MAA+B;AAjuBtD;AAkuBI,WAAO,IAAI,SAAS,KAAK,WAAW,YAAY,IAAI,kBAAkB,CAAC,GAAG;AAAA,MACxE,SAAQ,kCAAM,WAAN,YAAgB;AAAA,MACxB,SAAS,uBAAuB,MAAM;AAAA,QACpC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAKO,IAAM,0BAA0B;;;AChuBhC,SAAS,mBAAmB,aAA0C;AAd7E;AAeE,QAAM,QAAuB,CAAC;AAE9B,aAAW,cAAc,aAAa;AACpC,QAAI;AAEJ,QAAI;AACF,YAAM,IAAI,IAAI,WAAW,GAAG;AAAA,IAC9B,SAAS,OAAO;AACd,YAAM,IAAI,MAAM,gBAAgB,WAAW,GAAG,EAAE;AAAA,IAClD;AAEA,YAAQ,IAAI,UAAU;AAAA,MACpB,KAAK;AAAA,MACL,KAAK,UAAU;AACb,aAAI,gBAAW,gBAAX,mBAAwB,WAAW,WAAW;AAChD,gBAAM,KAAK,EAAE,MAAM,SAAS,OAAO,IAAI,CAAC;AAAA,QAC1C;AACA;AAAA,MACF;AAAA,MAEA,KAAK,SAAS;AACZ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,YAAI;AACF,WAAC,QAAQ,aAAa,IAAI,WAAW,IAAI,MAAM,GAAG;AAClD,qBAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,QAC9C,SAAS,OAAO;AACd,gBAAM,IAAI,MAAM,8BAA8B,WAAW,GAAG,EAAE;AAAA,QAChE;AAEA,YAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,gBAAM,IAAI,MAAM,4BAA4B,WAAW,GAAG,EAAE;AAAA,QAC9D;AAEA,aAAI,gBAAW,gBAAX,mBAAwB,WAAW,WAAW;AAChD,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,OAAO,+BAA+B,aAAa;AAAA,UACrD,CAAC;AAAA,QACH,YAAW,gBAAW,gBAAX,mBAAwB,WAAW,UAAU;AACtD,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,MAAM;AAAA,cACJ,+BAA+B,aAAa;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH;AAEA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,IAAI,MAAM,6BAA6B,IAAI,QAAQ,EAAE;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AClEO,SAAS,sBACd,UAMA;AACA,QAAM,eAA8B,CAAC;AAErC,aAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,KAAK,UAAU;AACb,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,qBAAa,KAAK;AAAA,UAChB,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,qBAAa,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,SAAS,2BACL;AAAA,YACE,EAAE,MAAM,QAAQ,MAAM,QAAQ;AAAA,YAC9B,GAAG,mBAAmB,wBAAwB;AAAA,UAChD,IACA;AAAA,QACN,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,YAAI,mBAAmB,MAAM;AAC3B,uBAAa,KAAK,EAAE,MAAM,aAAa,QAAQ,CAAC;AAChD;AAAA,QACF;AAGA,qBAAa,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,SAAS;AAAA,YACP,EAAE,MAAM,QAAQ,MAAM,QAAQ;AAAA,YAC9B,GAAG,gBAAgB,IAAI,CAAC,EAAE,YAAY,UAAU,KAAK,OAAO;AAAA,cAC1D,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,YACF,EAAE;AAAA,UACJ;AAAA,QACF,CAAC;AAGD,qBAAa,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,SAAS,gBAAgB;AAAA,YACvB,CAAC,EAAE,YAAY,UAAU,MAAM,OAAO,OAAO;AAAA,cAC3C,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAED;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,mBAAmB,gBAAgB,EAAE;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC5FO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAG7C,YAAY;AAAA,IACV;AAAA,IACA,UAAU,qBAAqB,EAAE;AAAA,EACnC,GAGG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,KAAK;AAAA,EACZ;AAAA,EAEA,OAAO,sBAAsB,OAA8C;AACzE,WACE,iBAAiB,SACjB,MAAM,SAAS,4BACf,OAAQ,MAA8B,OAAO;AAAA,EAEjD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,IAAI,KAAK;AAAA,IACX;AAAA,EACF;AACF;;;AClCO,IAAM,mBAAN,cAA+B,MAAM;AAAA,EAI1C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU,WAAW,SAAS,KAAK,OAAO;AAAA,EAC5C,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,OAAO,mBAAmB,OAA2C;AACnE,WACE,iBAAiB,SACjB,MAAM,SAAS,yBACf,OAAQ,MAA2B,YAAY,YAC/C,OAAQ,MAA2B,cAAc;AAAA,EAErD;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACF;;;ACxCO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAI7C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU,qBAAqB,UAAU,0BAA0B,mBAAmB,KAAK,CAAC;AAAA,EAC9F,GAIG;AACD,UAAM,OAAO;AAEb,SAAK,OAAO;AAEZ,SAAK,aAAa;AAClB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EAEA,OAAO,sBAAsB,OAA8C;AACzE,WACE,iBAAiB,SACjB,MAAM,SAAS,4BACf,OAAQ,MAA8B,eAAe,YACrD,MAAM,QAAS,MAA8B,kBAAkB;AAAA,EAEnE;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MAEZ,YAAY,KAAK;AAAA,MACjB,oBAAoB,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;;;ACoBO,SAAS,oCACd,WAC+B;AAC/B,QAAM,WAAW,IAAI,wBAAwB;AAE7C,aAAW,CAAC,IAAI,QAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AACtD,aAAS,iBAAiB,EAAE,IAAI,SAAS,CAAC;AAAA,EAC5C;AAEA,SAAO;AACT;AAKO,IAAM,mCACX;AAEF,IAAM,0BAAN,MAAuE;AAAA,EAAvE;AACE,SAAQ,YAAsC,CAAC;AAAA;AAAA,EAE/C,iBAAiB,EAAE,IAAI,SAAS,GAA6C;AAC3E,SAAK,UAAU,EAAE,IAAI;AAAA,EACvB;AAAA,EAEQ,YAAY,IAAsB;AACxC,UAAM,WAAW,KAAK,UAAU,EAAE;AAElC,QAAI,YAAY,MAAM;AACpB,YAAM,IAAI,oBAAoB;AAAA,QAC5B,YAAY;AAAA,QACZ,oBAAoB,OAAO,KAAK,KAAK,SAAS;AAAA,MAChD,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,QAAQ,IAA8B;AAC5C,UAAM,QAAQ,GAAG,QAAQ,GAAG;AAE5B,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI,oBAAoB,EAAE,GAAG,CAAC;AAAA,IACtC;AAEA,WAAO,CAAC,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,MAAM,QAAQ,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,cAAc,IAA2B;AA5G3C;AA6GI,UAAM,CAAC,YAAY,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC7C,UAAM,SAAQ,gBAAK,YAAY,UAAU,GAAE,kBAA7B,4BAA6C;AAE3D,QAAI,SAAS,MAAM;AACjB,YAAM,IAAI,iBAAiB,EAAE,SAAS,IAAI,WAAW,iBAAiB,CAAC;AAAA,IACzE;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,mBAAmB,IAAoC;AAvHzD;AAwHI,UAAM,CAAC,YAAY,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC7C,UAAM,SAAQ,gBAAK,YAAY,UAAU,GAAE,kBAA7B,4BAA6C;AAE3D,QAAI,SAAS,MAAM;AACjB,YAAM,IAAI,iBAAiB;AAAA,QACzB,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;AC5EO,SAAS,KACdC,OAC8B;AAC9B,SAAOA;AACT;;;AC5DA;AAAA,EACE,gBAAAC;AAAA,EACA;AAAA,EACA,wBAAAC;AAAA,EACA,2BAAAC;AAAA,EACA,sBAAAC;AAAA,EACA;AAAA,EACA,6BAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA,0BAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,cAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACPA,SAAS,iBAAiB,SAAmB,SAAmB;AACrE,MAAI,QAAQ,WAAW,QAAQ,QAAQ;AACrC,UAAM,IAAI;AAAA,MACR,+CAA+C,QAAQ,MAAM,uBAAuB,QAAQ,MAAM;AAAA,IACpG;AAAA,EACF;AAEA,SACE,WAAW,SAAS,OAAO,KAAK,UAAU,OAAO,IAAI,UAAU,OAAO;AAE1E;AAQA,SAAS,WAAW,SAAmB,SAAmB;AACxD,SAAO,QAAQ;AAAA,IACb,CAAC,aAAqB,OAAe,UACnC,cAAc,QAAQ,QAAQ,KAAK;AAAA,IACrC;AAAA,EACF;AACF;AAOA,SAAS,UAAU,QAAkB;AACnC,SAAO,KAAK,KAAK,WAAW,QAAQ,MAAM,CAAC;AAC7C;;;AC3CA;AAAA,EACE;AAAA,OAIK;AAoEA,SAAS,6BACd,cAC0E;AAC1E,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI;AAEJ,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,MAAM,YAA2B;AACrC,0BAAoB;AAAA,QAClB,CAAC,UAA2C;AAC1C,cACG,UAAU,SACT,MAAM,SAAS,WACf,MAAM,SAAS;AAAA;AAAA,UAGhB,MAAc,UAAU,QACzB;AACA,uBAAW,UAAU;AACrB;AAAA,UACF;AAEA,cAAI,UAAU,OAAO;AACnB,kBAAM,gBAAgB,eAClB,aAAa,MAAM,MAAM;AAAA,cACvB,OAAO,MAAM;AAAA,YACf,CAAC,IACD,MAAM;AACV,gBAAI;AAAe,yBAAW,QAAQ,aAAa;AAAA,UACrD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,UAAU,OAAO;AACf,wBAAkB,KAAK,YAAY,OAAO,KAAK,CAAC;AAAA,IAClD;AAAA,EACF,CAAC;AACH;AAwBO,SAAS,2BACd,IAC0E;AAC1E,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,qBAAqB;AACzB,QAAM,YAAY,MAAM,CAAC;AAEzB,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,QAAuB;AAC3B,UAAI,UAAU;AAAS,cAAM,UAAU,QAAQ;AAAA,IACjD;AAAA,IAEA,MAAM,UAAU,SAAS,YAA2B;AAClD,YAAM,UAAU,OAAO,YAAY,WAAW,UAAU,QAAQ;AAEhE,iBAAW,QAAQ,YAAY,OAAO,OAAO,CAAC;AAE9C,4BAAsB;AAEtB,UAAI,UAAU;AAAS,cAAM,UAAU,QAAQ,OAAO;AACtD,UAAI,UAAU,UAAU,OAAO,YAAY,UAAU;AACnD,cAAM,UAAU,OAAO,OAAO;AAAA,MAChC;AAAA,IACF;AAAA,IAEA,MAAM,QAAuB;AAC3B,YAAM,oBAAoB,8BAA8B,SAAS;AAGjE,UAAI,UAAU,cAAc;AAC1B,cAAM,UAAU,aAAa,kBAAkB;AAAA,MACjD;AAEA,UAAI,UAAU,WAAW,CAAC,mBAAmB;AAC3C,cAAM,UAAU,QAAQ,kBAAkB;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,8BACP,WACoC;AACpC,SAAO,iCAAiC;AAC1C;AAgBO,SAAS,0BAAoD;AAClE,MAAI,gBAAgB;AAEpB,SAAO,CAAC,SAAyB;AAC/B,QAAI,eAAe;AACjB,aAAO,KAAK,UAAU;AACtB,UAAI;AAAM,wBAAgB;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;AAoBO,SAAS,SACd,UACA,cACA,WAC4B;AAC5B,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,MAAM;AACjB,YAAM,SAAS,SAAS,KAAK,UAAU;AACvC,aAAO,IAAI,eAAe;AAAA,QACxB,MAAM,MAAM,YAAY;AACtB,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI,CAAC,MAAM;AACT,kBAAM,YAAY,IAAI,YAAY,EAAE,OAAO,KAAK;AAChD,uBAAW,MAAM,IAAI,MAAM,mBAAmB,SAAS,EAAE,CAAC;AAAA,UAC5D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,aAAO,IAAI,eAAe;AAAA,QACxB,MAAM,YAAY;AAChB,qBAAW,MAAM,IAAI,MAAM,kCAAkC,CAAC;AAAA,QAChE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,qBAAqB,SAAS,QAAQ,0BAA0B;AAEtE,SAAO,mBACJ,YAAY,6BAA6B,YAAY,CAAC,EACtD,YAAY,2BAA2B,SAAS,CAAC;AACtD;AAeA,SAAS,4BAA4C;AACnD,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,YAAY;AAChB,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAMO,SAAS,0BAA6B,UAA4B;AACvE,MAAI,KAAK,SAAS,OAAO,aAAa,EAAE;AACxC,SAAO,IAAI,eAAkB;AAAA,IAC3B,MAAM,KAAK,YAAY;AACrB,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,GAAG,KAAK;AACtC,UAAI;AAAM,mBAAW,MAAM;AAAA;AACtB,mBAAW,QAAQ,KAAK;AAAA,IAC/B;AAAA,IAEA,MAAM,OAAO,QAAQ;AApSzB;AAqSM,cAAM,QAAG,WAAH,4BAAY;AAAA,IACpB;AAAA,EACF,CAAC;AACH;;;ACxSA,SAAoB,oBAAAC,yBAAwB;AAKrC,IAAMC,cAAN,MAAiB;AAAA,EAStB,cAAc;AARd,SAAQ,UAAU,IAAI,YAAY;AAElC,SAAQ,aAA0D;AAGlE,SAAQ,WAAoB;AAC5B,SAAQ,iBAAwC;AAG9C,UAAM,OAAO;AAEb,SAAK,SAAS,IAAI,eAAe;AAAA,MAC/B,OAAO,OAAM,eAAc;AACzB,aAAK,aAAa;AAGlB,YAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,eAAK,iBAAiB,WAAW,MAAM;AACrC,oBAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF,GAAG,GAAI;AAAA,QACT;AAAA,MACF;AAAA,MACA,MAAM,gBAAc;AAAA,MAEpB;AAAA,MACA,QAAQ,YAAU;AAChB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,QAAuB;AAC3B,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AAEA,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AAEA,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW;AAGhB,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,OAAO,OAAwB;AAC7B,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AAEA,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AAEA,SAAK,WAAW;AAAA,MACd,KAAK,QAAQ,OAAOD,kBAAiB,QAAQ,CAAC,KAAK,CAAC,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EAEA,wBAAwB,OAAwB;AAC9C,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AAEA,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AAEA,SAAK,WAAW;AAAA,MACd,KAAK,QAAQ,OAAOA,kBAAiB,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAAA,IACtE;AAAA,EACF;AACF;AAMO,SAAS,8BAA8B;AAC5C,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,IAAI,gBAAgB;AAAA,IACzB,WAAW,OAAO,OAAO,eAAe;AACtC,YAAM,UAAU,QAAQ,OAAO,KAAK;AACpC,iBAAW,QAAQ,QAAQ,OAAOA,kBAAiB,QAAQ,OAAO,CAAC,CAAC;AAAA,IACtE;AAAA,EACF,CAAC;AACH;AAKO,IAAM,0BAAN,cAAsCC,YAAW;AAAC;;;ACiBzD,SAAS,uBAAwD;AAC/D,MAAI,WAAW;AAEf,SAAO,UAAQ;AACb,UAAM,OAAO,KAAK,MAAM,IAAc;AAGtC,QAAI,WAAW,MAAM;AACnB,YAAM,IAAI,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,OAAO,EAAE;AAAA,IAC7D;AAGA,QAAI,EAAE,gBAAgB,OAAO;AAC3B;AAAA,IACF;AAMA,UAAM,OAAO,KAAK;AAClB,QACE,CAAC,YACA,KAAK,SAAS,SAAS,UAAU,KAAK,WAAW,QAAQ,GAC1D;AACA,YAAM,QAAQ,KAAK,MAAM,SAAS,MAAM;AACxC,iBAAW;AAEX,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;AAEA,gBAAgB,WACd,QACA;AACA,mBAAiB,SAAS,QAAQ;AAChC,QAAI,gBAAgB,OAAO;AAEzB,YAAM,OAAO,MAAM;AACnB,UAAI;AAAM,cAAM;AAAA,IAClB,WAAW,WAAW,OAAO;AAE3B,YAAM,EAAE,MAAM,IAAI;AAClB,UAAI,UAAU,OAAO;AACnB,cAAM,OAAO,MAAM;AACnB,YAAI;AAAM,gBAAM;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AASO,SAAS,gBACd,KAIA,IACgB;AAChB,MAAI,OAAO,iBAAiB,KAAK;AAC/B,WAAO,0BAA0B,WAAW,GAAG,CAAC,EAC7C,YAAY,2BAA2B,EAAE,CAAC,EAC1C,YAAY,4BAA4B,CAAC;AAAA,EAC9C,OAAO;AACL,WAAO,SAAS,KAAK,qBAAqB,GAAG,EAAE,EAAE;AAAA,MAC/C,4BAA4B;AAAA,IAC9B;AAAA,EACF;AACF;;;ACtMA;AAAA,EAGE,oBAAAC;AAAA,OACK;AAsDA,SAAS,kBACd,EAAE,UAAU,UAAU,GACtBC,UACU;AACV,QAAM,SAAS,IAAI,eAAe;AAAA,IAChC,MAAM,MAAM,YAAY;AA/D5B;AAgEM,YAAM,cAAc,IAAI,YAAY;AAEpC,YAAM,cAAc,CAAC,YAA8B;AACjD,mBAAW;AAAA,UACT,YAAY,OAAOD,kBAAiB,qBAAqB,OAAO,CAAC;AAAA,QACnE;AAAA,MACF;AAEA,YAAM,kBAAkB,CAAC,YAAyB;AAChD,mBAAW;AAAA,UACT,YAAY,OAAOA,kBAAiB,gBAAgB,OAAO,CAAC;AAAA,QAC9D;AAAA,MACF;AAEA,YAAM,YAAY,CAAC,iBAAyB;AAC1C,mBAAW;AAAA,UACT,YAAY,OAAOA,kBAAiB,SAAS,YAAY,CAAC;AAAA,QAC5D;AAAA,MACF;AAEA,YAAM,gBAAgB,OAAOE,YAA4B;AApF/D,YAAAC,KAAA;AAqFQ,YAAI,SAA0B;AAE9B,yBAAiB,SAASD,SAAQ;AAChC,kBAAQ,MAAM,OAAO;AAAA,YACnB,KAAK,0BAA0B;AAC7B,yBAAW;AAAA,gBACT,YAAY;AAAA,kBACVF,kBAAiB,qBAAqB;AAAA,oBACpC,IAAI,MAAM,KAAK;AAAA,oBACf,MAAM;AAAA,oBACN,SAAS,CAAC,EAAE,MAAM,QAAQ,MAAM,EAAE,OAAO,GAAG,EAAE,CAAC;AAAA,kBACjD,CAAC;AAAA,gBACH;AAAA,cACF;AACA;AAAA,YACF;AAAA,YAEA,KAAK,wBAAwB;AAC3B,oBAAM,WAAUG,MAAA,MAAM,KAAK,MAAM,YAAjB,gBAAAA,IAA2B;AAE3C,mBAAI,mCAAS,UAAS,YAAU,aAAQ,SAAR,mBAAc,UAAS,MAAM;AAC3D,2BAAW;AAAA,kBACT,YAAY;AAAA,oBACVH,kBAAiB,QAAQ,QAAQ,KAAK,KAAK;AAAA,kBAC7C;AAAA,gBACF;AAAA,cACF;AAEA;AAAA,YACF;AAAA,YAEA,KAAK;AAAA,YACL,KAAK,8BAA8B;AACjC,uBAAS,MAAM;AACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,iBAAW;AAAA,QACT,YAAY;AAAA,UACVA,kBAAiB,0BAA0B;AAAA,YACzC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,UAAI;AACF,cAAMC,SAAQ;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,SAAS,OAAO;AACd,mBAAW,WAAc,YAAd,YAAyB,GAAG,KAAK,EAAE;AAAA,MAChD,UAAE;AACA,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,IACA,KAAK,YAAY;AAAA,IAAC;AAAA,IAClB,SAAS;AAAA,IAAC;AAAA,EACZ,CAAC;AAED,SAAO,IAAI,SAAS,QAAQ;AAAA,IAC1B,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAKO,IAAM,iCAAiC;;;ACzJ9C,gBAAgB,gBACd,UACA,2BACA;AAhBF;AAiBE,QAAM,UAAU,IAAI,YAAY;AAChC,mBAAiB,UAAS,cAAS,SAAT,YAAiB,CAAC,GAAG;AAC7C,UAAM,SAAQ,WAAM,UAAN,mBAAa;AAE3B,QAAI,SAAS,MAAM;AACjB,YAAM,YAAY,QAAQ,OAAO,KAAK;AACtC,YAAM,YAAY,KAAK,MAAM,SAAS;AACtC,YAAM,QAAQ,0BAA0B,SAAS;AAEjD,UAAI,SAAS,MAAM;AACjB,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,kCACd,UACA,WACgB;AAChB,SAAO,iBAAiB,UAAU,WAAW,WAAM;AArCrD;AAqCwD,uBAAM,UAAN,mBAAa;AAAA,GAAI;AACzE;AAEO,SAAS,0BACd,UACA,WACgB;AAChB,SAAO,iBAAiB,UAAU,WAAW,WAAS,MAAM,UAAU;AACxE;AAEO,SAAS,uBACd,UACA,WACgB;AAChB,SAAO,iBAAiB,UAAU,WAAW,WAAS,+BAAO,IAAI;AACnE;AAEO,SAAS,uBACd,UACA,WACgB;AAChB,SAAO,iBAAiB,UAAU,WAAW,WAAS,MAAM,UAAU;AACxE;AAEO,SAAS,iBACd,UACA,WACA,2BACA;AACA,SAAO;AAAA,IACL,gBAAgB,UAAU,yBAAyB;AAAA,EACrD,EACG,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAC9C;;;AChEA,IAAM,cAAc,IAAI,YAAY,OAAO;AAe3C,eAAe,aACb,OACA,YACA;AACA,aAAW,QAAQ,OAAO;AACxB,UAAM,EAAE,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;AAG7C,QAAI,CAAC,aAAa;AAChB,iBAAW,QAAQ,IAAI;AAAA,IACzB;AAAA,EACF;AACF;AAEA,eAAe,oBACb,QACA,YACA;AACA,MAAI,UAAU;AAEd,SAAO,MAAM;AACX,UAAM,EAAE,OAAO,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AACjD,QAAI,MAAM;AACR;AAAA,IACF;AAEA,eAAW,YAAY,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AAErD,UAAM,aAAa,QAAQ,MAAM,aAAa;AAC9C,cAAU,WAAW,IAAI,KAAK;AAE9B,UAAM,aAAa,YAAY,UAAU;AAAA,EAC3C;AAEA,MAAI,SAAS;AACX,UAAM,aAAa,CAAC,OAAO;AAC3B,UAAM,aAAa,YAAY,UAAU;AAAA,EAC3C;AAEA,aAAW,MAAM;AACnB;AAEA,SAASG,cAAa,KAAe;AAhErC;AAiEE,QAAM,UAAS,SAAI,SAAJ,mBAAU;AAEzB,SAAO,IAAI,eAAuB;AAAA,IAChC,MAAM,MAAM,YAA2B;AACrC,UAAI,CAAC,QAAQ;AACX,mBAAW,MAAM;AACjB;AAAA,MACF;AAEA,YAAM,oBAAoB,QAAQ,UAAU;AAAA,IAC9C;AAAA,EACF,CAAC;AACH;AAEA,gBAAgBC,YAAW,QAAoC;AAC7D,mBAAiB,SAAS,QAAQ;AAChC,QAAI,MAAM,cAAc,mBAAmB;AACzC,YAAM,OAAO,MAAM;AACnB,UAAI;AAAM,cAAM;AAAA,IAClB;AAAA,EACF;AACF;AAEO,SAAS,aACd,QACA,WACgB;AAChB,MAAI,OAAO,iBAAiB,QAAQ;AAClC,WAAO,0BAA0BA,YAAW,MAAM,CAAC,EAChD,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAAA,EAC9C,OAAO;AACL,WAAOD,cAAa,MAAM,EACvB,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAAA,EAC9C;AACF;;;ACrEA,gBAAgBE,YAAW,UAExB;AAlCH;AAmCE,mBAAiB,SAAS,SAAS,QAAQ;AACzC,UAAM,SAAQ,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,YAAvB,mBAAgC;AAE9C,QAAI,UAAU,QAAW;AACvB;AAAA,IACF;AAEA,UAAM,YAAY,MAAM,CAAC;AAEzB,QAAI,OAAO,UAAU,SAAS,UAAU;AACtC,YAAM,UAAU;AAAA,IAClB;AAAA,EACF;AACF;AAKO,SAAS,yBACd,UAGA,IACgB;AAChB,SAAO,0BAA0BA,YAAW,QAAQ,CAAC,EAClD,YAAY,2BAA2B,EAAE,CAAC,EAC1C,YAAY,4BAA4B,CAAC;AAC9C;;;ACvDA,SAASC,cAAa,KAA0B;AAC9C,QAAM,oBAAoB,wBAAwB;AAClD,SAAO,IAAI,eAAuB;AAAA,IAChC,MAAM,KAAK,YAA2B;AAV1C;AAWM,YAAM,EAAE,OAAO,KAAK,IAAI,MAAM,IAAI,KAAK;AAEvC,UAAI,MAAM;AACR,mBAAW,MAAM;AACjB;AAAA,MACF;AAEA,YAAM,OAAO,mBAAkB,iBAAM,UAAN,mBAAa,SAAb,YAAqB,EAAE;AACtD,UAAI,CAAC;AAAM;AAGX,UAAI,MAAM,kBAAkB,QAAQ,MAAM,eAAe,SAAS,GAAG;AACnE;AAAA,MACF;AAKA,UAAI,SAAS,UAAU,SAAS,mBAAmB,SAAS,WAAW;AACrE;AAAA,MACF;AAEA,iBAAW,QAAQ,IAAI;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEO,SAAS,kBACd,KACA,WACgB;AAChB,SAAOA,cAAa,GAAG,EACpB,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAC9C;;;ACnBO,SAAS,aACd,KACA,WACgB;AAChB,MAAI,CAAC,IAAI,MAAM;AACb,UAAM,IAAI,MAAM,uBAAuB;AAAA,EACzC;AAEA,MAAI,kBAAkB;AACtB,MAAI;AAEJ,QAAM,oBAAoC,CAAC,MAAc,YAAY;AArCvE;AAsCI,UAAM,EAAE,MAAM,IAAI;AAElB,QAAI,UAAU,iBAAiB;AAC7B,sBAAgB,KAAK,MAAM,IAAI;AAC/B,mDAAW,mBAAX,mCAA4B;AAAA,IAC9B;AAEA,QAAI,UAAU,iBAAiB;AAC7B,YAAM,qBAAqB,KAAK,MAAM,IAAI;AAC1C,yBAAkB,wBAAmB,oBAAnB,YAAsC;AACxD,aAAO,mBAAmB;AAAA,IAC5B;AACA;AAAA,EACF;AAEA,MAAI,EAAE,gBAAgB,GAAG,qBAAqB,IAAI,aAAa,CAAC;AAGhE,yBAAuB;AAAA,IACrB,GAAG;AAAA,IACH,SAAS,gBAAc;AA1D3B;AA2DM,YAAM,wBAA+C;AAAA,QACnD;AAAA,QACA;AAAA,MACF;AACA,mDAAW,YAAX,mCAAqB,YAAY;AAAA,IACnC;AAAA,EACF;AAEA,SAAO,SAAS,KAAK,mBAAmB,oBAAoB,EAAE;AAAA,IAC5D,4BAA4B;AAAA,EAC9B;AACF;;;ACtEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwDO,SAAS,WACd,QAIA,WACA;AACA,SAAO,aAAa,QAAQ,SAAS;AACvC;AASO,SAAS,aACd,QAIA,WACA;AACA,SAAO,OACJ;AAAA,IACC,IAAI,gBAEF;AAAA,MACA,WAAW,OAAO,OAAO,eAAe;AArFhD;AAuFU,YAAI,OAAO,UAAU,UAAU;AAC7B,qBAAW,QAAQ,KAAK;AACxB;AAAA,QACF;AAGA,YAAI,WAAW,OAAO;AAEpB,cAAI,MAAM,UAAU,wBAAwB;AAC1C;AAAA,eACE,WAAM,SAAN,mBAAY;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAGA,8BAAsB,OAAO,UAAU;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH,EACC,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAC9C;AAEO,SAAS,qBACd,QAIA,SAKA;AA3HF;AA4HE,QAAM,aAAa,aAAa,QAAQ,mCAAS,SAAS;AAC1D,QAAM,OAAO,mCAAS;AACtB,QAAM,OAAO,mCAAS;AAEtB,QAAM,iBAAiB,OACnB,aAAa,KAAK,QAAQ,UAAU,IACpC;AAEJ,SAAO,IAAI,SAAS,gBAAgB;AAAA,IAClC,SAAQ,kCAAM,WAAN,YAAgB;AAAA,IACxB,YAAY,6BAAM;AAAA,IAClB,SAAS,uBAAuB,MAAM;AAAA,MACpC,aAAa;AAAA,MACb,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,sBACP,OACA,YACA;AACA,MAAI,OAAO,MAAM,YAAY,UAAU;AACrC,eAAW,QAAQ,MAAM,OAAO;AAAA,EAClC,OAAO;AACL,UAAM,UAA4C,MAAM;AACxD,eAAW,QAAQ,SAAS;AAC1B,UAAI,KAAK,SAAS,QAAQ;AACxB,mBAAW,QAAQ,KAAK,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACF;;;ACnJO,SAAS,gBAAgB,WAAyC;AACvE,QAAM,SAAS,IAAI,gBAAgB;AACnC,QAAM,SAAS,OAAO,SAAS,UAAU;AAEzC,QAAM,OAAO,oBAAI,IAAI;AAErB,QAAM,cAAc,OAAO,GAAU,UAAkB;AACrD,SAAK,OAAO,KAAK;AACjB,UAAM,OAAO;AACb,UAAM,OAAO,MAAM,CAAC;AAAA,EACtB;AAEA,QAAM,cAAc,OAAO,UAAkB;AAC3C,SAAK,IAAI,KAAK;AAAA,EAChB;AAEA,QAAM,YAAY,OAAO,UAAkB;AACzC,SAAK,OAAO,KAAK;AAEjB,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,OAAO;AACb,YAAM,OAAO,MAAM;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ,OAAO,SACZ,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAAA,IAC5C;AAAA,IACA,UAAU;AAAA,MACR,mBAAmB,OAAO,UAAkB;AAC1C,cAAM,OAAO;AACb,cAAM,OAAO,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA,gBAAgB,OAAO,MAAW,UAAoB,UAAkB;AACtE,oBAAY,KAAK;AAAA,MACnB;AAAA,MACA,cAAc,OAAO,SAAc,UAAkB;AACnD,cAAM,UAAU,KAAK;AAAA,MACvB;AAAA,MACA,gBAAgB,OAAO,GAAU,UAAkB;AACjD,cAAM,YAAY,GAAG,KAAK;AAAA,MAC5B;AAAA,MACA,kBAAkB,OAAO,QAAa,SAAc,UAAkB;AACpE,oBAAY,KAAK;AAAA,MACnB;AAAA,MACA,gBAAgB,OAAO,UAAe,UAAkB;AACtD,cAAM,UAAU,KAAK;AAAA,MACvB;AAAA,MACA,kBAAkB,OAAO,GAAU,UAAkB;AACnD,cAAM,YAAY,GAAG,KAAK;AAAA,MAC5B;AAAA,MACA,iBAAiB,OAAO,OAAY,QAAgB,UAAkB;AACpE,oBAAY,KAAK;AAAA,MACnB;AAAA,MACA,eAAe,OAAO,SAAiB,UAAkB;AACvD,cAAM,UAAU,KAAK;AAAA,MACvB;AAAA,MACA,iBAAiB,OAAO,GAAU,UAAkB;AAClD,cAAM,YAAY,GAAG,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACF;;;ACrCA,gBAAgBC,YAAW,QAAoD;AApC/E;AAqCE,mBAAiB,SAAS,QAAQ;AAChC,UAAM,WAAU,iBAAM,QAAQ,CAAC,MAAf,mBAAkB,UAAlB,mBAAyB;AAEzC,QAAI,YAAY,UAAa,YAAY,IAAI;AAC3C;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AACF;AAKO,SAAS,cACd,UACA,WACgB;AAChB,QAAM,SAAS,0BAA0BA,YAAW,QAAQ,CAAC;AAC7D,SAAO,OACJ,YAAY,2BAA2B,SAAS,CAAC,EACjD,YAAY,4BAA4B,CAAC;AAC9C;;;AC3DA;AAAA,EAKE;AAAA,EACA,oBAAAC;AAAA,OACK;AAsQP,SAAS,oBAE+C;AACtD,QAAM,UAAU,YAAY;AAC5B,SAAO,UAAQ,QAAQ,KAAK,MAAM,IAAI,CAA4B;AACpE;AAOA,gBAAgBC,YAAW,QAA8C;AACvE,QAAM,UAAU,YAAY;AAE5B,iBAAe,SAAS,QAAQ;AAG9B,QAAI,yBAAyB,OAAO;AAClC,cAAQ;AAAA,QACN,IAAI,MAAM;AAAA,QACV,SAAS,MAAM,QAAQ,QAAQ;AAAA,QAC/B,QAAS,MAAc;AAAA;AAAA,QACvB,OAAQ,MAAc;AAAA;AAAA,QACtB,SAAS,MAAM,QAAQ,IAAI,YAAO;AArS1C;AAqS8C;AAAA,YACpC,OAAO;AAAA,cACL,UAAS,YAAO,UAAP,mBAAc;AAAA,cACvB,gBAAe,YAAO,UAAP,mBAAc;AAAA,cAC7B,OAAM,YAAO,UAAP,mBAAc;AAAA,cACpB,cAAY,kBAAO,UAAP,mBAAc,cAAd,mBAAyB,WACjC,kBAAO,UAAP,mBAAc,cAAd,mBAAyB,IAAI,CAAC,UAAU,WAAW;AAAA,gBACjD;AAAA,gBACA,IAAI,SAAS;AAAA,gBACb,UAAU,SAAS;AAAA,gBACnB,MAAM,SAAS;AAAA,cACjB,MACA;AAAA,YACN;AAAA,YACA,eAAe,OAAO;AAAA,YACtB,OAAO,OAAO;AAAA,UAChB;AAAA,SAAE;AAAA,MACJ;AAAA,IACF;AAEA,UAAM,OAAO,QAAQ,KAAK;AAE1B,QAAI;AAAM,YAAM;AAAA,EAClB;AACF;AAEA,SAAS,cAE+C;AACtD,QAAM,oBAAoB,wBAAwB;AAClD,MAAI;AACJ,SAAO,UAAQ;AApUjB;AAqUI,QAAI,sBAAsB,IAAI,GAAG;AAC/B,YAAM,SAAQ,UAAK,QAAQ,CAAC,MAAd,mBAAiB;AAC/B,WAAI,WAAM,kBAAN,mBAAqB,MAAM;AAC7B,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,+BAA+B,MAAM,cAAc,IAAI;AAAA,QAClE;AAAA,MACF,YAAW,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,MAAM;AAChD,gCAAwB;AACxB,cAAM,WAAW,MAAM,WAAW,CAAC;AACnC,YAAI,SAAS,UAAU,GAAG;AACxB,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,2BAA2B,SAAS,EAAE,iDAAgD,cAAS,aAAT,mBAAmB,IAAI;AAAA,UACxH;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,gBAAgB,SAAS,EAAE,iDAAgD,cAAS,aAAT,mBAAmB,IAAI;AAAA,UAC7G;AAAA,QACF;AAAA,MACF,YAAW,WAAM,kBAAN,mBAAqB,WAAW;AACzC,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,kBAAiB,WAAM,kBAAN,mBAAqB,SAAS;AAAA,QAC1D;AAAA,MACF,YAAW,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,WAAW;AACrD,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,kBAAiB,uBAAM,eAAN,mBAAmB,OAAnB,mBAAuB,aAAvB,mBAAiC,SAAS;AAAA,QACtE;AAAA,MACF,WACE,4BACC,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,qBAClC,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,SACrC;AACA,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS;AAAA,QACX;AAAA,MACF,WACE,2BACA,UAAK,QAAQ,CAAC,MAAd,mBAAiB,mBAAkB,cACnC;AACA,gCAAwB;AACxB,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAEA,UAAM,OAAO;AAAA,MACX,sBAAsB,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,MAAM,UACjD,KAAK,QAAQ,CAAC,EAAE,MAAM,UACtB,aAAa,IAAI,IACjB,KAAK,QAAQ,CAAC,EAAE,OAChB;AAAA,IACN;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,eAAuB;AAC/C,QAAI,qBAAqB,cACtB,QAAQ,OAAO,MAAM,EACrB,QAAQ,OAAO,KAAK,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK;AAEvB,WAAO,GAAG,kBAAkB;AAAA,EAC9B;AACF;AAEA,IAAM,qCAAqC;AAAA,EACzC;AACF;AAYA,SAAS,sBACP,MAC6B;AAC7B,SACE,aAAa,QACb,KAAK,WACL,KAAK,QAAQ,CAAC,KACd,WAAW,KAAK,QAAQ,CAAC;AAE7B;AAEA,SAAS,aAAa,MAAmD;AACvE,SACE,aAAa,QACb,KAAK,WACL,KAAK,QAAQ,CAAC,KACd,UAAU,KAAK,QAAQ,CAAC;AAE5B;AAKO,SAAS,aACd,KACA,WACgB;AAEhB,QAAM,KAIG;AAET,MAAI;AACJ,MAAI,OAAO,iBAAiB,KAAK;AAC/B,aAAS,0BAA0BA,YAAW,GAAG,CAAC,EAAE;AAAA,MAClD;AAAA,SACE,yBAAI,iCAA+B,yBAAI,2BACnC;AAAA,UACE,GAAG;AAAA,UACH,SAAS;AAAA,QACX,IACA;AAAA,UACE,GAAG;AAAA,QACL;AAAA,MACN;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS;AAAA,MACP;AAAA,MACA,kBAAkB;AAAA,OAClB,yBAAI,iCAA+B,yBAAI,2BACnC;AAAA,QACE,GAAG;AAAA,QACH,SAAS;AAAA,MACX,IACA;AAAA,QACE,GAAG;AAAA,MACL;AAAA,IACN;AAAA,EACF;AAEA,MAAI,OAAO,GAAG,+BAA+B,GAAG,0BAA0B;AACxE,UAAM,0BAA0B,8BAA8B,EAAE;AAChE,WAAO,OAAO,YAAY,uBAAuB;AAAA,EACnD,OAAO;AACL,WAAO,OAAO,YAAY,4BAA4B,CAAC;AAAA,EACzD;AACF;AAEA,SAAS,8BACP,WAGyC;AACzC,QAAM,cAAc,IAAI,YAAY;AACpC,MAAI,eAAe;AACnB,MAAI,qBAAqB;AACzB,MAAI,oCAAoC;AACxC,MAAI,wBAAwB;AAE5B,MAAI,uBACF,UAAU,kCAAkC,KAAK,CAAC;AAEpD,QAAM,SAAS,mBAAmB;AAElC,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,UAAU,OAAO,YAA2B;AAChD,YAAM,UAAU,OAAO,KAAK;AAC5B,2CAAqC;AAErC,YAAM,yBACJ,iBACC,QAAQ,WAAW,mBAAmB,KACrC,QAAQ,WAAW,gBAAgB;AAEvC,UAAI,wBAAwB;AAC1B,gCAAwB;AACxB,8BAAsB;AACtB,uBAAe;AACf;AAAA,MACF;AAGA,UAAI,CAAC,uBAAuB;AAC1B,mBAAW;AAAA,UACT,YAAY,OAAOC,kBAAiB,QAAQ,OAAO,CAAC;AAAA,QACtD;AACA;AAAA,MACF,OAAO;AACL,8BAAsB;AAAA,MACxB;AAAA,IACF;AAAA,IACA,MAAM,MAAM,YAA2B;AACrC,UAAI;AACF,YACE,CAAC,gBACD,0BACC,UAAU,+BACT,UAAU,0BACZ;AACA,kCAAwB;AACxB,gBAAM,UAAU,KAAK,MAAM,kBAAkB;AAE7C,cAAI,0BAA2C;AAAA,YAC7C,GAAG;AAAA,UACL;AAEA,cAAI,mBAMY;AAEhB,cAAI,UAAU,6BAA6B;AAIzC,gBAAI,QAAQ,kBAAkB,QAAW;AACvC,sBAAQ;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAEA,kBAAM,mBAAmB,KAAK;AAAA,cAC5B,QAAQ,cAAc;AAAA,YACxB;AAEA,+BAAmB,MAAM,UAAU;AAAA,cACjC;AAAA,gBACE,MAAM,QAAQ,cAAc;AAAA,gBAC5B,WAAW;AAAA,cACb;AAAA,cACA,YAAU;AAER,0CAA0B;AAAA,kBACxB,GAAG;AAAA,kBACH;AAAA,oBACE,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,eAAe,QAAQ;AAAA,kBACzB;AAAA,kBACA;AAAA,oBACE,MAAM;AAAA,oBACN,MAAM,QAAQ,cAAc;AAAA,oBAC5B,SAAS,KAAK,UAAU,MAAM;AAAA,kBAChC;AAAA,gBACF;AAEA,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,cAAI,UAAU,yBAAyB;AACrC,kBAAM,YAA6B;AAAA,cACjC,OAAO,CAAC;AAAA,YACV;AACA,uBAAWC,SAAQ,QAAQ,YAAY;AACrC,wBAAU,MAAM,KAAK;AAAA,gBACnB,IAAIA,MAAK;AAAA,gBACT,MAAM;AAAA,gBACN,MAAM;AAAA,kBACJ,MAAMA,MAAK,SAAS;AAAA,kBACpB,WAAW,KAAK,MAAMA,MAAK,SAAS,SAAS;AAAA,gBAC/C;AAAA,cACF,CAAC;AAAA,YACH;AACA,gBAAI,gBAAgB;AACpB,gBAAI;AACF,iCAAmB,MAAM,UAAU;AAAA,gBACjC;AAAA,gBACA,YAAU;AACR,sBAAI,QAAQ;AACV,0BAAM,EAAE,cAAc,eAAe,iBAAiB,IACpD;AAEF,8CAA0B;AAAA,sBACxB,GAAG;AAAA;AAAA,sBAEH,GAAI,kBAAkB,IAClB;AAAA,wBACE;AAAA,0BACE,MAAM;AAAA,0BACN,SAAS;AAAA,0BACT,YAAY,QAAQ,WAAW;AAAA,4BAC7B,CAAC,QAAkB;AAAA,8BACjB,IAAI,GAAG;AAAA,8BACP,MAAM;AAAA,8BACN,UAAU;AAAA,gCACR,MAAM,GAAG,SAAS;AAAA;AAAA,gCAElB,WAAW,KAAK;AAAA,kCACd,GAAG,SAAS;AAAA,gCACd;AAAA,8BACF;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,IACA,CAAC;AAAA;AAAA,sBAEL;AAAA,wBACE,MAAM;AAAA,wBACN;AAAA,wBACA,MAAM;AAAA,wBACN,SAAS,KAAK,UAAU,gBAAgB;AAAA,sBAC1C;AAAA,oBACF;AACA;AAAA,kBACF;AAEA,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF,SAAS,GAAG;AACV,sBAAQ,MAAM,0CAA0C,CAAC;AAAA,YAC3D;AAAA,UACF;AAEA,cAAI,CAAC,kBAAkB;AAIrB,uBAAW;AAAA,cACT,YAAY;AAAA,gBACVD;AAAA,kBACE,QAAQ,gBAAgB,kBAAkB;AAAA;AAAA,kBAE1C,KAAK,MAAM,kBAAkB;AAAA,gBAC/B;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF,WAAW,OAAO,qBAAqB,UAAU;AAE/C,uBAAW;AAAA,cACT,YAAY,OAAOA,kBAAiB,QAAQ,gBAAgB,CAAC;AAAA,YAC/D;AACA,gDAAoC;AACpC;AAAA,UACF;AAOA,gBAAM,oBAA2C;AAAA,YAC/C,GAAG;AAAA,YACH,SAAS;AAAA,UACX;AAEA,oBAAU,UAAU;AAEpB,gBAAM,eAAe,aAAa,kBAAkB;AAAA,YAClD,GAAG;AAAA,YACH,CAAC,kCAAkC,GAAG;AAAA,UACxC,CAAgC;AAEhC,gBAAM,SAAS,aAAa,UAAU;AAEtC,iBAAO,MAAM;AACX,kBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,gBAAI,MAAM;AACR;AAAA,YACF;AACA,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,UAAE;AACA,YAAI,UAAU,WAAW,mCAAmC;AAC1D,gBAAM,UAAU,QAAQ,iCAAiC;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC9pBA,eAAsB,gBACpB,KACA,IACA,SAGyB;AArD3B;AAsDE,QAAM,OAAM,SAAI,SAAJ,mBAAU;AAEtB,MAAI,CAAC,KAAK;AACR,QAAI,IAAI;AAAO,YAAM,IAAI,MAAM,IAAI,KAAK;AAAA;AACnC,YAAM,IAAI,MAAM,0CAA0C;AAAA,EACjE;AAEA,QAAM,cAAc,MAAM,MAAM,KAAK;AAAA,IACnC,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,GAAG,mCAAS;AAAA,IACd;AAAA,EACF,CAAC;AAED,SAAO,SAAS,aAAa,QAAW,EAAE,EAAE;AAAA,IAC1C,4BAA4B;AAAA,EAC9B;AACF;;;ACjEO,SAAS,iBACd,KACA,UACA,MACA,MACA;AAZF;AAaE,WAAS,WAAU,kCAAM,WAAN,YAAgB,KAAK;AAAA,IACtC,gBAAgB;AAAA,IAChB,GAAG,6BAAM;AAAA,EACX,CAAC;AAED,MAAI,kBAAkB;AAEtB,MAAI,MAAM;AACR,sBAAkB,aAAa,KAAK,QAAQ,GAAG;AAAA,EACjD;AAEA,QAAM,SAAS,gBAAgB,UAAU;AACzC,WAAS,OAAO;AACd,WAAO,KAAK,EAAE,KAAK,CAAC,EAAE,MAAM,MAAM,MAAsC;AACtE,UAAI,MAAM;AACR,iBAAS,IAAI;AACb;AAAA,MACF;AACA,eAAS,MAAM,KAAK;AACpB,WAAK;AAAA,IACP,CAAC;AAAA,EACH;AACA,OAAK;AACP;;;AC3BO,IAAM,wBAAN,cAAoC,SAAS;AAAA,EAClD,YAAY,KAAqB,MAAqB,MAAmB;AACvE,QAAI,kBAAkB;AAEtB,QAAI,MAAM;AACR,wBAAkB,aAAa,KAAK,QAAQ,GAAG;AAAA,IACjD;AAEA,UAAM,iBAAwB;AAAA,MAC5B,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,SAAS,uBAAuB,MAAM;AAAA,QACpC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;A1DMO,IAAME,cAAa;AAMnB,IAAM,SAAS;", "names": ["attributes", "_a", "embedding", "usage", "embeddings", "_a", "usage", "getErrorMessage", "getErrorMessage", "jsonSchema", "zodSchema", "span", "result", "_a", "doStreamSpan", "error", "tool", "safeParseJSON", "tool", "safeParseJSON", "_a", "span", "tool", "result", "result", "NoSuchToolError", "NoSuchToolError", "tool", "doStreamSpan", "tool", "APICallError", "InvalidArgumentError", "InvalidDataContentError", "InvalidPromptError", "InvalidToolArgumentsError", "NoObjectGeneratedError", "NoSuchToolError", "RetryError", "formatStreamPart", "StreamData", "formatStreamPart", "process", "stream", "_a", "create<PERSON><PERSON><PERSON>", "streamable", "streamable", "create<PERSON><PERSON><PERSON>", "streamable", "formatStreamPart", "streamable", "formatStreamPart", "tool", "generateId"]}