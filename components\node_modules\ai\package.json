{"name": "ai", "version": "3.3.0", "description": "Vercel AI SDK - The AI Toolkit for TypeScript and JavaScript", "license": "Apache-2.0", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "react/dist/**/*", "svelte/dist/**/*", "vue/dist/**/*", "solid/dist/**/*", "prompts/dist/**/*", "rsc/dist/**/*", "google/dist/**/*", "mistral/dist/**/*"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./rsc": {"types": "./rsc/dist/index.d.ts", "react-server": "./rsc/dist/rsc-server.mjs", "import": "./rsc/dist/rsc-client.mjs"}, "./prompts": {"types": "./prompts/dist/index.d.ts", "import": "./prompts/dist/index.mjs", "require": "./prompts/dist/index.js"}, "./react": {"types": "./react/dist/index.d.ts", "react-server": "./react/dist/index.server.mjs", "import": "./react/dist/index.mjs", "require": "./react/dist/index.js"}, "./svelte": {"types": "./svelte/dist/index.d.ts", "import": "./svelte/dist/index.mjs", "require": "./svelte/dist/index.js"}, "./vue": {"types": "./vue/dist/index.d.ts", "import": "./vue/dist/index.mjs", "require": "./vue/dist/index.js"}, "./solid": {"types": "./solid/dist/index.d.ts", "import": "./solid/dist/index.mjs", "require": "./solid/dist/index.js"}}, "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5", "@ai-sdk/react": "0.0.36", "@ai-sdk/solid": "0.0.27", "@ai-sdk/svelte": "0.0.29", "@ai-sdk/ui-utils": "0.0.24", "@ai-sdk/vue": "0.0.28", "@opentelemetry/api": "1.9.0", "eventsource-parser": "1.1.2", "jsondiffpatch": "0.6.0", "json-schema": "0.4.0", "nanoid": "3.3.6", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5"}, "devDependencies": {"@anthropic-ai/sdk": "0.18.0", "@aws-sdk/client-bedrock-runtime": "3.451.0", "@edge-runtime/vm": "^3.2.0", "@google/generative-ai": "0.1.1", "@huggingface/inference": "2.6.4", "@mistralai/mistralai": "0.1.3", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.1", "@types/json-schema": "7.0.15", "@types/node": "^18", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "4.2.0", "cohere-ai": "^7.6.2", "eslint": "^7.32.0", "jsdom": "^24.0.0", "langchain": "0.0.196", "msw": "2.3.1", "openai": "4.52.6", "react-dom": "^18", "react-server-dom-webpack": "18.3.0-canary-eb33bd747-20240312", "sswr": "2.1.0", "tsup": "^7.2.0", "typescript": "5.1.3", "zod": "3.23.8", "@vercel/ai-tsconfig": "0.0.0", "eslint-config-vercel-ai": "0.0.0"}, "peerDependencies": {"openai": "^4.42.0", "react": "^18 || ^19", "zod": "^3.0.0", "sswr": "^2.1.0", "svelte": "^3.0.0 || ^4.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "zod": {"optional": true}, "openai": {"optional": true}, "sswr": {"optional": true}, "svelte": {"optional": true}}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "homepage": "https://sdk.vercel.ai/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai", "nextjs", "svelte", "react", "vue"], "scripts": {"build": "tsup && cat react/dist/index.server.d.ts >> react/dist/index.d.ts", "clean": "rm -rf dist && rm -rf react/dist && rm -rf svelte/dist && rm -rf vue/dist && rm -rf solid/dist && rm -rf rsc/dist", "dev": "tsup --watch", "lint": "eslint \"./**/*.ts*\"", "type-check": "tsc --noEmit", "prettier-check": "prettier --check \"./**/*.ts*\"", "test": "pnpm test:node && pnpm test:edge && pnpm test:ui && pnpm test:e2e", "test:edge": "vitest --config vitest.edge.config.js --run", "test:node": "vitest --config vitest.node.config.js --run", "test:node:core": "pnpm vitest --config vitest.node.config.js --run ./core/", "test:ui": "pnpm test:ui:react", "test:ui:react": "vitest --config vitest.ui.react.config.js --run", "test:e2e": "playwright test", "test:edge:watch": "vitest --config vitest.edge.config.js", "test:node:watch": "vitest --config vitest.node.config.js", "test:ui:react:watch": "vitest --config vitest.ui.react.config.js"}}